apply from: 'buildsystem/build-system.gradle'
//apply plugin: 'com.google.gms.google-services'

buildscript {
    repositories {
        maven {
            //tencent_public
            url 'https://mirrors.tencent.com/nexus/repository/maven-public/'
        }
        maven {
            //thirdparty
            url 'https://mirrors.tencent.com/repository/maven/thirdparty-snapshots/'
        }
        maven {
            //thirdparty-snapshots
            url 'https://mirrors.tencent.com/repository/maven/thirdparty/'
        }



        google()
        jcenter()

        maven {url 'https://developer.huawei.com/repo/'}

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.0.4'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
//        classpath 'com.google.gms:google-services:4.0.1'

        classpath 'com.huawei.agconnect:agcp:1.4.2.300'
    }
}

allprojects {
    repositories {
        maven {
            //tencent_public
            url 'https://mirrors.tencent.com/nexus/repository/maven-public/'
        }
        maven {
            //thirdparty
            url 'https://mirrors.tencent.com/repository/maven/thirdparty-snapshots/'
        }
        maven {
            //thirdparty-snapshots
            url 'https://mirrors.tencent.com/repository/maven/thirdparty/'
        }
        google()

        maven {url 'https://developer.huawei.com/repo/'}
    }
}

task clean(type: Delete) {
    delete file("bin")
    delete buildDir
}


ext {
    //Java Compiler Version
    javaCompileVersion = JavaVersion.VERSION_1_8

    //Android
    androidCompileSdkVersion = 34
    //这里最好是22+，否则在local.properties中配置有ndk.dir时，assemble报错: transformnative_libswithstripdebugsymbolfordebug failed
    androidBuildToolsVersion = '29.0.2'
    androidMinSdkVersion = 29
    androidTargetSdkVersion = 34
    //这里最好是22+，否则在local.properties中配置有ndk.dir时，assemble报错: transformnative_libswithstripdebugsymbolfordebug failed

    //////////////////////////// 版本号大版本(166->167)每次+10， 小版本(166a->166b)每次+1 //////////////////////
    //////////////////////////// 版本号大版本(170->171)每次+1000: 171000， 小版本(170a->170b)每次+10: 171030 //////////////////////
    //Project
    //:CTI-Cores(include http-core/log/core)
    centauriCoreVersionCode = 501061
    centauriCoreVersionName = '5.01.061'
    //最后一位1:gw,2:grn,3:vng,4:toypay,5:huawei，6:samsung 7:xiaomi 8:ea 9:vivo 10:oppo

    //:Channel GoogleWallet
    centauriGoogleWalletVersionCode = 501061
    centauriGoogleWalletVersionName = '5.01.061'


    centauriHwVersionCode = 40526
    centauriHwVersionName = '4.05.26'
    //////////////////////////////////
    midasModuleBuildName = '${moduleName}_${variantName}_${versionName}_${versionCode}_${fileMd5}'
//    ctiCoresPackageName = "centauri-cores-${rootProjectcentauriCoreVersionCode}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.jar"
}


//先获取到所有的渠道projects
def channelProjects = []
def hasTencentProjects = false
getChildProjects().each { name, project ->
    if (name == "Tencent") {
        hasTencentProjects = true
    }
    if (name != "Tencent" && name != "Core") {
        channelProjects << name
    }
}

//构建任务入口
rootProject.ext.buildTypes.each { type ->
    def midasTask = tasks.create(name: "midasAssemble${type.capitalize()}") {
        group "midas package"
        description "assemble all ${type} midas tasks"
        doLast {
            println it
        }
    }

    channelProjects.each {
        midasTask.dependsOn ":${it}:midasChannel${type.capitalize()}"
    }
    midasTask.dependsOn ":Core:midasCore${type.capitalize()}"
    if (hasTencentProjects) {
        midasTask.dependsOn ":Tencent:midasTencent${type.capitalize()}"
    }
}

rootProject.ext.buildTypes.each { type ->
    def zipTask = tasks.create(name: "midasZip${type.capitalize()}", type: Zip) {
        group "midas package"
        description "zip all ${type} build results"

        String buildNo = "${System.env.BuildNo}"
        if (buildNo == null || buildNo.equals("null") || buildNo.length() <= 0) {
            buildNo = "local"
        }
        def bin = project.file("bin")
        if (!bin.exists()) {
            bin.mkdirs()
        }
        def hash = rootProject.ext.gitCommitHash(project, ".")
        def time = rootProject.ext.gitCommitTime(project, ".")
        //zip压缩
        from "${rootProject.ext.getSdkOutputDir(type)}"
        baseName "TencentMidasOversea_build${buildNo}_${type}_${hash}_${time}"
        destinationDir = bin
    }
}

gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        if (project.hasProperty("forCoverity")) {
            options.sourcepath = files('/this/directory/must/not/exists')
        }
    }
}

// rootProject build.gradle中定义全局方法
/**
 * 获取当前的git的commit_id
 * @return
 */
def getGitRevision() {
    return "git rev-parse --short HEAD".execute().text.trim()
}

/**
 * 获取git当前分支,去除"branch/"与"-"
 * @return
 */
def getGitBranch() {
    return 'git symbolic-ref --short -q HEAD'.execute().text.trim().replace("branch/","").replace("_","")
}

/**
 * 获取 build package 时间
 * @return
 */
static def getReleaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getDefault());
}


task ctiClean(type: Delete) {
    doFirst {
        println "==================== cti clean "
    }

    delete file("bin")
    delete buildDir
    delete('Core/build')
    delete('channel/GoogleWallet/build')




}

task ctiPrePackageJar(dependsOn: ctiClean ){
    doLast {
        println("==================== ctiPrePackageJar")
        String rootFolderPath = "${project.rootProject.buildDir.absolutePath}"
        File ctiResFile = project.mkdir("${rootFolderPath}/centauri-pay-sdk/res/")
        File ctiLibsFile = project.mkdir("${rootFolderPath}/centauri-pay-sdk/libs/")
        File ctiAssetsFile = project.mkdir("${rootFolderPath}/centauri-pay-sdk/assets/")
    }
}

task ctiPackageJar(dependsOn:ctiPrePackageJar){
    doFirst {
        println "==================== cti build package jar file"
    }
    dependsOn ":Core:ctiCoresJar"
    dependsOn ":channel:GoogleWallet:ctiChannelGwJar"
}

task ctiCopyPackageJar(type:Copy,dependsOn: ctiPackageJar){
    doFirst {
        println "==================== cti copy package jar file"
    }
    //copy Cores module libs
    from('Core/build/libs')
    from('channel/GoogleWallet/build/libs')
    into('build/centauri-pay-sdk/libs/')
}

task ctiCopyAssetsDir(type:Copy,dependsOn: ctiPackageJar){
    doFirst {
        println "==================== cti copy assets dir file"
    }
    from('Core/src/main/assets/')
    into('build/centauri-pay-sdk/assets/')
}

task ctiCopyResDir(type:Copy,dependsOn: ctiPackageJar){
    doFirst {
        println "==================== cti copy res dir file"
    }
    from('Core/src/main/res')
    into('build/centauri-pay-sdk/res')
}

task ctiCopyCfigfile(type:Copy,dependsOn: ctiPackageJar){
    doFirst {
        println "==================== cti copy config file"
    }
    from('Core/src/main/AndroidManifest.xml')
    from('Core/build.gradle')
    into('build/centauri-pay-sdk')
}

task ctiBuildAndCopy(){

    dependsOn (ctiClean)
    dependsOn(ctiPrePackageJar)
    project.rootProject.logger.info("[midas] [triggerMidasAarPackage] copy aar to 66")
    dependsOn(ctiPackageJar)

    dependsOn(ctiCopyPackageJar)
    dependsOn(ctiCopyAssetsDir)
    project.rootProject.logger.info("[midas] [triggerMidasAarPackage] copy aar to 888")
    dependsOn(ctiCopyResDir)
    dependsOn(ctiCopyCfigfile)

    tasks.findByName('ctiPrePackageJar').mustRunAfter (ctiClean)
    tasks.findByName('ctiPackageJar').mustRunAfter('ctiPrePackageJar')
    tasks.findByName('ctiCopyPackageJar').mustRunAfter('ctiPackageJar')
    tasks.findByName('ctiCopyResDir').mustRunAfter('ctiCopyPackageJar')
    tasks.findByName('ctiCopyCfigfile').mustRunAfter('ctiCopyResDir')
    tasks.findByName('ctiCopyAssetsDir').mustRunAfter('ctiCopyCfigfile')

}


task ctiZipPackage(type:Zip,dependsOn: ctiBuildAndCopy){
    doFirst {
        println "==================== cti zip packaging"
    }

    String zipPackageName ="centauri-pay-sdk-${getReleaseTime()}-${rootProject.ext.centauriCoreVersionName}"
    String rootFolderPath = "${project.rootProject.buildDir.absolutePath}"
    String destinationFolder = "${rootFolderPath}/bin"
    String sourceFolder = "${rootFolderPath}/centauri-pay-sdk"

    from sourceFolder
    baseName zipPackageName
    destinationDir = project.file(destinationFolder)
}