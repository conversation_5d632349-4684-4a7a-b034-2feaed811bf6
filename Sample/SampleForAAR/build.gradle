apply plugin: 'com.android.application'
//apply from: '../buildsystem/channel.gradle'

android {
    compileSdkVersion 30
    buildToolsVersion '30.0.2'

    defaultConfig {
//        applicationId "com.centauri.sample"
//        applicationId "com.tencent.imsdk.samples"
//        applicationId "com.tencent.oversea.sample"
        applicationId "com.mscti.game.plane"
        minSdkVersion 30
        targetSdkVersion 30
        versionCode 2
        versionName "1.1"
    }

    //配置keystore签名
    signingConfigs {
//        release {
//            storeFile file("centauri_sign.keystore")
//            storePassword "123456"
//            keyAlias "centauri"
//            keyPassword "123456"
//        }
//        debug {
//            storeFile file("centauri_sign.keystore")
//            storePassword "123456"
//            keyAlias "centauri"
//            keyPassword "123456"
//        }
        debug {
            storeFile file("keydemo")
            storePassword "123123"
            keyAlias "keydemo111"
            keyPassword "123123"
        }
        release {
            storeFile file("keydemo")
            storePassword "123123"
            keyAlias "keydemo111"
            keyPassword "123123"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
        compileOptions {
            sourceCompatibility 1.8
            targetCompatibility 1.8
        }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation fileTree(dir: 'libs', include: '*.jar')

    implementation project(':Core')
//    implementation project(':channel:SamSung:Lib')
//    implementation project(':channel:huawei:app')
//    implementation project(':channel:huawei:lib')
    implementation project(':channel:GoogleWallet')
//    implementation project(':channel:Netmarble')
//    implementation project(':channel:VNG')
//    implementation project(':channel:Garena')

//    implementation 'com.centauri.comm:centauricommon-http:2.00.01'
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.cardview:cardview:1.0.0'

    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:1.5.4'
    releaseImplementation 'com.squareup.leakcanary:leakcanary-android-no-op:1.5.4'
//    implementation 'com.centauri.comm:centauricommon-log:2.0.3'
    //bugly
//    implementation 'com.tencent.bugly:crashreport:latest.release'
    implementation 'com.google.firebase:firebase-core:16.0.1'
    implementation(name:"debugview-release", ext:"aar")
//    implementation 'com.android.billingclient:billing:6.1.0'
}

//apply plugin: 'com.google.gms.google-services'

gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        if (project.hasProperty("forCoverity")) {
            options.sourcepath = files('/this/directory/must/not/exists')
        }
    }
}
