<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="unipay_country_AL">Albania(AL)</string>
    <string name="unipay_country_DZ">Algeria(DZ)</string>
    <string name="unipay_country_AF">Afghanistan(AF)</string>
    <string name="unipay_country_AR">Argentina(AR)</string>
    <string name="unipay_country_AE">United Arab Emirates(AE)</string>
    <string name="unipay_country_OM">Oman(OM)</string>
    <string name="unipay_country_AZ">Azerbaijan(AZ)</string>
    <string name="unipay_country_AC">Ascension(AC)</string>
    <string name="unipay_country_EG">Egypt(EG)</string>
    <string name="unipay_country_ET">Ethiopia(ET)</string>
    <string name="unipay_country_IE">Ireland(IE)</string>
    <string name="unipay_country_EE">Estonia(EE)</string>
    <string name="unipay_country_AD">Andorra(AD)</string>
    <string name="unipay_country_AO">Angola(AO)</string>
    <string name="unipay_country_AI">Anguilla(AI)</string>
    <string name="unipay_country_AG">Antigua and Barbuda(AG)</string>
    <string name="unipay_country_AT">Austria(AT)</string>
    <string name="unipay_country_AU">Australia(AU)</string>
    <string name="unipay_country_MO">Macao(MO)</string>
    <string name="unipay_country_BB">Barbados(BB)</string>
    <string name="unipay_country_PG">Papua New Cuinea(PG)</string>
    <string name="unipay_country_BS">Bahamas(BS)</string>
    <string name="unipay_country_PK">Pakistan(PK)</string>
    <string name="unipay_country_PY">Paraguay(PY)</string>
    <string name="unipay_country_BH">Bahrain(BH)</string>
    <string name="unipay_country_PA">Panama(PA)</string>
    <string name="unipay_country_BR">Brazil(BR)</string>
    <string name="unipay_country_BY">Belarus(BY)</string>
    <string name="unipay_country_BM">Bermuda Is.(BM)</string>
    <string name="unipay_country_BG">Bulgaria(BG)</string>
    <string name="unipay_country_BJ">Benin(BJ)</string>
    <string name="unipay_country_BE">Belgium(BE)</string>
    <string name="unipay_country_IS">Iceland(IS)</string>
    <string name="unipay_country_PR">Puerto Rico(PR)</string>
    <string name="unipay_country_PL">Poland(PL)</string>
    <string name="unipay_country_BO">Bolivia(BO)</string>
    <string name="unipay_country_BZ">Belize(BZ)</string>
    <string name="unipay_country_BW">Botswana(BW)</string>
    <string name="unipay_country_BF">Burkina-faso(BF)</string>
    <string name="unipay_country_BI">Burundi(BI)</string>
    <string name="unipay_country_KP">North Korea(KP)</string>
    <string name="unipay_country_DK">Denmark(DK)</string>
    <string name="unipay_country_DE">Germany(DE)</string>
    <string name="unipay_country_TG">Togo(TG)</string>
    <string name="unipay_country_DO">Dominica Rep.(DO)</string>
    <string name="unipay_country_RU">Russia(RU)</string>
    <string name="unipay_country_EC">Ecuador(EC)</string>
    <string name="unipay_country_FR">France(FR)</string>
    <string name="unipay_country_PF">French Polynesia(PF)</string>
    <string name="unipay_country_GF">French Guiana(GF)</string>
    <string name="unipay_country_PH">Philippines(PH)</string>
    <string name="unipay_country_FJ">Fiji(FJ)</string>
    <string name="unipay_country_FI">Finland(FI)</string>
    <string name="unipay_country_GM">Gambia(GM)</string>
    <string name="unipay_country_CG">Congo(CG)</string>
    <string name="unipay_country_CO">Colombia(CO)</string>
    <string name="unipay_country_CR">Costa Rica(CR)</string>
    <string name="unipay_country_GD">Grenada(GD)</string>
    <string name="unipay_country_GE">Georgia(GE)</string>
    <string name="unipay_country_CU">Cuba(CU)</string>
    <string name="unipay_country_GU">Guam(GU)</string>
    <string name="unipay_country_GY">Guyana(GY)</string>
    <string name="unipay_country_KZ">Kazakstan(KZ)</string>
    <string name="unipay_country_HT">Haiti(HT)</string>
    <string name="unipay_country_KR">Korea(KR)</string>
    <string name="unipay_country_NL">Netherlands(NL)</string>
    <string name="unipay_country_AN">Netheriands Antilles(AN)</string>
    <string name="unipay_country_HN">Honduras(HN)</string>
    <string name="unipay_country_DJ">Djibouti(DJ)</string>
    <string name="unipay_country_KG">Kyrgyzstan(KG)</string>
    <string name="unipay_country_GN">Guinea(GN)</string>
    <string name="unipay_country_CA">Canada(CA)</string>
    <string name="unipay_country_GH">Ghana(GH)</string>
    <string name="unipay_country_GA">Gabon(GA)</string>
    <string name="unipay_country_KH">Kampuchea (Cambodia )(KH)</string>
    <string name="unipay_country_CZ">Czech Republic(CZ)</string>
    <string name="unipay_country_ZW">Zimbabwe(ZW)</string>
    <string name="unipay_country_CM">Cameroon(CM)</string>
    <string name="unipay_country_QA">Qatar(QA)</string>
    <string name="unipay_country_KY">Cayman Is.(KY)</string>
    <string name="unipay_country_KT">Ivory Coast(KT)</string>
    <string name="unipay_country_KW">Kuwait(KW)</string>
    <string name="unipay_country_KE">Kenya(KE)</string>
    <string name="unipay_country_CK">Cook Is.(CK)</string>
    <string name="unipay_country_LV">Latvia(LV)</string>
    <string name="unipay_country_LS">Lesotho(LS)</string>
    <string name="unipay_country_LA">Laos(LA)</string>
    <string name="unipay_country_LB">Lebanon(LB)</string>
    <string name="unipay_country_LT">Lithuania(LT)</string>
    <string name="unipay_country_LR">Liberia(LR)</string>
    <string name="unipay_country_LY">Libya(LY)</string>
    <string name="unipay_country_LI">Liechtenstein(LI)</string>
    <string name="unipay_country_RE">Reunion(RE)</string>
    <string name="unipay_country_LU">Luxembourg(LU)</string>
    <string name="unipay_country_RO">Romania(RO)</string>
    <string name="unipay_country_MG">Madagascar(MG)</string>
    <string name="unipay_country_MV">Maldives(MV)</string>
    <string name="unipay_country_MT">Malta(MT)</string>
    <string name="unipay_country_MW">Malawi(MW)</string>
    <string name="unipay_country_MY">Malaysia(MY)</string>
    <string name="unipay_country_ML">Mali(ML)</string>
    <string name="unipay_country_MP">Mariana Is(MP)</string>
    <string name="unipay_country_MQ">Martinique(MQ)</string>
    <string name="unipay_country_MU">Mauritius(MU)</string>
    <string name="unipay_country_US">United States of America(US)</string>
    <string name="unipay_country_MN">Mongolia(MN)</string>
    <string name="unipay_country_MS">Montserrat Is(MS)</string>
    <string name="unipay_country_BD">Bangladesh(BD)</string>
    <string name="unipay_country_PE">Peru(PE)</string>
    <string name="unipay_country_MM">Burma(MM)</string>
    <string name="unipay_country_MD">Moldova Republic of(MD)</string>
    <string name="unipay_country_MA">Morocco(MA)</string>
    <string name="unipay_country_MC">Monaco(MC)</string>
    <string name="unipay_country_MZ">Mozambique(MZ)</string>
    <string name="unipay_country_MX">Mexico(MX)</string>
    <string name="unipay_country_NA">Namibia(NA)</string>
    <string name="unipay_country_ZA">South Africa(ZA)</string>
    <string name="unipay_country_YU">Yugoslavia(YU)</string>
    <string name="unipay_country_NR">Nauru(NR)</string>
    <string name="unipay_country_NP">Nepal(NP)</string>
    <string name="unipay_country_NI">Nicaragua(NI)</string>
    <string name="unipay_country_NE">Niger(NE)</string>
    <string name="unipay_country_NG">Nigeria(NG)</string>
    <string name="unipay_country_NO">Norway(NO)</string>
    <string name="unipay_country_PT">Portugal(PT)</string>
    <string name="unipay_country_JP">Japan(JP)</string>
    <string name="unipay_country_SE">Sweden(SE)</string>
    <string name="unipay_country_CH">Switzerland(CH)</string>
    <string name="unipay_country_SV">EI Salvador(SV)</string>
    <string name="unipay_country_SL">Sierra Leone(SL)</string>
    <string name="unipay_country_SN">Senegal(SN)</string>
    <string name="unipay_country_CY">Cyprus(CY)</string>
    <string name="unipay_country_SC">Seychelles(SC)</string>
    <string name="unipay_country_SA">Saudi Arabia(SA)</string>
    <string name="unipay_country_ST">Sao Tome and Principe(ST)</string>
    <string name="unipay_country_LC">Saint Lueia(LC)</string>
    <string name="unipay_country_SM">San Marino(SM)</string>
    <string name="unipay_country_VC">Saint Vincent(VC)</string>
    <string name="unipay_country_LK">Sri Lanka(LK)</string>
    <string name="unipay_country_SK">Slovakia(SK)</string>
    <string name="unipay_country_SI">Slovenia(SI)</string>
    <string name="unipay_country_SZ">Swaziland(SZ)</string>
    <string name="unipay_country_SD">Sudan(SD)</string>
    <string name="unipay_country_SR">Suriname(SR)</string>
    <string name="unipay_country_SB">Solomon Is(SB)</string>
    <string name="unipay_country_SO">Somali(SO)</string>
    <string name="unipay_country_TJ">Tajikstan(TJ)</string>
    <string name="unipay_country_TW">Taiwan(TW)</string>
    <string name="unipay_country_TH">Thailand(TH)</string>
    <string name="unipay_country_TZ">Tanzania(TZ)</string>
    <string name="unipay_country_TO">Tonga(TO)</string>
    <string name="unipay_country_TT">Trinidad and Tobago(TT)</string>
    <string name="unipay_country_TN">Tunisia(TN)</string>
    <string name="unipay_country_TR">Turkey(TR)</string>
    <string name="unipay_country_TM">Turkmenistan(TM)</string>
    <string name="unipay_country_GT">Guatemala(GT)</string>
    <string name="unipay_country_VE">Venezuela(VE)</string>
    <string name="unipay_country_BN">Brunei(BN)</string>
    <string name="unipay_country_UG">Uganda(UG)</string>
    <string name="unipay_country_UA">Ukraine(UA)</string>
    <string name="unipay_country_UY">Uruguay(UY)</string>
    <string name="unipay_country_UZ">Uzbekistan(UZ)</string>
    <string name="unipay_country_ES">Spain(ES)</string>
    <string name="unipay_country_WS">Samoa Western(WS)</string>
    <string name="unipay_country_GR">Greece(GR)</string>
    <string name="unipay_country_HK">Hongkong(HK)</string>
    <string name="unipay_country_SG">Singapore(SG)</string>
    <string name="unipay_country_NZ">New Zealand(NZ)</string>
    <string name="unipay_country_HU">Hungary(HU)</string>
    <string name="unipay_country_SY">Syria(SY)</string>
    <string name="unipay_country_JM">Jamaica(JM)</string>
    <string name="unipay_country_AM">Armenia(AM)</string>
    <string name="unipay_country_YE">Yemen(YE)</string>
    <string name="unipay_country_IQ">Iraq(IQ)</string>
    <string name="unipay_country_IR">Iran(IR)</string>
    <string name="unipay_country_IL">Israel(IL)</string>
    <string name="unipay_country_IT">Italy(IT)</string>
    <string name="unipay_country_IN">India(IN)</string>
    <string name="unipay_country_ID">Indonesia(ID)</string>
    <string name="unipay_country_GB">United Kiongdom(GB)</string>
    <string name="unipay_country_JO">Jordan(JO)</string>
    <string name="unipay_country_VN">Vietnam(VN)</string>
    <string name="unipay_country_ZM">Zambia(ZM)</string>
    <string name="unipay_country_ZR">Zaire(ZR)</string>
    <string name="unipay_country_TD">Chad(TD)</string>
    <string name="unipay_country_GI">Gibraltar(GI)</string>
    <string name="unipay_country_CL">Chile(CL)</string>
    <string name="unipay_country_CF">Central African Republic(CF)</string>
    <string name="unipay_country_CN">China(CN)</string>
    <string name="unipay_country_PS">Palestine(PS)</string>
    <string name="unipay_country_BA">Bosnia and Herzegovina(BA)</string>
    <string name="unipay_country_BT">Bhutan(BT)</string>
    <string name="unipay_country_GQ">Equatorial Guinea(GQ)</string>
    <string name="unipay_country_DM">Dominica(DM)</string>
    <string name="unipay_country_ER">eritrea(ER)</string>
    <string name="unipay_country_VA">Vatican City(VA)</string>
    <string name="unipay_country_CV">CApe Verde Is(CV)</string>
    <string name="unipay_country_KI">Kiribati(KI)</string>
    <string name="unipay_country_GW">Guinea-Bissau(GW)</string>
    <string name="unipay_country_KM">Comoros(KM)</string>
    <string name="unipay_country_HR">Croatia(HR)</string>
    <string name="unipay_country_RW">Rwanda(RW)</string>
    <string name="unipay_country_MK">Macedonia(MK)</string>
    <string name="unipay_country_MH">Marshall Island (MH)</string>
    <string name="unipay_country_MR">Mauritania(MR)</string>
    <string name="unipay_country_FM">Micronesia(FM)</string>
    <string name="unipay_country_PW">Palau(PW)</string>
    <string name="unipay_country_TV">Tuvalu(TV)</string>
    <string name="unipay_country_VU">Vanuatu(VU)</string>
    <string name="unipay_country_CI">Ivory Coast(CI)</string>
</resources>
