<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="unipay_abroad_warnpic">warnpic</string>
 	<string name="unipay_abroad_iconsuc">iconsuc</string>
    <string name="unipay_abroad_iconwarn">iconwarn</string>
    <string name="unipay_cancel">Cancel</string>
    <string name="unipay_sure">Confirm</string>
 	<string name="unipay_hints">Reminder</string> 
 	<string name="unipay_dlg_tip">Friendly Reminder</string> 
	<string name="unipay_buy">Purchase</string> 
	<string name="unipay_order_sanbox_tip">Entering the Sandbox Payment System\nVersion:</string> 
	<string name="unipay_key_time_error">Your current system time is incorrect, please reset it.</string> 
	<string name="unipay_order_release_tip">We are entering a secure payment environment.</string> 
	<string name="unipay_waiting">Please wait a moment&#8230;</string>
	<string name="unipay_pay_error_tip">Payment Error, please try again later.</string>
	<string name="unipay_pay_no_goods">Product list error</string>
	<string name="unipay_no_charge_hints">You are now entering the game testing mode, any purchase here cannot apply to the actual game mode. Please do not purchase any items if you are not the game tester(s). Thank you.</string>
	<string name="unipay_network_error_1">Network error (error code)</string>
	<string name="unipay_network_error_2">Network connection timed out, please check the network.</string>
	<string name="unipay_network_error_3">Network response time-out, please check the network.</string>
	<string name="unipay_network_error_4">Network connection error, please check the network.</string>
	<string name="unipay_network_error_5">Network error, please try again later.</string>
	<string name="unipay_no_sim">No SIM Card detected</string>
	<string name="unipay_pay_unkwon">Failed to access to Payment Information, please check your account.</string>
	<string name="unipay_pay_buy">Purchase</string>
	<string name="unipay_pay_busy">System busy,please try again later.</string>
	<string name="unipay_clear">Reset Purchase</string>
	<string name="unipay_debug">debug</string>
	<string name="unipay_clear_hind">When the shipment fails, indicating that the item cannot be purchased, the reset ticket can be used to clean up</string>
	<string name="unipay_jlz_query_productinfo_error">Query ProductInfo error.</string>
	<string name="unipay_pay_fortumo_tip">A SIM card is required to complete the payment .Please make sure your SIM card is inserted properly and continue.</string>
	<string name="unipay_pay_send_explain">Buy %1$s and get extra %2$s diamonds</string>
	<string name="unipay_pay_send_once">ONCE ONLY</string>
	<string name="unipay_error">Error</string>
	<string name="unipay_try_again">Please try again later.</string>
	<string name="unipay_payment_successful">Payment successful</string>
	<string name="unipay_error_network_not_connected">your are not connected to the network,please connect and try again.</string>
	<string name="unipay_tip_setting">go to setting</string>

	<string name="unipay_error_sub_owned_sameuser">You have owned the subscription.</string>
	<string name="unipay_error_sub_owned_diffuser">Your another account has owned the subscription.</string>
	<string name="unipay_error_not_installed">Sorry, you haven\'t installed the app</string>

	<!--google play支付失败错误文案-->
	<string name="unipay_gw_pay_error_account">\nPlease make sure you are logged into your Google Play App Store account, and that you are able to make purchases through this account.</string>>
	<string name="unipay_gw_pay_error_device">\nPlease make sure Google Play is up to date on your device.</string>
	<string name="unipay_gw_pay_error_item_owned">\nYou already own this item. Please restart the app and check.</string>
	<string name="unipay_gw_pay_error_null_data">\nGoogle Play Store is blocked from creating pop-up windows. Please check.</string>

	<!--这个提示业务同意使用英文-->
	<string name="unipay_error_sub_upgrade_purchase_null">\nSubscription old purchase token is null.</string>

	<string name="ike">mike</string>
	<string name="Ike">Mike</string>
	<string name="ce">ice</string>
	<string name="elta">delta</string>
	<string name="lpha">alpha</string>
	<string name="ierra">sierra</string>
	<string name="ravo">bravo</string>
	<string name="niform">uniform</string>
	<string name="olf">golf</string>
	<string name="ima">lima</string>
	<string name="ankee">yankee</string>
	<string name="ot">.</string>
	<string name="harlie">charlie</string>
	<string name="scar">oscar</string>

</resources>
