<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <!--<declare-styleable name="StrokeTextView">-->
        <!--<attr name="stroke_width" format="dimension"/>-->
    <!--</declare-styleable>-->

    <!--
        Base application theme, dependent on API level. This theme is replaced
        by AppBaseTheme from res/values-vXX/styles.xml on newer devices.
    -->
    <style name="AppBaseTheme" parent="android:Theme.Light">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.
        -->
    </style>

    <!-- Application theme. -->
    <!--<style name="AppTheme" parent="AppBaseTheme">-->
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
    <!--</style>-->


    <!-- line -->
    <!--<style name="unipay_abroad_line">-->
        <!--<item name="android:background">#464646</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_line.horizontal">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">1px</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_line.vertical">-->
        <!--<item name="android:layout_width">1px</item>-->
        <!--<item name="android:layout_height">match_parent</item>-->
    <!--</style>-->

    <style name="unipay_abroad_fill">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="unipay_abroad_fill.bg">
        <item name="android:background">@color/unipay_abroad_trans</item>
    </style>

    <style name="unipay_abroad_wrap">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="unipay_abroad_linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="unipay_abroad_linear_ver">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">vertical</item>
    </style>

    <!--<style name="unipay_abroad_itembtn">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:button">@null</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_btnbg</item>-->
        <!--<item name="android:layout_marginLeft">15dp</item>-->
        <!--<item name="android:layout_marginRight">15dp</item>-->
        <!--<item name="android:textColor">#fff</item>-->
        <!--<item name="android:textSize">12sp</item>-->

    <!--</style>-->

    <!--<style name="unipay_abroad_itemtext">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:shadowDx">1</item>-->
        <!--<item name="android:shadowDy">1</item>-->
        <!--<item name="android:shadowRadius">2</item>-->
        <!--<item name="android:shadowColor">#4a4744</item>-->
        <!--<item name="android:gravity">center</item>-->
        <!--<item name="android:textColor">#fdfdfd</item>-->
        <!--<item name="android:textSize">13sp</item>-->

    <!--</style>-->

    <!--<style name="unipay_abroad_itembg">-->
        <!--<item name="android:layout_width">0dp</item>-->
        <!--<item name="android:layout_weight">100</item>-->
        <!--<item name="android:layout_height">101dp</item>-->
        <!--<item name="android:layout_marginLeft">8dp</item>-->
        <!--<item name="android:layout_marginRight">8dp</item>-->
        <!--<item name="android:orientation">vertical</item>-->
        <!--<item name="android:paddingTop">10dp</item>-->
        <!--<item name="android:paddingBottom">10dp</item>-->
        <!--<item name="android:gravity">center_horizontal</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_drawable_itembg</item>-->

    <!--</style>-->

    <!--<style name="unipay_abroad_itembg.port">-->
        <!--<item name="android:layout_marginLeft">5dp</item>-->
        <!--<item name="android:layout_marginRight">5dp</item>-->

    <!--</style>-->

    <style name="unipay_abroad_topiccn">
        <item name="android:layout_width">44dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:button">@null</item>
        <item name="android:background">@null</item>

    </style>


    <style name="unipay_abroad_h1">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#fff</item>

    </style>

    <!--<style name="unipay_abroad_container">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">match_parent</item>-->
        <!--<item name="android:layout_margin">10dp</item>-->
        <!--<item name="android:layout_marginRight">0dp</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_container_bg_xml</item>-->
        <!--<item name="android:orientation">vertical</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_navbar_container">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:gravity">top</item>-->
        <!--<item name="android:singleLine">true</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_navitem">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">54dp</item>-->
        <!--<item name="android:paddingLeft">12dp</item>-->
        <!--<item name="android:paddingRight">12dp</item>-->
        <!--<item name="android:gravity">center</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_navitem.active">-->
        <!--&lt;!&ndash;<item name="android:background">#525252</item>&ndash;&gt;-->
        <!--&lt;!&ndash;<item name="android:background">@drawable/unipay_abroad_navbg_selected_v2</item>&ndash;&gt;-->
    <!--</style>-->
    <!--<style name="unipay_abroad_navitem_logo">-->
    <!--<item name="android:layout_width">wrap_content</item>-->
    <!--<item name="android:layout_height">wrap_content</item>-->
    <!--<item name="android:layout_marginLeft">5dp</item>-->
    <!--<item name="android:layout_marginRight">5dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_navitem.port">-->
        <!--<item name="android:orientation">vertical</item>-->
        <!--<item name="android:layout_height">50dp</item>-->
        <!--<item name="android:gravity">center</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_iconqd">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:layout_marginRight">5dp</item>-->
        <!--<item name="android:alpha">0.5</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_iconqd.active">-->
        <!--<item name="android:alpha">1</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_textqd">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:gravity">center_vertical</item>-->
        <!--&lt;!&ndash;<item name="android:textColor">#888</item>&ndash;&gt;-->
        <!--<item name="android:textColor">#ffffff</item>-->
        <!--<item name="android:textSize">15sp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_textqd.active">-->
        <!--<item name="android:textColor">#fff</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_select">-->
        <!--<item name="android:layout_width">108dp</item>-->
        <!--<item name="android:layout_height">30dp</item>-->
        <!--<item name="android:paddingLeft">5dp</item>-->
        <!--<item name="android:paddingRight">5dp</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_selectbg_xml</item>-->
        <!--<item name="android:gravity">center_vertical</item>-->

    <!--</style>-->

    <!--<style name="unipay_abroad_currency_wrapper">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:paddingLeft">7dp</item>-->
        <!--<item name="android:paddingRight">10dp</item>-->
        <!--<item name="android:paddingTop">6dp</item>-->
        <!--<item name="android:orientation">horizontal</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_currency_wrapper.land">-->

    <!--</style>-->

    <!--<style name="unipay_abroad_currency_dropdown">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_selectbg_xml</item>-->
        <!--<item name="android:gravity">center_vertical</item>-->
        <!--<item name="android:orientation">vertical</item>-->
        <!--<item name="android:layout_marginTop">3dp</item>-->
        <!--<item name="android:layout_marginLeft">12dp</item>-->
        <!--<item name="android:layout_marginRight">12dp</item>-->
        <!--<item name="android:paddingLeft">0dp</item>-->
        <!--<item name="android:paddingRight">0dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_currency_dropdown.land">-->
        <!--<item name="android:layout_width">310dp</item>-->
        <!--<item name="android:layout_marginLeft">10dp</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_selectbg_xml</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_iconflag">-->
        <!--<item name="android:layout_width">19dp</item>-->
        <!--<item name="android:layout_height">13dp</item>-->
        <!--<item name="android:layout_marginRight">8dp</item>-->
    <!--</style>-->

    <style name="unipay_iconwarn">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:contentDescription">@string/unipay_abroad_warnpic</item>
        <item name="android:background">@drawable/unipay_abroad_iconwarn</item>
    </style>

    <!--<style name="unipay_abroad_navbg_arrow">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:src">@drawable/unipay_abroad_navbg_arrow</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_textcountry">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:layout_weight">100</item>-->
        <!--<item name="android:textColor">#fff</item>-->
        <!--<item name="android:textSize">11sp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_icondrop">-->
        <!--<item name="android:layout_width">10dp</item>-->
        <!--<item name="android:layout_height">5dp</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_iconselect</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_iconitem">-->
        <!--<item name="android:layout_width">48dp</item>-->
        <!--<item name="android:layout_height">48dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_selectinner">-->
        <!--<item name="android:layout_width">10dp</item>-->
        <!--<item name="android:layout_weight">1</item>-->
        <!--<item name="android:layout_height">35dp</item>-->
        <!--<item name="android:gravity">center_vertical</item>-->
        <!--<item name="android:paddingLeft">25dp</item>-->
        <!--&lt;!&ndash;<item name="android:background">#535353</item>&ndash;&gt;-->
    <!--</style>-->

    <!--<style name="unipay_abroad_selectinner.land">-->

        <!--<item name="android:layout_width">108dp</item>-->
        <!--<item name="android:layout_weight">0</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_selectinner.checked">-->
        <!--<item name="android:background">@color/checked_background_color</item>-->
    <!--</style>-->

    <style name="unipay_toast">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/unipay_abroad_toastbg</item>
        <item name="android:paddingLeft">5dp</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:paddingBottom">5dp</item>
    </style>

    <!--<style name="unipay_abroad_top_up_price_container">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">match_parent</item>-->
        <!--<item name="android:orientation">vertical</item>-->
        <!--<item name="android:paddingBottom">15dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_text_title">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:gravity">center</item>-->
        <!--<item name="android:paddingTop">10dp</item>-->
        <!--<item name="android:paddingBottom">10dp</item>-->
        <!--<item name="android:textSize">16sp</item>-->
        <!--<item name="android:textColor">#fff</item>-->
    <!--</style>-->
    <!--<style name="unipay_abroad_top_up_price_title">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:gravity">center</item>-->
        <!--<item name="android:paddingTop">10dp</item>-->
        <!--<item name="android:paddingBottom">10dp</item>-->
        <!--<item name="android:textSize">16sp</item>-->
        <!--<item name="android:textColor">#fff</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_top_up_price_txt">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:textSize">15sp</item>-->
        <!--<item name="android:textColor">@color/unipay_abroad_thin1</item>-->
        <!--<item name="android:layout_marginBottom">10dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_top_up_price_txt.land">-->
        <!--<item name="android:paddingLeft">25dp</item>-->
        <!--<item name="android:layout_marginBottom">10dp</item>-->
        <!--<item name="android:layout_weight">1</item>-->
    <!--</style>-->

    <style name="unipay_abroad_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/unipay_abroad_thin1</item>
    </style>

    <!--<style name="unipay_abroad_text.top_up_txt">-->
        <!--<item name="android:layout_width">50dp</item>-->
        <!--<item name="android:paddingLeft">25dp</item>-->
        <!--<item name="android:layout_weight">1</item>-->
    <!--</style>-->

    <style name="unipay_abroad_text.middle">
        <item name="android:textSize">14sp</item>
    </style>

    <!--<style name="unipay_abroad_text.small">-->
        <!--<item name="android:textSize">9sp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_text.sub">-->
        <!--<item name="android:textSize">9sp</item>-->
        <!--<item name="android:textColor">#6f6f6f</item>-->
        <!--<item name="android:layout_gravity">center_horizontal</item>-->
        <!--<item name="android:layout_marginTop">5dp</item>-->
    <!--</style>-->

    <style name="unipay_abroad_text.em">
        <item name="android:textColor">@color/unipay_abroad_em1</item>
    </style>


    <style name="unipay_abroad_result_txt">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/unipay_abroad_em1</item>
        <item name="android:layout_marginBottom">10dp</item>
    </style>

    <style name="unipay_abroad_result_txt.sub">
        <item name="android:textSize">10sp</item>
    </style>

    <style name="unipay_abroad_result_txt.sub.em">
        <item name="android:textColor">@color/unipay_abroad_em2</item>
    </style>

    <style name="unipay_abroad_text.em2">
        <item name="android:textColor">@color/unipay_abroad_em2</item>
    </style>

    <style name="unipay_dialog">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/unipay_abroad_tipsbg</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingTop">40dp</item>
        <item name="android:paddingBottom">32dp</item>
        <item name="android:paddingLeft">26dp</item>
        <item name="android:paddingRight">26dp</item>
        <item name="android:layout_marginLeft">20dp</item>
        <item name="android:layout_marginRight">20dp</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="unipay_dialog.land">
        <item name="android:layout_marginLeft">100dp</item>
        <item name="android:layout_marginRight">100dp</item>
    </style>

    <style name="unipay_abroad_wrap.iconsuc">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:src">@drawable/unipay_abroad_iconsuc</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:contentDescription">@string/unipay_abroad_iconsuc</item>
        <item name="android:layout_marginBottom">15dp</item>
    </style>

    <style name="unipay_abroad_wrap.iconfail">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:src">@drawable/unipay_abroad_iconwarn</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:contentDescription">@string/unipay_abroad_iconsuc</item>
        <item name="android:layout_marginBottom">15dp</item>
    </style>

    <!--<style name="unipay_abroad_wrap.iconwarn">-->
        <!--<item name="android:src">@drawable/unipay_abroad_iconwarn</item>-->
        <!--<item name="android:layout_gravity">center</item>-->
        <!--<item name="android:contentDescription">@string/unipay_abroad_iconsuc</item>-->
        <!--<item name="android:layout_marginBottom">15dp</item>-->
    <!--</style>-->
    <!--<style name="unipay_abroad_wrap.iconloading">-->
        <!--<item name="android:layout_width">44dp</item>-->
        <!--<item name="android:layout_height">44dp</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_iconloading</item>-->
    <!--</style>-->
    <!-- wrap star -->
    <style name="unipay_abroad_head">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:background">@drawable/unipay_abroad_drawable_topbg</item>
    </style>

    <!--<style name="unipay_abroad_main">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">match_parent</item>-->
        <!--<item name="android:layout_weight">100</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_bg</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_main.exchange_rate_default">-->
    <!--</style>-->

    <!--<style name="unipay_abroad_main.exchange_rate_port">-->
        <!--<item name="android:paddingLeft">36dp</item>-->
        <!--<item name="android:paddingRight">36dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_main.exchange_rate_land">-->
        <!--<item name="android:paddingLeft">95dp</item>-->
        <!--<item name="android:paddingRight">95dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_side">-->
        <!--<item name="android:layout_width">140dp</item>-->
        <!--<item name="android:layout_height">match_parent</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_navbg</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_side.port">-->
        <!--<item name="android:layout_width">82dp</item>-->
    <!--</style>-->
    <!-- wrap end -->
    <style name="unipay_abroad_embtn">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/unipay_abroad_drawable_embtn</item>
        <item name="android:textColor">@color/unipay_abroad_thin1</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="unipay_abroad_btn_wrap">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingTop">20dp</item>
    </style>

    <style name="unipay_abroad_btn_cancel_style">
        <item name="android:layout_width">10dp</item>
        <item name="android:layout_height">45dp</item>
        <item name="android:padding">0dp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">#fff</item>
        <item name="android:background">@drawable/unipay_abroad_drawable_btn</item>
    </style>

    <style name="unipay_abroad_btn_ok_style">
        <item name="android:layout_width">10dp</item>
        <item name="android:layout_height">45dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">#fff</item>
        <item name="android:background">@drawable/unipay_abroad_drawable_embtn1</item>
    </style>
    <!--小黄条-->
    <!--<style name="unipay_abroad_ad_area">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">30dp</item>-->
        <!--<item name="android:gravity">center_vertical</item>-->
        <!--<item name="android:paddingLeft">7dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_ad_icon">-->
        <!--<item name="android:layout_width">16dp</item>-->
        <!--<item name="android:layout_height">15dp</item>-->
        <!--<item name="android:layout_marginRight">8dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_ad_txt">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:textSize">13sp</item>-->
        <!--<item name="android:textColor">#9a9a9a</item>-->
        <!--<item name="android:singleLine">true</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_ad_arrow">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:layout_marginLeft">12dp</item>-->
        <!--<item name="android:src">@drawable/unipay_abroad_ad_arrow</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_channel_price_list">-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:orientation">vertical</item>-->
        <!--<item name="android:paddingBottom">10dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_channel_price_list.land">-->
        <!--<item name="android:layout_height">match_parent</item>-->
        <!--<item name="android:orientation">horizontal</item>-->
        <!--<item name="android:paddingBottom">10dp</item>-->
        <!--<item name="android:paddingLeft">10dp</item>-->
        <!--<item name="android:paddingTop">10dp</item>-->
        <!--<item name="android:layout_marginRight">8dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item">-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:weightSum">1</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_drawable_itembg</item>-->
        <!--<item name="android:layout_marginTop">10dp</item>-->
        <!--&lt;!&ndash;<item name="android:padding">15dp</item>&ndash;&gt;-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item.land">-->
        <!--<item name="android:orientation">vertical</item>-->
        <!--<item name="android:layout_width">120dp</item>-->
        <!--<item name="android:layout_height">match_parent</item>-->
        <!--<item name="android:layout_marginTop">0dp</item>-->
        <!--<item name="android:layout_marginRight">8dp</item>-->
        <!--<item name="android:paddingBottom">10dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item.port">-->
        <!--<item name="android:orientation">horizontal</item>-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:padding">10dp</item>-->
        <!--<item name="android:layout_marginRight">12dp</item>-->
        <!--<item name="android:layout_marginLeft">12dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_img">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_img.port">-->
        <!--<item name="android:layout_marginRight">10dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_img.land"></style>-->

    <!--<style name="unipay_abroad_itembg.port2">-->
        <!--<item name="android:layout_marginLeft">10dp</item>-->
        <!--<item name="android:layout_marginRight">10dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_iconitem_v2">-->
        <!--<item name="android:layout_width">38dp</item>-->
        <!--<item name="android:layout_height">34dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_btn">-->
        <!--<item name="android:layout_height">26dp</item>-->
        <!--<item name="android:textColor">#ffffff</item>-->
        <!--<item name="android:textSize">14sp</item>-->
        <!--<item name="android:layout_gravity">center_vertical</item>-->
        <!--<item name="android:background">@drawable/unipay_abroad_price_item_btnbg</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_btn.port">-->
        <!--<item name="android:minWidth">80dp</item>-->
        <!--<item name="android:layout_width">wrap_content</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_btn.land">-->
        <!--<item name="android:layout_width">90dp</item>-->
        <!--<item name="android:layout_marginTop">5dp</item>-->
        <!--<item name="android:layout_gravity">center_horizontal</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_tip_text">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:textColor">#fff</item>-->
        <!--<item name="android:layout_marginEnd">5dp</item>-->
        <!--<item name="android:paddingLeft">4dp</item>-->
        <!--<item name="android:paddingRight">4dp</item>-->
        <!--<item name="android:paddingTop">1dp</item>-->
        <!--<item name="android:paddingBottom">1dp</item>-->
        <!--<item name="android:textSize">10sp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_tip_text.orange">-->
        <!--<item name="android:background">@drawable/unipay_abroad_price_item_text_orange_bg</item>-->
        <!--<item name="android:layout_marginStart">5dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_tags_container">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:orientation">horizontal</item>-->
    <!--</style>-->
    <!--<style name="unipay_abroad_tags_container.port">-->
        <!--<item name="android:layout_gravity">bottom</item>-->
        <!--<item name="android:layout_marginLeft">6dp</item>-->
        <!--<item name="android:layout_marginBottom">3dp</item>-->
    <!--</style>-->
    <!--<style name="unipay_abroad_tip_text.orange.land">-->
        <!--<item name="android:textSize">9sp</item>-->
        <!--<item name="android:layout_marginStart">0dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_tip_text.yellow">-->
        <!--<item name="android:background">@drawable/unipay_abroad_price_item_text_yellow_bg</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_tip_text.yellow.land">-->
        <!--<item name="android:textSize">9sp</item>-->
    <!--</style>-->
    <!--<style name="unipay_abroad_price_item_description">-->
        <!--<item name="android:layout_width">0dp</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:layout_gravity">top</item>-->
        <!--<item name="android:layout_weight">1</item>-->
        <!--<item name="android:orientation">vertical</item>-->
        <!--<item name="android:weightSum">1</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_description_text">-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:textColor">#808080</item>-->
        <!--<item name="android:paddingTop">1dp</item>-->
        <!--<item name="android:paddingBottom">1dp</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_description_text.port">-->
        <!--<item name="android:layout_width">fill_parent</item>-->
        <!--<item name="android:layout_marginRight">10dp</item>-->
        <!--<item name="android:textSize">12sp</item>-->
    <!--</style>-->


    <!--<style name="unipay_abroad_price_item_description_text.land">-->
        <!--<item name="android:textSize">10sp</item>-->
        <!--<item name="android:layout_width">match_parent</item>-->
        <!--<item name="android:layout_marginTop">2dp</item>-->
        <!--<item name="android:layout_weight">1</item>-->
        <!--<item name="android:ellipsize">end</item>-->
    <!--</style>-->

    <!--<style name="unipay_abroad_price_item_price">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:textSize">21sp</item>-->
        <!--<item name="android:textStyle">bold</item>-->
        <!--<item name="android:textColor">#fff</item>-->
    <!--</style>-->
    <!--<style name="unipay_abroad_price_item_icon">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:layout_marginBottom">8dp</item>-->
    <!--</style>-->
    <!--<style name="unipay_abroad_price_item_price.sub">-->
        <!--<item name="android:layout_marginRight">5dp</item>-->
        <!--<item name="android:textSize">14sp</item>-->
    <!--</style>-->

    <style name="unipay_btn">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/unipay_abroad_drawable_embtn2</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#fff</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="unipay_abroad_btn">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/unipay_abroad_drawable_btn</item>
        <item name="android:textColor">@color/unipay_abroad_thin1</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="unipay_btn.thinbtn">
        <item name="android:background">@drawable/unipay_abroad_drawable_thinbtn</item>
        <item name="android:textColor">#333</item>
    </style>

    <style name="unipay_customDialog" parent="android:style/Theme.Dialog">
      <item name="android:windowFrame">@null</item>
      <item name="android:windowIsFloating">true</item>
      <item name="android:windowIsTranslucent">true</item>
      <item name="android:windowNoTitle">true</item>
      <item name="android:backgroundDimEnabled">true</item>
      <item name="android:windowBackground">@drawable/unipay_abroad_tipsbg</item>
    </style>

    <!--<style name="unipay_linear">-->
        <!--<item name="android:layout_width">fill_parent</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:orientation">vertical</item>-->
    <!--</style>-->

    <!--小黄条-->
      <!--<style name="unipay_text">-->
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
        <!--<item name="android:textSize">15sp</item>-->
        <!--<item name="android:textColor">@color/unipay_btn_color</item>-->
    <!--</style>-->

    <style name="unipay_dialog_linearLayout">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:paddingTop">20dp</item>
        <item name="android:paddingLeft">25dp</item>
        <item name="android:paddingRight">25dp</item>
        <item name="android:paddingBottom">25dp</item>
        <item name="android:orientation">vertical</item>
    </style>

     <style name="unipay_tiptitle">
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="unipay_tiptitle.sub">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="unipay_CustomProgressDialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!--&lt;!&ndash;沙箱显示提示文字大小1&ndash;&gt;-->
    <!--<style name="sandbox_tips_size1">-->
        <!--<item name="android:textSize">20sp</item>-->
    <!--</style>-->

    <!--&lt;!&ndash;沙箱显示提示文字大小2&ndash;&gt;-->
    <!--<style name="sandbox_tips_size2">-->
        <!--<item name="android:textSize">18sp</item>-->
    <!--</style>-->

</resources>
