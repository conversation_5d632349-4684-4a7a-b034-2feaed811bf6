<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="10dp"
    android:padding="10dp"
    android:background="@drawable/layout_back"
    >

    <!--env-->
    <LinearLayout
        android:id="@+id/layout1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|top"
            android:text="Env"
            android:background="@android:color/transparent"
            />

        <RadioGroup
            android:id="@+id/env_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="left|top"
            >

            <RadioButton
                android:id="@+id/dev"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="dev"
                />
            <RadioButton
                android:id="@+id/test"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="test"
                />
            <RadioButton
                android:id="@+id/release"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="release"
                />
        </RadioGroup>


    </LinearLayout>


    <!--openId-->
    <LinearLayout
        android:id="@+id/layout2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:orientation="vertical"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|top"
            android:text="OpenId"
            android:background="@android:color/transparent"
            />
        <EditText
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/openidEdit"
            android:hint="请输入自定义openid">

        </EditText>

        <RadioGroup
            android:id="@+id/openid_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="left|top"
            >
            <RadioButton
                android:id="@+id/tryandsucc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Midas223"
                android:checked="true"
                />
            <RadioButton
                android:id="@+id/midasoverseax"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="starlightli"
                />
        </RadioGroup>
<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="left|top"-->
<!--            android:text="切换google 版本"-->
<!--            android:background="@android:color/transparent"-->
<!--            />-->
<!--        <RadioGroup-->
<!--            android:id="@+id/switchGoogleVersion"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="vertical"-->
<!--            android:layout_gravity="left|top">-->
<!--            <RadioButton-->
<!--                android:id="@+id/newGoogle"-->
<!--                android:text="isNewGoogle"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"/>-->
<!--            <RadioButton-->
<!--                android:id="@+id/oldGoogle"-->
<!--                android:text="oldNewGoogle"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"/>-->
<!--        </RadioGroup>-->

    </LinearLayout>

    <!--idc-->
    <LinearLayout
        android:id="@+id/layout3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@id/layout1"
        android:orientation="vertical"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|top"
            android:text="Idc"
            android:background="@android:color/transparent"
            />
        <RadioGroup
            android:id="@+id/idc_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="left|top"
            >
            <RadioButton
                android:id="@+id/local"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="local"
                android:checked="true"
                />

            <RadioButton
                android:id="@+id/hk"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="hongkong"
                />

            <RadioButton
                android:id="@+id/singapore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="singapore"
                />
            <RadioButton
                android:id="@+id/transfer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="transfer"
                />
        </RadioGroup>
    </LinearLayout>

    <Button
        android:id="@+id/login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/layout3"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:text="初始化"
        android:textSize="12pt"
        android:textColor="@android:color/white"
        android:background="@drawable/bt_back"
        />

</RelativeLayout>