<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="600dp"
    android:paddingLeft="30dp"
    android:paddingRight="30dp">

    <EditText
        android:id="@+id/tv_session_id"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="30dp"
        android:gravity="center"
        android:hint="Enter Session Id"
        android:maxLines="1"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_pay"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="44dp"
        android:text="Pay"
        app:layout_constraintTop_toBottomOf="@+id/tv_session_id"
        tools:layout_editor_absoluteX="30dp" />

</androidx.constraintlayout.widget.ConstraintLayout>