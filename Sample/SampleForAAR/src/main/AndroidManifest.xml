<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.tencent.imsdk.samples"
    android:versionCode="16"
    android:versionName="16.0">
    <!-- 通用权限 -->
    <application
        android:name="com.example.myapplication.View.LeakApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <meta-data
            android:name="com.google.android.play.billingclient.version"
            android:value="6.1.0" />

        <activity
            android:name="com.android.billingclient.api.ProxyBillingActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.example.myapplication.DemoWebActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:process=":demoweb"
            android:theme="@style/AppTheme.NoActionBar"></activity>
        <!-- GDPR数据采集开关,true：不采集，false：采集，默认false -->
        <activity
            android:name="com.example.myapplication.View.ScrollingActivity"
            android:label="@string/app_name"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity> <!-- 支付失败定时刷补发开关，默认打开 -->
        <activity
            android:name="com.centauri.oversea.business.pay.CTIProxyActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"></activity>

        <activity
            android:name="com.centauri.oversea.business.payhub.doku.APDokuWebActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"></activity>
        <activity
            android:name="com.centauri.oversea.business.h5.CTIWebActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:process=":remoteWeb" />

        <meta-data
            android:name="isMidasGdprOn"
            android:value="false" /> <!-- Midas集成通用 Activity -->
        <meta-data
            android:name="isReprovideTimerOn"
            android:value="true" /> <!-- 网络探测用的service -->
        <meta-data
            android:name="isShowErrDialog"
            android:value="false" /> <!-- 跨进程WebView Activity -->
        <meta-data
            android:name="isQuerySecondSkuDetails"
            android:value="true" /> <!-- 支付成功后查询第二次物品 -->
        <service
            android:name="com.centauri.oversea.newnetwork.service.APNetDetectService"
            android:process=":networkDetector" /> <!-- 主进程与webview进程通信service -->
        <service android:name="com.centauri.oversea.business.h5.WebService" />

        <receiver android:name="com.amazon.device.iap.ResponseReceiver" >
            <intent-filter>
                <action
                    android:name="com.amazon.inapp.purchasing.NOTIFY"
                    android:permission="com.amazon.inapp.purchasing.Permission.NOTIFY" />
            </intent-filter>
        </receiver>
    </application>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Google Wallet支付需要的权限 -->
    <uses-permission android:name="com.android.vending.BILLING" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="com.samsung.android.iap.permission.BILLING"/>
</manifest>