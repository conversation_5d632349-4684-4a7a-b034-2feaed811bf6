package com.example.myapplication.Cfg;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/1.
 */

public class DC {

    private static DC instance = null;

    private DC(){
        cfg = new Config(); //默认Demo配置
    }

    public static DC instance(){
        if(instance == null){
            synchronized (DC.class){
                if(instance == null){
                    instance = new DC();
                }
            }
        }
        return instance;
    }

    //配置
    public Config cfg = null;
    public String env = "sandbox";
    public String idc = ""; //废弃
    public String idcInfo = "{\"release\": {\"k_domain\":\"sandbox.centauriglobal.com\",\"k_domain_hb\":\"sandbox.centauriglobal.com\",\"k_ip_list\":[\"*************\"]},\"dev\":{\"k_domain\":\"sandbox.centauriglobal.com\",\"k_domain_hb\":\"sandbox.centauriglobal.com\",\"k_ip_list\":[\"*************\"]},\"sandbox\":{\"k_domain\":\"sandbox.centauriglobal.com\",\"k_domain_hb\":\"sandbox.centauriglobal.com\",\"k_ip_list\":[\"*************\"]}}";
//    public String idcInfo = "{\"production\":{\"k_domain\":\"ms.singaporepaya.com\",\"k_domain_hb\":\"ms.singaporepaya.com\",\"k_ip_list\":[\"*************\"]},\"sandbox\":{\"k_domain\":\"sandbox.centauriglobal.com\",\"k_domain_hb\":\"sandbox.centauriglobal.com\",\"k_ip_list\":[\"*************\"]},\"dev\":{\"k_domain\":\"presandbox.centauriglobal.com\",\"k_domain_hb\":\"presandbox.centauriglobal.com\",\"k_ip_list\":[]}}";
//    public String idcInfo = "{ \"test\": { \"local\": { \"k_domain\": \"sandbox1.api.unipay.qq.com\", \"k_domain_hb\": \"sandbox.api.unipay.qq.com\", \"k_ip_list\": [\"*************\"] }, \"singapore\": { \"k_domain\": \"sandbox.centauriglobal.com\", \"k_domain_hb\": \"sandbox.centauriglobal.com\", \"k_ip_list\": [\"*************\"] } } }";
//    public String idcInfo = "{\n" +
//            "\n" +
//            "\t\"release\": {\n" +
//            "\n" +
//            "\t\t\"local\": {\n" +
//            "\n" +
//            "\t\t\t\"k_domain\": \"na1.centauriglobal.com\",\n" +
//            "\t\t\t\"k_domain_hb\": \"na1.centauriglobal.com\"\n" +
//            "\n" +
//            "\t\t}\n" +
//            "\n" +
//            "\t},\n" +
//            "\n" +
//            "\t\"test\": {\n" +
//            "\n" +
//            "\t\t\"local\": {\n" +
//            "\n" +
//            "\t\t\t\"k_domain\": \"na11.centauriglobal.com\",\n" +
//            "\t\t\t\"k_domain_hb\": \"sandbox.centauriglobal.com\"\n" +
//            "\n" +
//            "\t\t}\n" +
//            "\n" +
//            "\n" +
//            "\t},\n" +
//            "\n" +
//            "\t\"dev\": {\n" +
//            "\n" +
//            "\t\t\"local\": {\n" +
//            "\n" +
//            "\t\t\t\"k_domain\": \"dev.api.unipay.qq.com\",\n" +
//            "\t\t\t\"k_domain_hb\": \"sandbox.api.unipay.qq.com\"\n" +
//            "\n" +
//            "\t\t}\n" +
//            "\n" +
//            "\n" +
//            "\t},\n" +
//            "\n" +
//            "\t\"testing\": {\n" +
//            "\n" +
//            "\t\t\"local\": {\n" +
//            "\n" +
//            "\t\t\t\"k_domain\": \"testing.api.unipay.qq.com\",\n" +
//            "\t\t\t\"k_domain_hb\": \"sandbox.api.unipay.qq.com\"\n" +
//            "\n" +
//            "\t\t}\n" +
//            "\n" +
//            "\n" +
//            "\t}\n" +
//            "\n" +
//            "}";
}
