package com.example.myapplication.Cfg;

/**
 * Created by zachzeng on 2017/12/25.
 */

public class Config {
    public String openId = "midasoversea";
    //    public String offerid = "1450005285";
//    public String offerid = "1460000516";
    public String offerid = "1460000622";

//    public String offerid = "1460000387";  //飞机大战

    public String mGameProductID = "com.mscti.core.100";  //游戏币的productid
    public String mGoodsProductID = "com.mscti.core.resurrection"; //道具的productid
    public String mSubsProductID = "subsweek_autopay_default_pid";  //自动续费订阅

    public String amazonGameProductId = "com.proximabeta.re0jp.test60";//gamecoins1 //com.proximabeta.re0jp.test60
    public String amazonGoodsProductId = "goodspackage";
    public String amazonSubcribeProductId = "Subscription-re0";

    public String mServiceCode = "subsweek";
    public String mServiceName = "subsweek";

    public String mCountry = "US";
    public String mCurrency= "USD";

    //是否使用特殊渠道配置
    public boolean isSpecialChannel = false;

}

