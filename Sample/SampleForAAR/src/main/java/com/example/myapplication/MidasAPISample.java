package com.example.myapplication;

import android.app.Activity;
import android.text.TextUtils;

import com.centauri.oversea.api.request.CTIPayRequest;
import com.example.myapplication.Cfg.DC;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.api.ICTICallBack;
import com.centauri.oversea.api.ICTIPayUpdateCallBack;
import com.centauri.oversea.api.request.CTIBaseRequest;
import com.centauri.oversea.api.request.CTIGameRequest;
import com.centauri.oversea.api.request.CTIGoodsRequest;
import com.centauri.oversea.api.request.CTIMonthRequest;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 业务可参考该类实现海外midas api接入配置
 * <AUTHOR>
 */
public class MidasAPISample {

    /**
     * midas初始化
     * @param activity  业务外部传入，midas不持有
     * @param callBack  初始化回调，
     */
    public static void MidasInit(Activity activity, ICTIPayUpdateCallBack callBack){
        //1、打开日志开关
        CTIPayAPI.singleton().setLogEnable(true);
        //2、设置支付环境，"sandbox"：沙箱，"release"：现网
        CTIPayAPI.singleton().setEnv("sandbox");
        CTIPayAPI.singleton().setIDCInfo(DC.instance().idcInfo);
        //3、设置初始化参数
        CTIBaseRequest request = new CTIGameRequest();
//        request.zoneId = "1";
        request.serverId = "1111";
        request.roleId = "222";
//        request.goodsZoneId="1001";
//        request.offerId = "**********";
//        request.providerOfferId = "900000958";
//        request.mpInfo.payChannel = "gwallet";
        request.offerId = "**********";
        request.providerOfferId = "900000958";
        request.mpInfo.payChannel = "os_mp";
        request.regionCode = "SG";
        request.currencyCode = "SGD";
        request.userId = DC.instance().cfg.openId;
        //初始化
        CTIPayAPI.singleton().init(activity, request,callBack);
    }


    /**
     * 购买游戏币
     * @param activity  业务外部传入，midas不持有
     * @param callBack  支付回调
     */
    public static void buyGameCoins(Activity activity,ICTICallBack callBack){
        //1、创建CTIGameRequest实例
        CTIPayRequest request = new CTIPayRequest();
//        request.offerId = "146000080";
//        request.openId = "midas22";
        request.payInfo = "{\"data\":{\"app_id\":\"**********\",\"res_app_id\":\"\",\"biz_app_id\":\"\",\"transaction_id\":\"E-240122110012426019\",\"reference_id\":\"\",\"transaction\":{\"pay_desc\":\"\",\"pay_channel\":\"os_midaspay\",\"sub_channel\":\"1\",\"currency\":\"HKD\",\"amount\":\"\",\"region\":\"HK\",\"application_context\":null,\"coin_info\":null,\"contract_info\":null,\"sub_channel_name_list\":\"\"},\"buy_info\":{\"login_number\":\"\",\"login_token\":\"\",\"login_type\":1,\"pay_number\":\"\",\"pay_token\":\"\",\"provide_number\":\"5240528299984796896\",\"server_id\":\"\",\"role_id\":\"\",\"platform\":1,\"address\":{\"region\":\"\",\"state_or_province\":\"\",\"city\":\"\",\"street\":\"\",\"house_number_or_name\":\"\",\"postal_code\":\"\"},\"user_ip\":\"***************\",\"provide_app_id\":\"\"},\"purchase_list\":[{\"product_id\":\"com.levelinfinite.sgameglobal_60_ie\",\"quantity\":\"1\",\"product_type\":\"save\",\"product_name\":\"80\\u70b9\\u5238\",\"price\":\"782\",\"provide_app_id\":\"900000935\",\"server_id\":\"108007\",\"role_id\":\"\",\"provide_id\":\"\",\"provide_id_type\":\"\",\"role_name\":\"\",\"area\":\"\",\"partition\":\"\",\"platid\":\"\",\"join_model\":{\"list\":[]},\"real_product_id\":\"\",\"channel_product_id\":\"\"}],\"language\":\"\",\"scene\":\"00000000\",\"encrypt_redirect_url\":{\"version\":\"v1\",\"redirect_url\":\"02BED45E40804FD581DBCC922211378C3941B3F83B0F00127219357E78AEC3A496469717E28EF40F1228DD479C83B5B0552169CE91BF6E082E6770F6B68D43F03EF4B3922EAA2DED6AF25BC412C5194584D5B0459A3F24811EB28237A8B9CF059510D3D97F042A4B30E818978AD51FCBA8296F46734EA7B5D498FA760F4CF516EDEFDAA7614FB1CD2F6BB18E9FF935944E56795ED66690B0FF2CBD1B696C0BD4D7DDF9FF455EEEA58ED004A6DE9BCF1CA9205FD09871D06330251305A5BFCE17AC3EFEEA2EA53D53C763296A94277B5349BCAFFA1BEC1B19916B660349E9AA43B7B8FDBC465FD48D92541EA364E703968B84168095C902F6F2F4603FE364F392425DD024F6D7A3CAFACC11F0CA38957D55FAE6185B07BED5CEBAF927B9B34D6F4E450B468909D58139400CE1738A608BC3C92B820F1359B66C9E943095BA0B961F6A47DC3196F93DFA3B7CBB78AB457844651C1BAD3519079152570AF1D2D07E9266827A8EAA991F8C5A83DD095ED4CF3203CC3360420F485FFB80DA63D3A35F2172E02544E2DA5BD31A5B7AF8A889E92F7C33207E3CAF7D3FA65EFBA98BD84C50378FA5DAAA6579AE97312B7808AD14\",\"msg_len\":\"428\"},\"package_name_validation\":{\"name\":\"com.mscti.game.plane\",\"verify_type\":1}},\"sign\":\"\"}";
//        request.payInfo = "{\"data\":\"eyJhcHBfaWQiOiIxNDYwMDAwNjIyIiwicmVzX2FwcF9pZCI6IiIsImJpel9hcHBfaWQiOiIiLCJ0cmFuc2FjdGlvbl9pZCI6IkUtMjQwOTI3MDcwMDMwMDM1MDc3IiwicmVmZXJlbmNlX2lkIjoiIiwidHJhbnNhY3Rpb24iOnsicGF5X2Rlc2MiOiIiLCJwYXlfY2hhbm5lbCI6Imd3YWxsZXQiLCJzdWJfY2hhbm5lbCI6IjEiLCJjdXJyZW5jeSI6IlVTRCIsImFtb3VudCI6IiIsInJlZ2lvbiI6IlVTIiwiYXBwbGljYXRpb25fY29udGV4dCI6bnVsbCwiY29pbl9pbmZvIjpudWxsLCJjb250cmFjdF9pbmZvIjpudWxsLCJzdWJfY2hhbm5lbF9uYW1lX2xpc3QiOiIifSwiYnV5X2luZm8iOnsibG9naW5fbnVtYmVyIjoiIiwibG9naW5fdG9rZW4iOiIiLCJsb2dpbl90eXBlIjoiTk9MT0dJTiIsInBheV9udW1iZXIiOiIiLCJwYXlfdG9rZW4iOiIiLCJwcm92aWRlX251bWJlciI6IjEiLCJzZXJ2ZXJfaWQiOiIxIiwicm9sZV9pZCI6IiIsInBsYXRmb3JtIjoiQU5EUk9JRCIsImFkZHJlc3MiOm51bGwsInVzZXJfaXAiOiIiLCJwcm92aWRlX2FwcF9pZCI6IiIsIm9wZW5rZXkiOiJoeV9vcGVua2V5Iiwic2Vzc2lvbl9pZCI6Imh5X2dhbWVpZCIsInNlc3Npb25fdHlwZSI6InN0X2R1bW15In0sInB1cmNoYXNlX2xpc3QiOlt7InByb2R1Y3RfaWQiOiJQUk8tVG9rZW4tMDYxMi0xIiwicXVhbnRpdHkiOiIxIiwicHJvZHVjdF90eXBlIjoic2F2ZSIsInByb2R1Y3RfbmFtZSI6IumBk+WFt+iwt+atjOa4oOmBky3mtYvor5UiLCJwcmljZSI6Ijk5IiwicHJvdmlkZV9hcHBfaWQiOiI5MDAwMDA5NTgiLCJzZXJ2ZXJfaWQiOiIxIiwicm9sZV9pZCI6IiIsInByb3ZpZGVfaWQiOiIiLCJwcm92aWRlX2lkX3R5cGUiOiIiLCJyb2xlX25hbWUiOiIiLCJhcmVhIjoiIiwicGFydGl0aW9uIjoiIiwicGxhdGlkIjoiIiwiam9pbl9tb2RlbCI6eyJsaXN0IjpbXX0sInJlYWxfcHJvZHVjdF9pZCI6IiIsImNoYW5uZWxfcHJvZHVjdF9pZCI6ImNvbS5tc2N0aS5jb3JlLjEwMCIsImFtc19naWZ0X2luZm8iOm51bGwsImN1c3RvbV9wYXJhbXNfdG9rZW4iOiIiLCJhbXNfcHJvdmlkZV9leHRyYSI6e30sImNoYW5uZWxfcHJvZHVjdF9pbmZvIjoiIiwicGF5X2l0ZW0iOiIifV0sImxhbmd1YWdlIjoiZW4iLCJzY2VuZSI6IiIsImVuY3J5cHRfcmVkaXJlY3RfdXJsIjpudWxsLCJwYWNrYWdlX25hbWVfdmFsaWRhdGlvbiI6bnVsbCwicGYiOiIiLCJwZl9rZXkiOiIifQ==\",\"sign\":\"VKPCMyMgeM82roHoUU3YaCll==\",\"version\":\"v3\"}";
//        request.payInfo = "{\"data\":{\"app_id\":\"**********\",\"res_app_id\":\"\",\"biz_app_id\":\"\",\"transaction_id\":\"E-240924080013904017\",\"reference_id\":\"billno1727166365vw9yo\",\"transaction\":{\"pay_desc\":\"\",\"pay_channel\":\"gwallet\",\"sub_channel\":\"2\",\"currency\":\"HKD\",\"amount\":\"\",\"region\":\"HK\",\"application_context\":null,\"coin_info\":null,\"contract_info\":null,\"sub_channel_name_list\":\"\"},\"buy_info\":{\"login_number\":\"\",\"login_token\":\"\",\"login_type\":1,\"pay_number\":\"\",\"pay_token\":\"\",\"provide_number\":\"Midas66\",\"server_id\":\"2\",\"role_id\":\"\",\"platform\":1,\"address\":{\"region\":\"\",\"state_or_province\":\"\",\"city\":\"\",\"street\":\"\",\"house_number_or_name\":\"\",\"postal_code\":\"\"},\"user_ip\":\"*******\",\"provide_app_id\":\"\",\"openkey\":\"hy_openkey\",\"session_id\":\"hy_gameid\",\"session_type\":\"st_dummy\"},\"purchase_list\":[{\"product_id\":\"PRO-Token-0612-1\",\"quantity\":\"1\",\"product_type\":\"save\",\"product_name\":\"\u9053\u5177\u8c37\u6b4c\u6e20\u9053-\u6d4b\u8bd5\",\"price\":\"800\",\"provide_app_id\":\"900000958\",\"server_id\":\"1\",\"role_id\":\"\",\"provide_id\":\"\",\"provide_id_type\":\"\",\"role_name\":\"\",\"area\":\"\",\"partition\":\"\",\"platid\":\"\",\"join_model\":{\"list\":[]},\"real_product_id\":\"\",\"channel_product_id\":\"com.mscti.core.100\",\"ams_gift_info\":null,\"custom_params_token\":\"\",\"ams_provide_extra\":{},\"channel_product_info\":\"{\\\"gwallet_package_name\\\":\\\"com.mscti.game.plane\\\",\\\"gwallet_product_type\\\":\\\"NON_CONSUMABLE\\\",\\\"gwallet_subscription_group_id\\\":\\\"empty\\\",\\\"gwallet_basic_plan_id\\\":\\\"empty\\\",\\\"gwallet_product_id\\\":\\\"com.mscti.core.100\\\",\\\"sdk_product_type\\\":\\\"inapp\\\"}\",\"pay_item\":\"HK-PRO-Token-0612-1*{\\\"acct_client_ver\\\":\\\"android\\\",\\\"acct_ext1\\\":\\\"ext\\\",\\\"acct_ext2\\\":\\\"ext\\\",\\\"acct_ext3\\\":\\\"ext\\\",\\\"acct_zoneid\\\":\\\"1\\\",\\\"amt\\\":\\\"800\\\",\\\"area\\\":\\\"\\\",\\\"ori_zoneid\\\":\\\"1\\\",\\\"partition\\\":\\\"\\\",\\\"platid\\\":\\\"\\\",\\\"roleid\\\":\\\"\\\"}*1*900000958\"}],\"language\":\"en\",\"scene\":\"xx\",\"encrypt_redirect_url\":null,\"package_name_validation\":{\"name\":\"\",\"verify_type\":1},\"pf\":\"1002-xx-android\",\"pf_key\":\"hy_pfkey\"},\"sign\":\"U8g6L3F2feuNh3lWPVO92xMAcfk5r8V3zqqWmowrs9h8KQDBNWvOJ8u6QZZd2hpjLvsh8gHO3Nm3l3D5hxC3MH3MelspkHkWJuLHJN8QCsA5HutqMDFyr5K6LEVvEaOGUd7fgxf3oL3wB22LkcclnGNPmSJVF53HCioK6/8AMn6dBfieyDTTP6SafhQixdCzbjKvCK4e2aY633teQc1CHg7zJ0ZxpeWU089mroHE3/5jJ80Y6RB9nwVAzuojc/4i/sEpOoH5j+nOu/rASTbZDFc+OcTB2nFMKTRcUpjFjyLOj5mUnRQr9BEBd26kOvpEE3KHXISae7U+NtzA/WgOKg==\"}";
//        request.payInfo = "{ \"data\": { \"app_id\": \"**********\", \"res_app_id\": \"\", \"biz_app_id\": \"\", \"transaction_id\": \"E-240827080013706002\", \"reference_id\": \"1724746455\", \"transaction\": { \"pay_desc\": \"\", \"pay_channel\": \"gwallet\", \"sub_channel\": \"2\", \"currency\": \"HKD\", \"amount\": \"\", \"region\": \"HK\", \"application_context\": null, \"coin_info\": null, \"contract_info\": null, \"sub_channel_name_list\": \"\" }, \"buy_info\": { \"login_number\": \"\", \"login_token\": \"\", \"login_type\": 1, \"pay_number\": \"\", \"pay_token\": \"\", \"provide_number\": \"hungtaulitest\", \"server_id\": \"1\", \"role_id\": \"\", \"platform\": 1, \"address\": { \"region\": \"\", \"state_or_province\": \"\", \"city\": \"\", \"street\": \"\", \"house_number_or_name\": \"\", \"postal_code\": \"\" }, \"user_ip\": \"*******\", \"provide_app_id\": \"\", \"openkey\": \"nokey\", \"session_id\": \"hy_gameid\", \"session_type\": \"st_dummy\" }, \"purchase_list\": [ { \"product_id\": \"PRO-Token-0612-1\", \"quantity\": \"1\", \"product_type\": \"save\", \"product_name\": \"道具谷歌渠道-测试\", \"price\": \"800\", \"provide_app_id\": \"900000958\", \"server_id\": \"1\", \"role_id\": \"\", \"provide_id\": \"\", \"provide_id_type\": \"\", \"role_name\": \"\", \"area\": \"\", \"partition\": \"\", \"platid\": \"\", \"join_model\": { \"list\": [] }, \"real_product_id\": \"\", \"channel_product_id\": \"com.mscti.core.100\", \"ams_gift_info\": null, \"custom_params_token\": \"\", \"ams_provide_extra\": {}, \"channel_product_info\": \"{\\\"gwallet_package_name\\\":\\\"com.mscti.game.plane\\\",\\\"gwallet_product_type\\\":\\\"NON_CONSUMABLE\\\",\\\"gwallet_subscription_group_id\\\":\\\"empty\\\",\\\"gwallet_basic_plan_id\\\":\\\"empty\\\",\\\"gwallet_product_id\\\":\\\"com.mscti.core.100\\\",\\\"sdk_product_type\\\":\\\"inapp\\\"}\", \"pay_item\": \"HK-PRO-Token-0612-1*{\\\"acct_client_ver\\\":\\\"android\\\",\\\"acct_ext1\\\":\\\"ext\\\",\\\"acct_ext2\\\":\\\"ext\\\",\\\"acct_ext3\\\":\\\"ext\\\",\\\"acct_zoneid\\\":\\\"1\\\",\\\"amt\\\":\\\"800\\\",\\\"area\\\":\\\"\\\",\\\"ori_zoneid\\\":\\\"1\\\",\\\"partition\\\":\\\"\\\",\\\"platid\\\":\\\"\\\",\\\"roleid\\\":\\\"\\\"}*1*900000958\" } ], \"language\": \"en\", \"scene\": \"xx\", \"encrypt_redirect_url\": null, \"package_name_validation\": { \"name\": \"\", \"verify_type\": 1 }, \"pf\": \"1002-xx-android\", \"pf_key\": \"hy_pfkey\" }, \"sign\": \"Y+puU123eBnuTZHxDv3aR91qIJrX+UezaF7KI7/VprA2yo+yHbEX7WV9rb3mZ5sGujSE52qpwW78SjkcuOAJOwi1JHAje3wya2+K3UoiMkwkzpprzRth6aSp8MRivAuW+slnVFDC+F05Ksu8Fm5CRJSTzlv+Ub5FThHDY3MIIyNZQZZwigpAr+y/7TMqzqN8xn6RnzmyJAiWkzOEAzdxrnRgGDrynESG2vBiMi9qJ4v06eJSqaJQZtNzPtRsdj7EIcpfwJTeD79kGJ+rn0Q/wamefNahwgDBAd0MiigO2rq7NmbulbF/QpjVuzptOj40hAdzN9F72jmiOhtDJCV2aw==\" }";
        CTIPayAPI.singleton().pay(activity,request,callBack);
////        CTIGameRequest request = new CTIGameRequest();
////        initRequest(request);
////        request.offerId = DC.instance().cfg.offerid;
////        request.openId = DC.instance().cfg.openId;
////        request.mpInfo.payChannel = "gwallet";                           //支付渠道名
////        request.mpInfo.productid = DC.instance().cfg.mGameProductID;    //midas测配置物品id
////        request.country = DC.instance().cfg.mCountry;            //midas测对应的物品国家代码
////        request.currency_type = DC.instance().cfg.mCurrency;    //midas测对应的物品币种代码
////
////        CTIPayAPI.singleton().pay(activity,request,callBack);

//        CTIGameRequest request = new CTIGameRequest();
//        request.mpInfo.payChannel = "os_mp";
//        request.extras = "pay_info={ \"data\": { \"app_id\": \"**********\", \"res_app_id\": \"\", \"biz_app_id\": \"\", \"transaction_id\": \"E-231222090012219086\", \"reference_id\": \"\", \"transaction\": { \"pay_desc\": \"\", \"pay_channel\": \"os_midaspay\", \"sub_channel\": \"1\", \"currency\": \"USD\", \"amount\": \"\", \"region\": \"US\", \"application_context\": null, \"coin_info\": null, \"contract_info\": null, \"sub_channel_name_list\": \"\" }, \"buy_info\": { \"login_number\": \"\", \"login_token\": \"\", \"login_type\": 1, \"pay_number\": \"\", \"pay_token\": \"\", \"provide_number\": \"16349793653035829818\", \"server_id\": \"666668\", \"role_id\": \"\", \"platform\": 3, \"address\": { \"region\": \"\", \"state_or_province\": \"\", \"city\": \"\", \"street\": \"\", \"house_number_or_name\": \"\", \"postal_code\": \"\" }, \"user_ip\": \"*******\", \"provide_app_id\": \"\" }, \"purchase_list\": [{ \"product_id\": \"IEGAMS-3000186-3000101_3003737\", \"quantity\": \"1\", \"product_type\": \"bg\", \"product_name\": \"永恒水手月亮\", \"price\": \"2980\", \"provide_app_id\": \"900000935\", \"server_id\": \"1\", \"role_id\": \"\", \"provide_id\": \"\", \"provide_id_type\": \"\", \"role_name\": \"\", \"area\": \"\", \"partition\": \"\", \"platid\": \"\", \"join_model\": { \"list\": [] }, \"real_product_id\": \"\", \"channel_product_id\": \"\" }], \"language\": \"en\", \"scene\": \"xx\", \"encrypt_redirect_url\": { \"version\": \"v1\", \"redirect_url\": \"02BED45E40804FD581DBCC922211378C194010CD01B488314CA06A372A3F62C5453FDCD347479FC4D69F723E068F86F7F5BCA924ECA3E4C99D1E5F99006D524C05E39DEA1092F8BE875821FFC063BA7939499EE278EE2B81BDC745DE60DCF136DD1E3E75F08D174253DC0FF6FA3770EC957FC05C23EAA803898B644286079A9C783D41E4C837FED2191F77771C12C888E4FA268039A392EC255D18A20DC65B8C92A507A91DEF0B575092BB447CC4FA54758B28BAFED649B6EACBAD9A5795CC3E5572706878CDD684524BF6277A81B912C70C4C47D703348AA3A781F4A68F5F573A5DC834ECD09DC60887773A4DBE59E54763A7A9937BA71EC9B9EA12544E20C8EEB3F72F89D41E65D45F89D663BACDD54E6DF0F1DDDDD3CE465D758D4D8E0C093BDF9CEEA550554D770AB869AD713B4F81A40960ECC1FB6E970F335F9B0A6180B2840D66C44B52F9ACA3A07BD5AD58368E4E0EF03138F1C43661813037BCFA1CB92D6EF2C6CF9A7C9B47A1E2420276E59EA8DDEAFCFB582D2A4B08841D3F4D5E932BBE595C2AD0CA277AE5F92E0A4F18A61C978E0EC92EC0324617E7AB56007C668901E8C2060D5E404C83C99D89C7587EADDBDF9FE681D7E887E628DE7FAA3D23B435D966C7A493C6B7DA877EC3E1281EE7DF69373716A17B6FAC1E7C7A8774\", \"msg_len\": \"469\" }, \"package_name_validation\": { \"name\": \"com.mscti.game.plane\", \"verify_type\": 1 } } }";
//        CTIPayAPI.singleton().pay(activity, request,callBack);
    }


    /**
     *  购买包月（非自动续费）
     * @param activity  业务外部传入，midas不持有
     * @param callBack  支付回调
     */
    public static void buyMonth(Activity activity,ICTICallBack callBack) {
        CTIMonthRequest request = new CTIMonthRequest();
        initRequest(request);
        request.offerId = DC.instance().cfg.offerid;
        request.openId = DC.instance().cfg.openId;
        request.mpInfo.productid = "product02";    //midas测配置物品id
        request.country = DC.instance().cfg.mCountry;            //midas测对应的物品国家代码
        request.currency_type = DC.instance().cfg.mCurrency;    //midas测对应的物品币种代码
        request.mType = "month";
//        request.basePlanId = "base02-auto-month-7-10hkd-base02-auto-month-7-10hkd-base02-au00";
//        request.gw_offerId = "offer3-new-zjjd-once-week-50percent";
        request.mpInfo.payChannel = "gwallet";
        request.autoPay = false;                //自动续费：设为true
//        request.serviceCode = DC.instance().cfg.mServiceCode;      //midas测配置的业务代码
//        request.serviceName = DC.instance().cfg.mServiceName;
        CTIPayAPI.singleton().pay(activity, request,callBack);
    }


    /**
     *  购买订阅
     *  订阅对应midas测物品类型：1、自动续费包月，2、自动续费月卡
     * @param activity  业务外部传入，midas不持有
     * @param callBack  支付回调
     */
    public static void buySub(Activity activity,ICTICallBack callBack){
        CTIMonthRequest request = new CTIMonthRequest();
        initRequest(request);
        request.offerId = DC.instance().cfg.offerid;
        request.openId = DC.instance().cfg.openId;
        request.mpInfo.productid = DC.instance().cfg.mSubsProductID;    //midas测配置物品id
        request.country = DC.instance().cfg.mCountry;            //midas测对应的物品国家代码
        request.currency_type = DC.instance().cfg.mCurrency;    //midas测对应的物品币种代码

        request.basePlanId = "base02-auto-month-7-10hkd";
//        request.basePlanId = "base02-auto-month-7-10hkd-base02-auto-month-7-10hkd-base02-au00";
//        request.gw_offerId = "offer3-new-zjjd-once-week-50percent";
        request.mpInfo.payChannel = "gwallet";
        request.autoPay = true;                //自动续费：设为true
        request.serviceCode = DC.instance().cfg.mServiceCode;      //midas测配置的业务代码
        request.serviceName = DC.instance().cfg.mServiceName;
        CTIPayAPI.singleton().pay(activity, request,callBack);
    }


    //可配置默认参数
    public static void initRequest(CTIBaseRequest request) {
        request.zoneId = "1";
        request.goodsZoneId="1001";
        request.pf = "huyu_m-2001-android-2001";
    }

    //可配置默认参数
    public static void initRequest(CTIBaseRequest request, String zoneId) {
        request.sessionId = "hy_gameid";
        request.sessionType = "st_dummy";
        request.zoneId = zoneId;
        request.goodsZoneId="1001";
        request.pf = "huyu_m-2001-android-2001";
    }

}
