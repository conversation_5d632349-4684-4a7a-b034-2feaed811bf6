package com.example.myapplication.View;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.centauri.oversea.api.request.CTIGoodsRequest;
import com.centauri.oversea.api.request.CTIProductRequest;
import com.centauri.oversea.api.request.CTIPromotionRequest;
import com.centauri.oversea.api.request.ICTIPromotionCallback;
import com.centauri.oversea.newapi.params.NetParams;
import com.example.myapplication.CTIBase64;
import com.example.myapplication.Cfg.DC;
import com.example.myapplication.MidasAPISample;
import com.google.android.material.snackbar.Snackbar;
import com.tencent.imsdk.samples.R;
import com.centauri.comm.CTILog;
//import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.api.ICTINetCallBack;
import com.centauri.oversea.api.ICTICallBack;
import com.centauri.oversea.api.ICTIPayUpdateCallBack;
import com.centauri.oversea.api.request.CTIBaseRequest;
import com.centauri.oversea.api.request.CTIGameRequest;
import com.centauri.oversea.api.request.CTIMonthRequest;
import com.centauri.oversea.api.request.ICTIProductInfoCallback;
import com.centauri.oversea.business.pay.CTIResponse;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newapi.params.MallParams;
//import com.centauri.oversea.newnetwork.http.NetworkManager;
//import com.centauri.oversea.newnetwork.service.CTIIPDetectTools;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by zachzeng on 2017/11/1.
 */

public class PayHelper
        implements View.OnClickListener,
        ICTICallBack,ICTINetCallBack,ICTIPayUpdateCallBack,ICTIProductInfoCallback{
    public static final String TAG = "PayHelper";
    private Activity mActivity;



    Handler mHandler = new Handler();
//    GWHelper mGWHelper;
    public PayHelper(Activity activity) {
        mActivity = activity;
//        mGWHelper = new GWHelper(mActivity);
    }

    /**
     * 米大师外部接口
     */
    public void MidasAPI(View base){
        View view = LayoutInflater.from(mActivity).inflate(R.layout.layout_midas_api, null);
        TextView net = (TextView) view.findViewById(R.id.midas_net);
        TextView reProvide = (TextView) view.findViewById(R.id.midas_reprovide);
        TextView getProductInfo = (TextView) view.findViewById(R.id.midas_getProductInfo);
        TextView getIntroPriceInfo = (TextView) view.findViewById(R.id.midas_getIntroPriceInfo);
        TextView deInit = (TextView) view.findViewById(R.id.midas_deinit);
        TextView showMidasUI = (TextView)view.findViewById(R.id.show_midas_ui);

        net.setOnClickListener(this);
        reProvide.setOnClickListener(this);
        getProductInfo.setOnClickListener(this);
        getIntroPriceInfo.setOnClickListener(this);
        deInit.setOnClickListener(this);
        showMidasUI.setOnClickListener(this);

        BottomWM.instance().setHeight(400).build(mActivity).show(base, view);
    }


    /**
     * mol支付
     * @param base
     */
    public void MolPay(View base){
        molAccount();
    }


    /**
     * google play支付
     */
    public void GWPay(View base) {
        View view = LayoutInflater.from(mActivity).inflate(R.layout.layout_gwpay, null);
        TextView buy = (TextView) view.findViewById(R.id.buy_bg);
        TextView game = (TextView)view.findViewById(R.id.buy_game);
        TextView sub = (TextView) view.findViewById(R.id.sub);
        TextView sub_upgrade = (TextView) view.findViewById(R.id.sub_upgrade);
        TextView query = (TextView) view.findViewById(R.id.query);
        TextView reProvide = (TextView) view.findViewById(R.id.reprovide);
        TextView consume = (TextView) view.findViewById(R.id.force_consume);
        game.setOnClickListener(this);
        buy.setOnClickListener(this);
        sub.setOnClickListener(this);
        sub_upgrade.setOnClickListener(this);
        query.setOnClickListener(this);
        reProvide.setOnClickListener(this);
        consume.setOnClickListener(this);

        BottomWM.instance().setHeight(400).build(mActivity).show(base, view);
    }

    /**
     * amazon
     */

    public void AmazonPay(View base) {
        View view = LayoutInflater.from(mActivity).inflate(R.layout.layout_amzon, null);
        TextView buy = (TextView) view.findViewById(R.id.amazon_buy_bg);
        TextView game = (TextView)view.findViewById(R.id.amazon_buy_game);
        TextView sub = (TextView) view.findViewById(R.id.amazon_sub);
        TextView query = (TextView) view.findViewById(R.id.amazon_query);
        TextView reProvide = (TextView) view.findViewById(R.id.amazon_reprovide);
        TextView consume = (TextView) view.findViewById(R.id.amazon_force_consume);
        game.setOnClickListener(this);
        buy.setOnClickListener(this);
        sub.setOnClickListener(this);
        query.setOnClickListener(this);
        reProvide.setOnClickListener(this);
        consume.setOnClickListener(this);

        BottomWM.instance().setHeight(400).build(mActivity).show(base, view);
    }

    /**
     * 网络探测工具
     */
    public void NetTools(View base) {
        View view = LayoutInflater.from(mActivity).inflate(R.layout.layout_nettools, null);
        ((TextView)view.findViewById(R.id.ping_btn)).setOnClickListener(this);
        ((TextView)view.findViewById(R.id.getTask_btn)).setOnClickListener(this);
        ((TextView)view.findViewById(R.id.dns_btn)).setOnClickListener(this);
        ((TextView)view.findViewById(R.id.dns_parse_btn)).setOnClickListener(this);
        ((TextView)view.findViewById(R.id.socket_btn)).setOnClickListener(this);
        BottomWM.instance().setHeight(400).build(mActivity).show(base, view);
    }

    /**
     * 调用Net接口
     */
    private void callNet(){
        String reqType = "mp";
        CTIGameRequest request = new CTIGameRequest();
        initRequest(request);
        CTIPayAPI.singleton().net(reqType,this);
    }

    /**
     * 调用Net接口
     */
    private void callV3Net(){
        String reqType = "mp";
        NetParams request = new NetParams();
        request.drmInfo = "version=3.0";
        CTIPayAPI.singleton().net(request, reqType,this);
    }

    /**
     * 调用reProvide接口
     */
    private void callReProvide(){
        CTIGameRequest request = new CTIGameRequest();
        initRequest(request);
        CTIPayAPI.singleton().reProvide(this);
    }


    /**
     * 调用getProductInfo接口
     */
    private void callGetProductInfo(){
        HashMap<String, String> skuList = new HashMap<String, String>();
        skuList.put(DC.instance().cfg.mGoodsProductID, "inapp");
        skuList.put("midas_product_1", "inapp");
        skuList.put("midas_product_1", "inapp");
        skuList.put("midas_product_3", "inapp");
        skuList.put("testsub1", "subs");
        skuList.put("testsub2", "subs");
        skuList.put("testsub3", "subs");
        skuList.put("testsub444", "subs");
        CTIPayAPI.singleton().getProductInfo("gwallet",skuList,this);
    }

    /**
     * 查物品折扣信息
     */
    private void callGetIntroPriceInfo(){
        HashMap<String, String> skuList = new HashMap<String, String>();
        skuList.put("testwithdiffprice1", "inapp");
        skuList.put("testwithdiffprice2", "inapp");

        CTIPayAPI.singleton().getIntroPriceInfo("gwallet", skuList, new ICTIProductInfoCallback() {
            @Override
            public void onProductInfoResp(String resp) {
                CTILog.d("PayHelper","getIntroPriceInfo response: "+resp);
                //测试代码
                try{
                    JSONObject js = new JSONObject(resp);
                    JSONArray jInfo = js.getJSONArray("gwallet_productInfo");
                    for(int i=0;i<jInfo.length();i++){
                        JSONObject jItem = jInfo.getJSONObject(i);
                        String productId = jItem.getString("productId");
                        String price = jItem.getString("price");
                        String currency = jItem.getString("currency");
                        long microprice = jItem.getLong("microprice");
                        String introPrice = jItem.getString("introPrice");
                        long introMicroPrice = jItem.getLong("introMicroPrice");
                        String introPeriod = jItem.getString("introPeriod");
                        String introCycles = jItem.getString("introCycles");

                        CTILog.d(TAG,productId+"|"+price+"|"+currency+"|"+microprice+"|"+introPrice+"|"+introMicroPrice+"|"+introPeriod+"|"+introCycles);
                    }
                }catch (JSONException e){
                    CTILog.e("PayHelper","getIntroPriceInfo res error.");
                }
            }
        });
    }

    /**
     * 调用deinit接口
     */
    private void callDeinit(View view){
        CTIPayAPI.singleton().deinit();
        Snackbar snackbar = Snackbar.make(view,"已注销，请重新登录使用！",Snackbar.LENGTH_LONG);
        View view1 = snackbar.getView();
        if(view1 != null){
            view1.setBackgroundColor(mActivity.getResources().getColor(R.color.colorPrimary));
        }
        snackbar.show();
    }


    /**
     * 调用showMidasUI接口
     */
    public void callShowMidasUI(View view){
        final String[] pList = {
                "closeLoading", "closeResult", "closeAll"
        };
        AlertDialog dialog = new AlertDialog.Builder(mActivity)
                .setSingleChoiceItems(pList, -1, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        switch (which){
                            case 0:
                                CTIPayAPI.singleton().showCentauriUI(CTIPayAPI.CLOSE_LOADING);
                                break;
                            case 1:
                                CTIPayAPI.singleton().showCentauriUI(CTIPayAPI.CLOSE_RESULT);
                                break;
                            case 2:
                                CTIPayAPI.singleton().showCentauriUI(CTIPayAPI.CLOSE_ALL);
                                break;
                        }

                        BottomWM.instance().close();
                    }
                })
                .setCancelable(true)
                .create();

        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            //米大师外部接口
            case R.id.midas_net:
                callV3Net();
                break;
            case R.id.midas_reprovide:
                callReProvide();
                break;
            case R.id.midas_getProductInfo:
                callGetProductInfo();
                break;
            case R.id.midas_getIntroPriceInfo:
                callGetIntroPriceInfo();
                break;
            case R.id.midas_deinit:
                callDeinit(view);
                break;
            case R.id.show_midas_ui:
                callShowMidasUI(view);
                break;

            //google play
            case R.id.buy_game:
                Log.i(TAG, "buy");
                MidasAPISample.buyGameCoins(mActivity,this);
                break;
            case R.id.buy_bg:
                Log.i(TAG, "buy");
                MidasAPISample.buyMonth(mActivity,this);
                break;
            case R.id.sub:  //订阅
                Log.i(TAG, "sub");
                //subClick();
                MidasAPISample.buySub(mActivity,this);
                break;
            case R.id.sub_upgrade:  //订阅升级
                Log.i(TAG, "subUpgrade");
                subUpgradeClick();
                break;
            case R.id.query:    //查询
                Log.i(TAG, "query");

                queryGWInfo();
                break;
            case R.id.reprovide:    //补发货
                Log.i(TAG, "reprovide");
                reProvide();
                break;
            case R.id.getTask_btn:
                Log.i(TAG, "getTask_btn");
//                NetworkManager.singleton().detectTaskQuery(new com.centauri.http.centaurihttp.ICTIHttpCallback() {
//                    @Override
//                    public void onSuccess(CTIHttpAns ctiHttpAns) {
//                        Log.i(TAG, "getTask_btn success");
//                    }
//
//                    @Override
//                    public void onFailure(CTIHttpAns ctiHttpAns) {
//                        Log.i(TAG, "getTask_btn onFailure");
//                    }
//
//                    @Override
//                    public void onStop(CTIHttpAns ctiHttpAns) {
//                        Log.i(TAG, "getTask_btn onStop");
//                    }
//                });
                break;
            case R.id.force_consume:    //强制消耗
                Log.i(TAG, "force_consume");

//                if (mGWHelper != null) {
//                    mGWHelper.consume();
//                }
                break;
            case R.id.ping_btn:
//                CTIIPDetectTools.ping("***************", 0, 4,2);
                break;
            case R.id.dns_btn:
//                List<String> dns = CTIIPDetectTools.getDnsServer(CTIPayNewAPI.singleton().getApplicationContext());
//                CTILog.d(this.getClass().getName(), "dns Servers:" + dns);
                break;
            case R.id.dns_parse_btn:
                new Thread(new Runnable() {
                    @Override
                    public void run() {
//                        CTIIPDetectTools.dnsRequest("www.parallelworld.club", "***************");
                    }
                }).start();
               break;
            case R.id.amazon_buy_bg:
                AmazonGoodsClick();
                break;
            case R.id.amazon_sub:
                AmazonSubcribeClick();
                break;
            case R.id.amazon_buy_game:
                AmazonGameClick();
                break;
            case R.id.amazon_force_consume:
//                mGWHelper.consume();
                break;
            case R.id.amazon_reprovide:
                callReProvide();
                break;
            case R.id.amazon_query:
                queryAmazonProductInfo();
                break;


        }
    }


    //替换腾讯视频订阅物品
    private void subClick() {
        final String[] pList = {
                "test_autopay_default_pid", "sub_test_2", "auto_test_autopay_default_pid",
                "test_autopay_default_pid", "month_test_autopay_default_pid", "freetrailmonth", "freetrailmonth2", "freetrailmonth3",
                "testwithdiffprice", "testwithdiffprice1", "testwithdiffprice2",
                "kuanxian_sub1","sub_kuanxian_3","sub_kuanxian_7"
        };
//        final String[] pList = {
//                "test.tencent.month","com.tencent.pay.bossmonth", "com.tencent.qpaytest.otherautorenew1week","test.tencent.package"
//        };

        AlertDialog dialog = new AlertDialog.Builder(mActivity)
                .setSingleChoiceItems(pList, -1, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        CTIMonthRequest request = new CTIMonthRequest();
                        initRequest(request);
                        request.autoPay = true;
                        request.mpInfo.payChannel = "gwallet";
                        request.serviceCode = DC.instance().cfg.mServiceCode;
                        request.serviceName = DC.instance().cfg.mServiceName;
//                        request.offerId = "1450013481";
//                        request.mType = "month";
//                        request.serviceCode = "TXSPTL";
//                        if("test.tencent.month".equals(pList[which])){
//                            request.currency_type = "USD";
//                        }else if("test.tencent.package".equals(pList[which])){
//                            request.offerId = "1450018807";
//                            request.currency_type = "USD";
//                        } else {
//                            request.currency_type = "CNY";
//                        }
                        request.mpInfo.productid = pList[which];

                        //把上次开通成功的参数保存下来
                        ScrollingActivity.OldSku = request.mpInfo.productid;
                        CTIPayAPI.singleton().pay(mActivity, request,PayHelper.this);

                        BottomWM.instance().close();
                    }
                })
                .setCancelable(true)
                .create();

        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
    }

    //替换腾讯视频订阅物品
    private void subUpgradeClick() {
        //新官网查营销活动
        CTIPromotionRequest ctiPromotionRequest = new CTIPromotionRequest();
        List<String> unifiedSkuLists = new ArrayList<>();
        unifiedSkuLists.add("com.levelinfinite.sgameglobal_w_gauto_iee");
        ctiPromotionRequest.unifiedSkuLists = unifiedSkuLists;
        ctiPromotionRequest.paymentMethod = "gwallet";

        CTIPayAPI.singleton().getPromotionInfo(ctiPromotionRequest, new ICTIPromotionCallback(){
            @Override
            public void onPromotionInfoResp(String resp) {
                try {
                    JSONObject json = new JSONObject(resp);
                    String ret = json.getString("retCode");
                    String retMsg = json.getString("retMsg");

                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });
        /*
        final String[] pList = {
                "com.mscti.sub.resurrection"
        };
//        final String[] pList = {
//                "test.tencent.month","com.tencent.pay.bossmonth", "com.tencent.qpaytest.otherautorenew1week","test.tencent.package"
//        };

        AlertDialog dialog = new AlertDialog.Builder(mActivity)
                .setSingleChoiceItems(pList, -1, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        CTIMonthRequest request = new CTIMonthRequest();
                        initRequest(request);
                        request.autoPay = true;
                        request.mpInfo.payChannel = "gwallet";
                        request.basePlanId = "p1w";
                        request.serviceCode = DC.instance().cfg.mServiceCode;
                        request.serviceName = DC.instance().cfg.mServiceName;
//                        request.offerId = "1450013481";
//                        request.mType = "month";
//                        request.serviceCode = "TXSPTL";
//                        if("test.tencent.month".equals(pList[which])){
//                            request.currency_type = "USD";
//                        }else if("test.tencent.package".equals(pList[which])){
//                            request.offerId = "1450018807";
//                            request.currency_type = "USD";
//                        } else {
//                            request.currency_type = "CNY";
//                        }
                        request.mpInfo.productid = pList[which];

                        //升级传递的协议
                        StringBuilder sb = new StringBuilder();
                        sb.append("OldSku=");
                        sb.append(ScrollingActivity.OldSku);
                        sb.append("&");
                        sb.append("SubcribeProrationMode=4");

                        request.extras = sb.toString();
//                        channelExtras="OldSku=&SubcribeProrationMode=4"
                        CTIPayAPI.singleton().pay(mActivity, request,PayHelper.this);

                        BottomWM.instance().close();
                    }
                })
                .setCancelable(true)
                .create();

        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
         */
    }

    /**
     * h5支付
     */
    public void h5Pay(){

       // TestConfig.mWebViewUrl = "https://www.qq.com"; //
        CTIGameRequest request = new CTIGameRequest();
        request.offerId="1460000764";
        request.openId = "5941419346640078797";
        request.country = "hk";
        request.serverId = "66";
        request.extras = "charac_id=54058580653359921202&charac_name=zach&product_id=1680_coins_midasbuy";
//        initRequest(request);
//        if (CTIPayAPI.singleton().getEnv().equals("dev")){
//            request.offerId="1450021702";
//        }else{
//            request.offerId="1460000237";
////            request.offerId="1450015065";
//        }
//
        request.mpInfo.payChannel = "h5_mall";
//        request.openId = "8227068844573992"; //35319745633191208
//        request.extras = "charac_id=123456&charac_name=zach"; //charac_id=123456&charac_name=zach&from=s1a06sq&sc=s1a06sq"
//        request.country = "id";

//        request.mpInfo.payChannel = "os_mp";
//        request.extras = "pay_info={\"data\":{\"app_id\":\"1460000516\",\"res_app_id\":\"\",\"biz_app_id\":\"\",\"transaction_id\":\"E-240122110012426019\",\"reference_id\":\"\",\"transaction\":{\"pay_desc\":\"\",\"pay_channel\":\"os_midaspay\",\"sub_channel\":\"1\",\"currency\":\"HKD\",\"amount\":\"\",\"region\":\"HK\",\"application_context\":null,\"coin_info\":null,\"contract_info\":null,\"sub_channel_name_list\":\"\"},\"buy_info\":{\"login_number\":\"\",\"login_token\":\"\",\"login_type\":1,\"pay_number\":\"\",\"pay_token\":\"\",\"provide_number\":\"5240528299984796896\",\"server_id\":\"\",\"role_id\":\"\",\"platform\":1,\"address\":{\"region\":\"\",\"state_or_province\":\"\",\"city\":\"\",\"street\":\"\",\"house_number_or_name\":\"\",\"postal_code\":\"\"},\"user_ip\":\"***************\",\"provide_app_id\":\"\"},\"purchase_list\":[{\"product_id\":\"com.levelinfinite.sgameglobal_60_ie\",\"quantity\":\"1\",\"product_type\":\"save\",\"product_name\":\"80\\u70b9\\u5238\",\"price\":\"782\",\"provide_app_id\":\"900000935\",\"server_id\":\"108007\",\"role_id\":\"\",\"provide_id\":\"\",\"provide_id_type\":\"\",\"role_name\":\"\",\"area\":\"\",\"partition\":\"\",\"platid\":\"\",\"join_model\":{\"list\":[]},\"real_product_id\":\"\",\"channel_product_id\":\"\"}],\"language\":\"\",\"scene\":\"00000000\",\"encrypt_redirect_url\":{\"version\":\"v1\",\"redirect_url\":\"02BED45E40804FD581DBCC922211378C3941B3F83B0F00127219357E78AEC3A496469717E28EF40F1228DD479C83B5B0552169CE91BF6E082E6770F6B68D43F03EF4B3922EAA2DED6AF25BC412C5194584D5B0459A3F24811EB28237A8B9CF059510D3D97F042A4B30E818978AD51FCBA8296F46734EA7B5D498FA760F4CF516EDEFDAA7614FB1CD2F6BB18E9FF935944E56795ED66690B0FF2CBD1B696C0BD4D7DDF9FF455EEEA58ED004A6DE9BCF1CA9205FD09871D06330251305A5BFCE17AC3EFEEA2EA53D53C763296A94277B5349BCAFFA1BEC1B19916B660349E9AA43B7B8FDBC465FD48D92541EA364E703968B84168095C902F6F2F4603FE364F392425DD024F6D7A3CAFACC11F0CA38957D55FAE6185B07BED5CEBAF927B9B34D6F4E450B468909D58139400CE1738A608BC3C92B820F1359B66C9E943095BA0B961F6A47DC3196F93DFA3B7CBB78AB457844651C1BAD3519079152570AF1D2D07E9266827A8EAA991F8C5A83DD095ED4CF3203CC3360420F485FFB80DA63D3A35F2172E02544E2DA5BD31A5B7AF8A889E92F7C33207E3CAF7D3FA65EFBA98BD84C50378FA5DAAA6579AE97312B7808AD14\",\"msg_len\":\"428\"},\"package_name_validation\":{\"name\":\"com.mscti.game.plane\",\"verify_type\":1}},\"sign\":\"\"}";
//        request.extras = "pay_info={ \"data\": { \"app_id\": \"1450050491\", \"res_app_id\": \"\", \"biz_app_id\": \"800000521\", \"transaction_id\": \"E-231116170270206006\", \"reference_id\": \"\", \"transaction\": { \"pay_desc\": \"\", \"pay_channel\": \"os_midaspay\", \"sub_channel\": \"\", \"currency\": \"USD\", \"amount\": \"\", \"region\": \"US\", \"application_context\": null, \"coin_info\": null, \"contract_info\": null }, \"buy_info\": { \"login_number\": \"\", \"login_token\": \"\", \"login_type\": 1, \"pay_number\": \"\", \"pay_token\": \"\", \"provide_number\": \"uid1459202928\", \"server_id\": \"\", \"role_id\": \"\", \"platform\": 1, \"address\": { \"region\": \"\", \"state_or_province\": \"\", \"city\": \"\", \"street\": \"\", \"house_number_or_name\": \"\", \"postal_code\": \"\" }, \"user_ip\": \"\", \"provide_app_id\": \"\" }, \"purchase_list\": [ { \"product_id\": \"PRO-94G0TVJ3PCSL\", \"quantity\": \"2\", \"product_type\": \"save\", \"product_name\": \"test_dw_111\", \"price\": \"20\", \"provide_app_id\": \"800000558\", \"server_id\": \"1\", \"role_id\": \"test123\", \"provide_id\": \"\", \"provide_id_type\": \"\", \"role_name\": \"\", \"area\": \"\", \"partition\": \"\", \"platid\": \"\", \"join_model\": { \"list\": [] }, \"real_product_id\": \"\", \"channel_product_id\": \"\" } ], \"language\": \"en\", \"scene\": \"xx\", \"encrypt_redirect_url\": { \"version\": \"v1\", \"redirect_url\": \"02BED45E40804FD581DBCC922211378C3941B3F83B0F00127219357E78AEC3A496469717E28EF40F1228DD479C83B5B0552169CE91BF6E082E6770F6B68D43F03EF4B3922EAA2DED6AF25BC412C5194584D5B0459A3F24811EB28237A8B9CF059510D3D97F042A4B30E818978AD51FCBA8296F46734EA7B5D498FA760F4CF516EDEFDAA7614FB1CD2F6BB18E9FF935944E56795ED66690B0FF2CBD1B696C0BD4D7DDF9FF455EEEA58ED004A6DE9BCF1CA9205FD09871D06330251305A5BFCE17AC3EFEEA2EA53D53C763296A94277B5349BCAFFA1BEC1B19916B660349E9AA43B7B8FDBC465FD48D92541EA364E703968B84168095C902F6F2F4603FE364F392425DD024F6D7A3CAFACC11F0CA38957D55FAE6185B07BED5CEBAF927B9B34D6F4E450B468909D58139400CE1738A608BC3C92B820F1359B66C9E943095BA0B961F6A47DC3196F93DFA3B7CBB78AB457844651C1BAD3519079152570AF1D2D07E9266827A8EAA991F8C5A83DD095ED4CF3203CC3360420F485FFB80DA63D3A35F2172E02544E2DA5BD31A5B7AF8A889E92F7C33207E3CAF7D3FA65EFBA98BD84C50378FA5DAAA6579AE97312B7808AD14\" }},\"sign\":\"DA5BD31A5B7AF8A889E92F7\"}"; //charac_id=123456&charac_name=zach&from=s1a06sq&sc=s1a06sq"
        CTIPayAPI.singleton().pay(mActivity, request,PayHelper.this);

    }


    /**
     * doku支付
     */
    public void DoKuPay(){
        CTIMonthRequest request = new CTIMonthRequest();
        initRequest(request);
        request.mpInfo.payChannel = "os_doku";
        request.extras = "pay_channel=31";
        request.serviceCode = DC.instance().cfg.mServiceCode;
        request.serviceName = DC.instance().cfg.mServiceName;
        request.mpInfo.productid = DC.instance().cfg.mSubsProductID;
        CTIPayAPI.singleton().pay(mActivity, request,PayHelper.this);
    }

    private void testQuery() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {

            }
        }, 10);
    }

    private void queryGWInfo(){
        //新官网查物品
        CTIProductRequest ctiProductRequest = new CTIProductRequest();

        List<String> unifiedSkuLists = new ArrayList<>();
        unifiedSkuLists.add("1500050");
//        unifiedSkuLists.add("PRO-Goods-0612-1");
        ctiProductRequest.unifiedSkuLists = unifiedSkuLists;
        ctiProductRequest.paymentMethod = "gwallet";

        CTIPayAPI.singleton().getProductInfo(ctiProductRequest, new ICTIProductInfoCallback(){
            @Override
            public void onProductInfoResp(String resp) {
                try {
                    JSONObject json = new JSONObject(resp);
                    String ret = json.getString("retCode");
                    String retMsg = json.getString("retMsg");
                    CTILog.i(TAG, "retCode = " + ret);
                    CTILog.i(TAG, "retMsg = " + retMsg);
                    CTILog.i(TAG, "resp = " + resp);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });

//        HashMap<String, String> map = new HashMap<String, String>();
//        map.put("com.mscti.core.raward100", "inapp");
//        map.put("PRO-Token-0612-1", "inapp");
//        map.put("PRO-Goods-0612-1", "inapp");
//        map.put("subsweek_autopay_default_pid", "subs");
//        map.put("com.mscti.sub.resurrection", "subs");

//        int i = 1;
//        while (i-- > 0) {

//            CTIPayAPI.singleton().getProductInfo("gwallet",map,new ICTIProductInfoCallback(){
//                @Override
//                public void onProductInfoResp(String resp) {
//                    try {
//                        JSONObject json = new JSONObject(resp);
//                        String ret = json.getString("retCode");
//                        String retMsg = json.getString("retMsg");
//                        CTILog.i(TAG, "retCode = " + ret);
//                        CTILog.i(TAG, "retMsg = " + retMsg);
//                        CTILog.i(TAG, "resp = " + resp);
//                    }catch (Exception e){
//                        e.printStackTrace();
//                    }
//                }
//            });
//        }

    }

    private void queryAmazonProductInfo(){
//        com.proximabeta.re0jp.test980
//com.proximabeta.re0jp.test300
//com.proximabeta.re0jp.test60
        HashMap<String, String>  list = new HashMap<String, String> ();
        list.put("com.proximabeta.re0jp.test60", "inapp");
        list.put("com.proximabeta.re0jp.test300", "inapp");

//        list.put("gamecoins1", "inapp");
//        list.put("goodspackage", "inapp");
        CTIPayAPI.singleton().getProductInfo("os_amazon", list, new ICTIProductInfoCallback() {
            @Override
            public void onProductInfoResp(String resp) {
                try {
                    JSONObject json = new JSONObject(resp);
                    String ret = json.getString("ret");
                    if("0".equals(ret)){
                        showDialog("result",JsonFormat.format(json.toString()));
                    }else {
                        showDialog("result","!!Error");
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });
    }


    private void reProvide(){
        CTIGameRequest request = new CTIGameRequest();
        initRequest(request);
        CTIPayAPI.singleton().reProvide(new ICTIPayUpdateCallBack() {
            @Override
            public void onUpdate(int retCode, String info) {
                Log.i(TAG,"retCode = "+retCode+" info = "+info);
                Toast.makeText(mActivity,"reProvide finished: retCode = "+retCode+" info = "+info
                    ,Toast.LENGTH_SHORT).show();
            }
        });
    }


    private void initRequest(CTIBaseRequest request) {
        request.offerId = DC.instance().cfg.offerid;
        request.openId = DC.instance().cfg.openId;
        request.openKey = "openKey";
        request.sessionId = "hy_gameid";
        request.sessionType = "st_dummy";
        request.zoneId = "1";
        request.goodsZoneId="1001";
        request.pf = "huyu_m-2001-android-2001";
        request.pfKey = "DSSSSSSSSSSS";
        request.country = DC.instance().cfg.mCountry;
        request.currency_type = DC.instance().cfg.mCurrency;
    }

    private void AmazonGameClick() {
        CTIGameRequest request = new CTIGameRequest();
        initRequest(request);
        request.zoneId = "1";
        request.mpInfo.payChannel = "os_amazon";
        request.mpInfo.productid = DC.instance().cfg.amazonGameProductId;
        CTIPayAPI.singleton().pay(mActivity, request, this);
    }

    private void AmazonGoodsClick() {
//        CTIGameRequest request = new CTIGameRequest();
        CTIBaseRequest request = new CTIGoodsRequest();
        initRequest(request);
        request.zoneId = "1";
        request.mpInfo.payChannel = "os_amazon";
        request.mpInfo.productid = DC.instance().cfg.amazonGoodsProductId;
        CTIPayAPI.singleton().pay(mActivity, request, this);
    }

    private void AmazonSubcribeClick() {
//        CTIGameRequest request = new CTIGameRequest();
        CTIBaseRequest request = new CTIMonthRequest();
        initRequest(request);
        request.zoneId = "1";
        request.mpInfo.payChannel = "os_amazon";
        request.mpInfo.productid = DC.instance().cfg.amazonSubcribeProductId;
        CTIPayAPI.singleton().pay(mActivity, request, this);
    }

    private void molAccount(){
        CTIMonthRequest request = new CTIMonthRequest();
        initRequest(request);
        request.mpInfo.payChannel = "mol_acct";
        request.serviceCode = DC.instance().cfg.mServiceCode;
        request.serviceName = DC.instance().cfg.mServiceName;
        request.mpInfo.productid = "com_tencent_ibg_joox_mol_month12";
        CTIPayAPI.singleton().pay(mActivity,request,this);
    }


    /**
     * 商城页
     */
    public void mall(){
        MallParams params = new MallParams.Builder()
                .setPayChannel("gwallet")
                .build();
        CTIPayNewAPI.singleton().mall(mActivity,params,this);
    }


    private void showDialog(String title, String mes) {
        AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(mActivity);
        dialogBuilder.setTitle(title);
        dialogBuilder.setMessage(mes);
        dialogBuilder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.cancel();
            }
        });
        AlertDialog dialog = dialogBuilder.create();
        dialog.show();
    }

    //Midas支付回调1
    @Override
    public void CentauriPayCallBack(CTIResponse responseInfo) {
        CTILog.d(TAG, "risk filed is:" + responseInfo.getExtendJson());
        showDialog(" info:", responseInfo.toString());
        CTILog.d(TAG+" MidasPayCallback",responseInfo.toString());
        try{
            JSONObject js = new JSONObject(responseInfo.getExtra());
            String jInfo = js.optString("info");
            JSONObject _jInfo = new JSONObject(new String(CTIBase64.decode(jInfo)));
            CTILog.d(TAG,"info "+_jInfo.toString());
        }catch (JSONException e){
            CTILog.d(TAG,"MidasPayCallBack Exception: "+e.getMessage());
        }
    }

    //Midas支付回调2
    @Override
    public void CentauriPayNeedLogin() {
        showDialog("info", "login expired,please login again");
    }

    //Midas net回调1
    @Override
    public void CentauriNetFinish(String reqType, String result) {
        Log.i(TAG,"reqType: "+reqType +",result: "+result);
    }

    //Midas net回调2
    @Override
    public void CentauriNetError(String reqType, int resultCode, String resultMsg) {
        Log.i(TAG,"reqType = "+reqType +" resultCode = "+resultCode+" resultMsg = "+resultMsg);
    }

    //Midas net回调3
    @Override
    public void CentauriNetStop(String reqType) {
        Log.i(TAG,"reqType = "+reqType);
    }

    //Midas reProvide回调
    @Override
    public void onUpdate(int retCode, String info) {
        Log.i(TAG,"reprovide retCode = "+retCode+" info = "+info);

        Log.i(TAG,"retCode = "+retCode+" info = "+info);
        Toast.makeText(mActivity,"reProvide finished: retCode = "+retCode+" info = "+info
                ,Toast.LENGTH_SHORT).show();
    }

    //Midas getProductInfo回调
    @Override
    public void onProductInfoResp(String resp) {
        Log.i(TAG,"resp = "+resp);
    }


}
