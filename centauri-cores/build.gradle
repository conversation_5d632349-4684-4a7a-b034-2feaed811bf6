apply plugin: 'com.android.library'

android {
    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 14
        targetSdkVersion 31
        versionCode 30003
        versionName "3.00.03"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        abortOnError false
    }
    /**
     * build aar and rename aar by dongbingliu 2020-10-30 星期五
     */
    setVersion(defaultConfig.versionName)
    libraryVariants.all { variant ->
        if (variant.buildType.name == 'release') {
            variant.outputs.all {
                outputFileName = "centauri-cores-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-${getVersion()}.aar"
            }
        }else{
            variant.outputs.all {
                outputFileName = "centauri-cores-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-${getVersion()}-debug.aar"
            }
        }
    }
//    tasks.register('makeJar', Copy) {
//        //删除存在的
//        delete 'build/libs/myjar.jar'
//        //设置拷贝的文件
//        from('build/intermediates/aar_main_jar/release/')
//        //打进jar包后的文件目录
//        into('build/libs/')
//        //将classes.jar放入build/libs/目录下
//        //include ,exclude参数来设置过滤
//        include('classes.jar')
//        //重命名
//        rename('classes.jar', "centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.jar")
//    }
//    makeJar.dependsOn(build)
}

dependencies {

    implementation fileTree(dir: 'libs', include: '*.jar')
    implementation 'com.android.billingclient:billing:5.1.0'
//    implementation 'com.android.support:appcompat-v7:28.1.1'
}