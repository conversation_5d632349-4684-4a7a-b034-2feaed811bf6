apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        versionCode rootProject.ext.centauriGoogleWalletVersionCode
        versionName rootProject.ext.centauriGoogleWalletVersionName
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
    }

    /**
     * build aar and rename aar by dongbingliu 2020-10-30 星期五
     */
    setVersion(defaultConfig.versionName)
    libraryVariants.all { variant ->
        if (variant.buildType.name == 'release') {
            variant.outputs.all {
                outputFileName = "centauri-vivo-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.aar"
            }
        }else{
            variant.outputs.all {
                outputFileName = "centauri-vivo-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-debug.aar"
            }
        }
    }

   \
    compileOptions {
        sourceCompatibility rootProject.ext.javaCompileVersion
        targetCompatibility rootProject.ext.javaCompileVersion
    }
    lintOptions {
        abortOnError false
    }
//    packageBuildConfig = false
    buildFeatures {
        buildConfig = false
    }

}
repositories {
    flatDir {
        dirs 'libs'
    }
}

task ctiChannelVivoJar(type: Jar, dependsOn: "assembleRelease") {
    //jar package name
    archivesBaseName = "centauri-vivo-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}"
    //路径会跟随gradle版本变更而变化
    from "build/intermediates/javac/release/classes"
    include('com/')

    //依赖的maven仓库包打进一个jar包
//    from {configurations.myConfig.collect { it.isDirectory() ? it : zipTree(it) }}

    exclude('**/test/*.class', '**/sample/*.class')
    exclude "**/R.class"
    exclude "**/R\$*.class"
    exclude('**/BuildConfig.class')

    // copy 至ctiLibs目录,build/libs目录是中间目录,编译时候自动删除不可控
    copy {
        String libDir = file('build/ctiLibs')
        from("build/libs")
        into("build/ctiLibs")
    }
}

dependencies {

    implementation fileTree(dir: 'libs', include: '*.jar')

    compileOnly(name:"vivounionsdk_2.5.7.1_prodRelease", ext:"aar")
    compileOnly project(':Core')
    compileOnly 'com.centauri.comm:centauricommon-log:2.0.3'
    implementation 'com.squareup.okhttp3:okhttp:3.9.0'
    implementation 'com.squareup.okio:okio:1.13.0'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'org.greenrobot:eventbus:3.2.0'
}