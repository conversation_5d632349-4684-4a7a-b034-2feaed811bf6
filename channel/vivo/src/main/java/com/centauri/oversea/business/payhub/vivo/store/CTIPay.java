package com.centauri.oversea.business.payhub.vivo.store;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.pay.CTIPayBaseChannel;
import com.centauri.oversea.business.pay.CTIPayBaseView;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.vivo.unionpay.sdk.open.VivoConstants;
import com.vivo.unionpay.sdk.open.VivoPayCallback;

import org.json.JSONObject;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 05/12/2023
 */
public class CTIPay extends CTIPayBaseChannel {
    private final String TAG = "CTIPay";

    private String appId = "";
    private String openId = "";
    private String productId = "";

    private String channelExtras = "";

    @Override
    public void init(CTIPayBaseView view) {
        super.init(view);
    }

    @Override
    public void startPayCheckEnv() {
        super.startPayCheckEnv();
    }

    @Override
    public void startPay() {
        super.startPay();
    }

    @Override
    public int getOrderKey() {
        return super.getOrderKey();
    }

    @Override
    public void release() {
        super.release();
    }

    @Override
    protected void init() {
        BillingFlowParams request = mModel.getRequest();
        openId = GlobalData.singleton().openID;
//        productId = request.getProductID();
        channelExtras = request.getExtra().getChannelExtras();
//        HashMap<String, String> desParams = CTITools.kv2Map(channelExtras);
//        appId = desParams.get("appId");
//        if (TextUtils.isEmpty(appId)) {
//            reportResult("initerr", "appId null");
//            reportTime("initerr", MTimer.duration(MTimer.GW_PROCESS_INIT));
//            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_ERROR, -1, "");
//            return;
//        }
        BillingHelper.getInstance().init(mView.getActivity());
        super.init();
    }

    @Override
    public void prePay() {
        super.prePay();
    }

    @Override
    public void pay(Activity act, JSONObject obj) {
        super.pay(act, obj);
        String sdkret = "";
        String payInfo = "";
//        CTILog.d(TAG, "pay obj : " + obj.toString());
        try {
            sdkret = URLDecoder.decode(obj.getString("sdkret"), "UTF-8");
            String[] pairs = sdkret.split("&");
            for (String pair : pairs) {
                int idx = pair.indexOf("=");
                if ("PayInfo".equals(pair.substring(0, idx))) {
                    payInfo = pair.substring(idx + 1);
                }
//                CTILog.e(TAG, pair.substring(0, idx) + " = " + pair.substring(idx + 1));
            }
            JSONObject infoObj = new JSONObject(payInfo);
            appId = infoObj.getString("appId");
            productId = infoObj.getString("productId");
            BillingHelper.getInstance().pay(mView.getActivity(), payInfo, appId, new VivoPayCallback() {
                @Override
                public void onVivoPayResult(int i) {
                    onPayResult(i);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            onPayResult(-1);
        }

    }

    private void onPayResult(int status) {

        CTILog.e(TAG, "onPayResult status : " + status);
        String msg = "";
        switch (status) {
            case VivoConstants.VIVO_PAY_SUCCESS:
                sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC, status, "");
//                mLogView.printD(Constants.TIPS_PAY_SUCCESS);
                break;
            case VivoConstants.VIVO_PAY_FAILED:
                msg = "VIVO_PAY_FAILED";
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, status, msg);
                break;
            case VivoConstants.VIVO_PAY_INVALID_PARAM:
                msg = "VIVO_PAY_INVALID_PARAM";
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, status, msg);
                break;
            case VivoConstants.VIVO_PAY_ERROR:
                msg = "VIVO_PAY_ERROR";
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, status, msg);
                break;
            case VivoConstants.VIVO_PAY_OVER_TIME:
                msg = "VIVO_PAY_OVER_TIME";
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, status, msg);
                break;
            case VivoConstants.VIVO_PAY_CANCEL:
                msg = "VIVO_PAY_CANCEL";
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, status, msg);
                break;
            default:
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, status, msg);
                break;
        }
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_VIVO_SDK_RESPONSE,
                "name=onSignResp&result=" + status +
                        "&msg=" + msg);
    }

    @Override
    public void postPay() {
        super.postPay();
    }

    @Override
    protected JSONObject addChannelExtra(JSONObject in) {
        return super.addChannelExtra(in);
    }

    @Override
    protected String getProductType() {
        return super.getProductType();
    }

    @Override
    protected boolean hasGoodsList() {
        return super.hasGoodsList();
    }


    @Override
    protected void onSaveReceipt(CTIPayReceipt receipt) {
        super.onSaveReceipt(receipt);
    }

    @Override
    protected boolean needShowSucc() {
        return super.needShowSucc();
    }

    @Override
    protected boolean isSdkProvide() {
        return super.isSdkProvide();
    }

    @Override
    public boolean handleActivityResult(int requestCode, int resultCode, Intent data) {
        return super.handleActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void dispose() {
        super.dispose();
    }

    private void sendMesUIH(int what, int arg1, Object obj) {
        if (UIHandler != null) {
            Message msg = new Message();
            msg.what = what;
            msg.arg1 = arg1;
            msg.obj = obj;
            Bundle bundle = new Bundle();
            // resultCode -> InnerCode
            bundle.putString("msgErrCode", String.valueOf(arg1));
            msg.setData(bundle);
            UIHandler.sendMessage(msg);
        }
    }


    private void reportTime(String name, long times) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_VIVO_TIME,
                "version=2.0&name=" + name
                        + "&times=" + times);
    }

    private void reportResult(String name, String result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_VIVO_RESULT,
                "version=2.0&name=" + name
                        + "&result=" + result);
    }
}
