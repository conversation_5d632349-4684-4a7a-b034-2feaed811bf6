package com.centauri.oversea.business.payhub.vivo.store;

import android.app.Activity;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import com.centauri.comm.CTILog;
import com.vivo.unionpay.sdk.open.VivoPayCallback;
import com.vivo.unionpay.sdk.open.VivoPayInfo;
import com.vivo.unionpay.sdk.open.VivoUnionSDK;

import org.json.JSONObject;

import java.util.Objects;


public class BillingHelper {

    private static final String TAG = "BillingHelper";
    private static BillingHelper sInstance;
    private String appId;

    private BillingHelper() {

    }

    public static BillingHelper getInstance() {
        if (sInstance == null) {
            synchronized (BillingHelper.class) {
                if (sInstance == null) {
                    sInstance = new BillingHelper();
                }
            }
        }
        return sInstance;
    }


    public void init(Activity context) {
        CTILog.e(TAG, "vivo init");
        try {
            PackageManager pm = context.getPackageManager();
            ApplicationInfo ai = pm.getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
            appId = Objects.requireNonNull(ai.metaData.get("vivoAppId")).toString();
            CTILog.e(TAG, "vivo appId : " +appId);
            VivoUnionSDK.initSdk(context, appId + "");
        } catch (Exception e) {
            CTILog.e(TAG, e.getMessage());
            e.printStackTrace();
        }
    }

    public void pay(Activity context, String payInfoJson, String appid, VivoPayCallback callback) {
//        try {
//            appId = appid;
//            VivoUnionSDK.initSdk(context, appid);
//        } catch (Exception e) {
//            Log.e("DemoApplication", e.getMessage());
//        }
        VivoPayInfo payInfo;
        payInfo = createPayInfoByProductId(payInfoJson);
        VivoUnionSDK.pay(context, payInfo, callback);
    }



    // order by productId(推荐,收银台根据开发者平台美元价格自动换算本地价格)
    public VivoPayInfo createPayInfoByProductId(String payInfoJson) {


//        VivoRoleInfo roleInfo = new VivoRoleInfo();
//        roleInfo.setServiceAreaName(serviceName);
//        roleInfo.setRoleName(roleName);
//        roleInfo.setServiceAreaId(serviceId);
//        roleInfo.setRoleId(roleId);
//        roleInfo.setRoleGrade(rolegrade);
        String sign = "";
        String orderId = "";
        String signType = "";
        String notifyUrl = "";
        String partnerOpenid = "";
        String productId = "";
        String expireTime = "";
        String extInfo = "";
        String bizContent = "";
//        CTILog.e(TAG, "pay obj : " + payInfoJson);
        String timestamp = "";
        try {
//            sdkret = URLDecoder.decode(obj.getString("sdkret"), "UTF-8");
            CTILog.e(TAG, "pay payInfoJson : " + payInfoJson);

            JSONObject infoObj = new JSONObject(payInfoJson);
            sign = infoObj.getString("sign");
            orderId = infoObj.getString("partnerOrderId");
            appId = infoObj.getString("appId");
            productId = infoObj.getString("productId");
            notifyUrl = infoObj.getString("notifyUrl");
//            partnerOpenid = infoObj.getString("partnerOpenid");
            expireTime = infoObj.getString("expireTime");
            extInfo = infoObj.getString("extInfo");
            bizContent = infoObj.getString("bizContent");
            signType = infoObj.getString("signType");
            timestamp = infoObj.getString("timestamp");
        } catch (Exception e) {
            e.printStackTrace();
        }


//        CTILog.e(TAG, "sign sign : " + sign);
        //RSA签名调用getVivoRsaSign(推荐)
//        String sign1 = VivoSignUtils.getVivoRsaSign(params, VivoSignUtils.key);
        VivoPayInfo vivoPayInfo = null;
        vivoPayInfo = new VivoPayInfo.Builder()
                .setAppId(appId)
                .setProductId(productId)
                .setTransNo(orderId)
                .setExpireTime(expireTime)
                .setExtInfo(extInfo)
                .setNotifyUrl(notifyUrl)
                .setSign(sign)
                .setSignType(signType) // RSA签名(推荐)
                .setAfterUnified("0")
                .setBizContent(bizContent)
                .setMethod("exvivopay.app.trade.create")
                .setVersion("1.0.0")
                .setTimestamp(timestamp)
                //                .setSignType(Constants.SIGN_TYPE_MD5) // MD5签名
                //                .setVivoRoleInfo(roleInfo)
                .build();

        return vivoPayInfo;
    }
}
