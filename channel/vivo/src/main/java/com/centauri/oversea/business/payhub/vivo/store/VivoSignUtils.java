/**
 * 验签工具类，调用getVivoSign可生成验签
 */

package com.centauri.oversea.business.payhub.vivo.store;

import android.util.Base64;


import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 签名工具类，推荐服务端处理签名，参考文档3.4节
 */
public class VivoSignUtils {

    private static final String TAG = "VivoSignUtils";

    private static final char[] HEX_CHAR = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e',
            'f' };
    public static final String SIGN_ALGORITHMS = "SHA256WithRSA";
    private static final String CHARSET_UTF8 = "UTF-8";

    private final static String KEY_SIGN = "sign";
    private final static String KEY_SIGN_TYPE = "signType";

    private static final String QSTRING_EQUAL = "=";
    private static final String QSTRING_SPLIT = "&";

    public static String key = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCN9OnqYlYC1GhldYUDjO7TOSa/wgE9fsQa/5A65KfsUQ4Y+aJgWX5FfX7hDKylcidQO+RmFtvQRwyFClspC/uCnlPrxDuPo5VHV6fANNI/i6yj6H0oj8XCfCkj8YZrGP7T7r/xLLLGcMNwvVGl2ny3yj+T/BEp8Izum9JzrSevJxnXy//nG1WznkiqMEH9zFZDbeSq4x4QoIuXbXXM088tuGr99a6NWUzKKtVoCtzB0cJZT2DBeQ31cjL+91jdWSigt/P2wvVICfJMbXe3Wyg6b4u6SRAY501XItWfL9VrGIFv2Z2pDKtNgDV3wurNnmTqFUBsWDniJZhPikyXWEchAgMBAAECggEAODYV9jDQPDidyic574j7/PnxhRcuZIpw3vv5wchPUj1aZrta9+oYHS4vcrARIUZNmPDOZTqB+jxYEcpjBKLxCezl8EL5DszIzMJB89M2ueD2EMV74fbkaTm/FLOL9ZalOYidysNXW0rFqTveO92Da0eT22aB6WiOdeGP+SxEvY8XXgjAon3HnPOefnBGTs31IGTENw9BxvRFXgq/hACa8vB6iPm5Ltsr2P8Q8u2oLrJOuBerAWHTcuDsu6B1RAdnTkq6AOqmOtGmoTpo3ApPqk6deTJLPFyo6NQ5X5IDIBxiAAzkLX/336bY2Ag8Qqj4cPeELHqlX1SUBivW03wq0QKBgQDTXx8f/ydqvdW9ycP3AHeD7XWYv6vwVOJ9qjBTtyC9VVHua/5fbZk3CPs5yjbRiyHSLGpqKlZpQmVs9pw3Crs9X+ZoLwhO4LcdfZpwl9q1Lt0ZZyUackFtx5fROC/UGoTo9RAFVEUc1wereYexy9E3GEYAhVafZMMt2smWn1QTVQKBgQCr7dZyq9zQRMw1MN9O+WIAaRvLNJCsWGTiQJ1PFJBYpioyzpecEDW5mLY2fjm9Bayjpo1usXI+YBVtxG9x1zoaqonnwzluJnREse4bDiXiUsNeNmCN77oc8bc1kfydWqS4P+z1OT7d7VL1lsx34s3eVwYEbYiYF8cuQ4x8pD68nQKBgApXDui7zKvWGYytQB7nMPGNPkF4ruqU3HeVScPuo3VfpSBqvAjjDam5kMCk+cWKbt8fOR10cMG5c8i+z6mJpts7w8O4+UkSiSKyMEntw881Oa+ycgg3Kqg6KV0cyIoxuerDgkMgejkM890Zlt/FvrhZQaNo4nHttfaaS15I8Q35AoGAMzFcW0SlOTZi/6uPsno7Hhgt+HGsakTftSa8MGHVITBI/FvbdNK2zI6WIt+u3YOlBg1/q2gWtk/x0X9eF62YVXxWF7ZfkVbN4+5/EXtC+ILgPVs/oMOyzQ+EF+ViLJBi36HZMqNa/sW9n/bI5yGXXw7WcdcThwX9ft8KI8rcC1kCgYBy1m+gzsLg7pDA7bFaK0ZugVnLfulCje8ZPRIUpfGOk0ug67uBbFGFegIFlDjokRvEI1uxw1hI7W8sjd2X35/LxySaHicycxuCladcoxH3sVERWZs917IfyIrf7gnNO08avySCtYAd7iLiyld8k6LRh3+/9EUs1CZgNpNxVlo+MA==";
    public static String pKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjfTp6mJWAtRoZXWFA4zu0zkmv8IBPX7EGv+QOuSn7FEOGPmiYFl+RX1+4QyspXInUDvkZhbb0EcMhQpbKQv7gp5T68Q7j6OVR1enwDTSP4uso+h9KI/FwnwpI/GGaxj+0+6/8SyyxnDDcL1Rpdp8t8o/k/wRKfCM7pvSc60nrycZ18v/5xtVs55IqjBB/cxWQ23kquMeEKCLl211zNPPLbhq/fWujVlMyirVaArcwdHCWU9gwXkN9XIy/vdY3VkooLfz9sL1SAnyTG13t1soOm+LukkQGOdNVyLVny/VaxiBb9mdqQyrTYA1d8LqzZ5k6hVAbFg54iWYT4pMl1hHIQIDAQAB";

    /**
     * 验签
     *
     * @param para
     *            参数集
     * @param key
     *            密钥（AppSecret）
     * @return 验签结果
     */
    public static boolean verifySignature(Map<String, String> para, String key) {

        // 除去数组中的空值和签名参数
        Map<String, String> filteredReq = paraFilter(para);
        // 根据参数获取vivo签名
        String signature = getVivoSign(filteredReq, key);
        // 获取参数中的签名值
        String respSignature = para.get(KEY_SIGN);
        // 对比签名值
        if (null != respSignature && respSignature.equals(signature)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取md5签名
     *
     * @param para
     *            参与签名的要素<key,value>
     * @param key
     *            密钥(AppSecret)
     * @return 签名结果
     */
    public static String getVivoSign(Map<String, String> para, String key) {

        // 除去数组中的空值和签名参数
        Map<String, String> filteredReq = paraFilter(para);
        String prestr = createLinkString(filteredReq, true, false); // 得到待签名字符串
        // 需要对map进行sort，不需要对value进行URL编码
        prestr = prestr + QSTRING_SPLIT + md5Hex(key);
        return md5Hex(prestr);
    }

    /**
     * 获取RSA签名
     *
     * @param para
     *            参与签名的要素
     * @param key
     *            RSA密钥
     * @return签名结果
     */
    public static String getVivoRsaSign(Map<String, String> para, String key) {

        Map<String, String> filteredReq = paraFilter(para);
        String prestr = createLinkString(filteredReq, true, false); // 得到待签名字符串
        return sign(prestr, key, CHARSET_UTF8);
    }

    /**
     * 除去请求要素中的空值和签名参数
     *
     * @param para
     *            请求要素
     * @return 去掉空值与签名参数后的请求要素
     */
    private static Map<String, String> paraFilter(Map<String, String> para) {

        Map<String, String> result = new HashMap<String, String>();

        if (para == null || para.size() <= 0) {
            return result;
        }

        for (String key : para.keySet()) {
            String value = para.get(key);
            if (value == null || value.equals("") || key.equalsIgnoreCase(KEY_SIGN)
                    || key.equalsIgnoreCase(KEY_SIGN_TYPE)) {
                continue;
            }
            result.put(key, value);
        }

        return result;
    }

    /**
     * 把请求要素按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param para
     *            请求要素
     * @param sort
     *            是否需要根据key值作升序排列
     * @param encode
     *            是否需要URL编码
     * @return 拼接成的字符串
     */
    private static String createLinkString(Map<String, String> para, boolean sort, boolean encode) {

        List<String> keys = new ArrayList<String>(para.keySet());

        if (sort)
            Collections.sort(keys);

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = para.get(key);

            if (encode) {
                try {
                    value = URLEncoder.encode(value, "utf-8");
                } catch (UnsupportedEncodingException e) {
                }
            }

            if (i == keys.size() - 1) {// 拼接时，不包括最后一个&字符
                sb.append(key).append(QSTRING_EQUAL).append(value);
            } else {
                sb.append(key).append(QSTRING_EQUAL).append(value).append(QSTRING_SPLIT);
            }
        }

        return sb.toString();
    }

    private static String md5Hex(String data) {

        if (data == null || data.trim().equals("")) {
            return "";
        }
        try {
            MessageDigest digest = MessageDigest.getInstance("md5");
            byte[] md5Data = digest.digest(data.getBytes(CHARSET_UTF8));
            return byteToHexString(md5Data);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String byteToHexString(byte[] data) {

        char[] buf = new char[data.length * 2];
        int index = 0;
        for (byte b : data) { // 利用位运算进行转换
            buf[index++] = HEX_CHAR[b >>> 4 & 0xf];
            buf[index++] = HEX_CHAR[b & 0xf];
        }
        return new String(buf);
    }

    public static String sign(String content, String privateKey, String encode) {

        try {
            PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(Base64.decode(privateKey, Base64.DEFAULT));
            KeyFactory keyf = KeyFactory.getInstance("RSA");
            PrivateKey priKey = keyf.generatePrivate(priPKCS8);
            java.security.Signature signature = java.security.Signature.getInstance(SIGN_ALGORITHMS);

            signature.initSign(priKey);
            signature.update(content.getBytes(encode));
            byte[] signed = signature.sign();

            return replaceBlank(Base64.encodeToString(signed, Base64.DEFAULT));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static String replaceBlank(String src) {

        String dest = "";
        if (src != null) {
            Pattern pattern = Pattern.compile("\t|\r|\n|\\s*");
            Matcher matcher = pattern.matcher(src);
            dest = matcher.replaceAll("");
        }
        return dest;
    }

}
