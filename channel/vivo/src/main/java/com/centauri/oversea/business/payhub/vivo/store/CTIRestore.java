package com.centauri.oversea.business.payhub.vivo.store;

import android.content.Context;

import com.centauri.oversea.business.CTIBaseRestore;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.ICallback;
import com.centauri.oversea.newapi.response.IGetPurchaseCallback;

import java.util.List;

public class CTIRestore extends CTIBaseRestore {
    public final static String TAG = "APRestore_New";

    @Override
    public void init(Context context, ICallback callback) {
        BillingHelper.getInstance().init(CTITools.getCurrentActivity());
    }

    @Override
    public void getPurchaseList(Context context, IGetPurchaseCallback callback) {
    }

    @Override
    public void postProvide(List<String> list, ICTICallback callback) {
    }

    @Override
    public void getInAppMessages(Context context, ICallback callback) {

    }

    @Override
    public void dispose() {
        super.dispose();
    }

}
