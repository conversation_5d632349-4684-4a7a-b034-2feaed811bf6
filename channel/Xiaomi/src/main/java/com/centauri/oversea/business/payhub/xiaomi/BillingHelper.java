package com.centauri.oversea.business.payhub.xiaomi;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.centauri.comm.CTILog;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.MRetCode;
import com.xiaomi.billingclient.api.BillingClient;
import com.xiaomi.billingclient.api.BillingClientStateListener;
import com.xiaomi.billingclient.api.BillingFlowParams;
import com.xiaomi.billingclient.api.BillingResult;
import com.xiaomi.billingclient.api.ConsumeResponseListener;
import com.xiaomi.billingclient.api.Purchase;
import com.xiaomi.billingclient.api.PurchasesResponseListener;
import com.xiaomi.billingclient.api.PurchasesUpdatedListener;
import com.xiaomi.billingclient.api.SkuDetails;
import com.xiaomi.billingclient.api.SkuDetailsParams;
import com.xiaomi.billingclient.api.SkuDetailsResponseListener;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public class BillingHelper implements PurchasesUpdatedListener {
    public static final String TAG = "BillingHelper";

    private static final long RECONNECT_TIMER_START_MILLISECONDS = 1L * 500L;
    private static final long RECONNECT_TIMER_MAX_TIME_MILLISECONDS = 1000L * 100L;
    private static final long SKU_DETAILS_REQUERY_TIME = 1000L * 60L * 60L * 4L; // 4 hours
    private long reconnectMilliseconds = RECONNECT_TIMER_START_MILLISECONDS;
    private final static long SKU_LIMIT_TIME = 1000 * 60 * 5;
    
    private static final Handler mHandler = new Handler(Looper.getMainLooper());

    private BillingClient mBillingClient;
    private OnIabPurchaseListener mPurchaseListener;

    private boolean mIsServiceConnected = false;
    private static BillingHelper sInstance;
    private Context mContext;

    private IabRunnable iabRunnable;

    private BillingHelper(Context context) {
        mContext = context.getApplicationContext();
        mBillingClient = BillingClient.newBuilder(CTITools.getCurrentActivity())
                .setListener(this)
                .build();
//        mBillingClient.setScreenOrientation(BillingClient.OrientationType.PORTRAIT);
    }


    public static BillingHelper getInstance(Context context) {
        if (sInstance == null) {
            synchronized (BillingHelper.class) {
                if (sInstance == null) {
                    sInstance = new BillingHelper(context);
                }
            }
        }
        return sInstance;
    }

    /**
     * connect to billing service
     *
     * @param listener
     */
    public void startSetup(final OnIabSetupFinishedListener listener) {
        Log.i(TAG, "startSetup");
        startServiceConnection(new IabRunnable() {
            @Override
            public void run(BillingResult result) {
                if (listener != null) {
                    listener.onIabSetupFinished(new WrapperBillingResult(result));
                }
            }
        });
    }

    /**
     * start a purchase or subscription replace flow
     *
     * @param billingFlowParams
     */
    public void launchPurchaseFlow(final Activity activity, final BillingFlowParams billingFlowParams, final OnIabPurchaseListener listener) {
        Log.i(TAG, "launchPurchaseFlow");

        if (listener == null) {
            Log.e(TAG, "launchPurchaseFlow: listener is null.");
            return;
        }

        //cached
        mPurchaseListener = listener;

        if (mBillingClient != null) {
            mBillingClient.launchBillingFlow(activity, billingFlowParams);
        } else {
            Log.e(TAG, "launchPurchaseFlow: BillingClient is null.");
            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW_SDK_INNER_ERROR, "version=2.0&ret=-1&msg=" + "billclient is null");
            BillingResult billingResult = BillingResult.newBuilder()
                    .setResponseCode(MRetCode.ERR_GW_UNKNOW)
                    .setDebugMessage("BillingClient is null.")
                    .build();

            listener.onPurchaseResponse(new WrapperBillingResult(billingResult), null);
        }
    }
    /**
     * query sku details
     *
     * @param skuDetailsParams
     * @param listener
     */
    public void querySkuDetailsAsync(final SkuDetailsParams skuDetailsParams, final OnIabQuerySkuDetailsListener listener) {
        Log.i(TAG, "querySkuDetailsAsync");

        if (listener == null) {
            Log.e(TAG, "querySkuDetailsAsync: listener is null.");
            return;
        }

        if (mBillingClient != null) {
            try {
                mBillingClient.querySkuDetailsAsync(skuDetailsParams, new SkuDetailsResponseListener() {
                    @Override
                    public void onSkuDetailsResponse(BillingResult billingResult, List<SkuDetails> skuDetailsList) {
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                Log.i(TAG, "onSkuDetailsResponse ---- ");
                                listener.onSkuDetailsResponse(new WrapperBillingResult(billingResult), skuDetailsList);
                            }
                        });
                    }
                });
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            }

        } else {
            Log.e(TAG, "querySkuDetailsAsync: BillingClient is null.");

            BillingResult billingResult = BillingResult.newBuilder()
                    .setResponseCode(MRetCode.ERR_GW_UNKNOW)
                    .setDebugMessage("BillingClient is null.")
                    .build();
            listener.onSkuDetailsResponse(new WrapperBillingResult(billingResult), null);
        }
    }
    /**
     * AIDL Connect
     *
     * @param iabRunnable
     */
    private void startServiceConnection(IabRunnable iabRunnable) {
        this.iabRunnable = iabRunnable;
        CTILog.i(TAG, "startServiceConnection");

//        if (mBillingClient.getConnectionState() == BillingClient.ConnectionState.CONNECTED) {
//            CTILog.i(TAG, "Service is connected.");
//            BillingResult result = BillingResult.newBuilder()
//                    .setResponseCode(BillingClient.BillingResponseCode.OK)
//                    .build();
//            if (null != this.iabRunnable) {
//                this.iabRunnable.run(result);
//                this.iabRunnable = null;
//            }
//            return;
//        }

        if (mBillingClient != null) {
            mBillingClient.startConnection(billingClientStateListener);
        } else {
            CTILog.e(TAG, "startServiceConnection: BillingClient is null.");
            BillingResult result = BillingResult.newBuilder()
                    .setResponseCode(MRetCode.ERR_GW_UNKNOW)
                    .setDebugMessage("BillingClient is null.")
                    .build();
            if (null != this.iabRunnable) {
                this.iabRunnable.run(result);
                this.iabRunnable = null;
            }
        }
    }

//    private final PurchasesUpdatedListener purchasesUpdatedListener = new PurchasesUpdatedListener() {
//        @Override
//        public void onPurchasesUpdated(BillingResult billingResult, List<Purchase> list) {
//            int code = billingResult.getResponseCode();
//            Log.d("TAG", "onPurchasesUpdated.code = " + code);
//            if (code == BillingClient.BillingResponseCode.PAYMENT_SHOW_DIALOG) {//可根据自己业务需要处理该状态，不可当做交易失败处理
//                resultTv.setText("收银台启动");
//            } else if (code == BillingClient.BillingResponseCode.OK) {
//                Purchase p = list.get(0);
//                resultTv.setText("支付成功" + p.getPurchaseToken());
//                Log.e("TAG", "pay success，purchaseToken: "+p.getPurchaseToken());
//                purchasesList = list;
//                purchaseListAdapter.updateList(list);
//            } else if (code == BillingClient.BillingResponseCode.USER_CANCELED) {
//                resultTv.setText("支付取消");
//            } else {//其他失败状态  比如断网、服务异常等
//                resultTv.setText(billingResult.getDebugMessage());
//            }
//        }
//    };

    BillingClientStateListener billingClientStateListener = new BillingClientStateListener() {
        @Override
        public void onBillingSetupFinished(BillingResult billingResult) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    int billingResponseCode = billingResult.getResponseCode();
                    String debugMessage = billingResult.getDebugMessage();
                    CTILog.d(TAG, "onBillingSetupFinished: " + billingResponseCode + " " + debugMessage);

                    switch (billingResponseCode) {
                        case BillingClient.BillingResponseCode.OK:
                            // The billing client is ready. You can query purchases here.
                            // This doesn't mean that your app is set up correctly in the console -- it just
                            // means that you have a connection to the Billing service.
                            if (billingResponseCode == BillingClient.BillingResponseCode.OK) {
                                mIsServiceConnected = true;
                            }
                            if (null != iabRunnable) {
                                iabRunnable.run(billingResult);
                                iabRunnable = null;
                            }
                            reconnectMilliseconds = RECONNECT_TIMER_START_MILLISECONDS;
                            mIsServiceConnected = true;
                            break;
                        default:
                            BillingResult result = BillingResult.newBuilder()
                                    .setResponseCode(BillingClient.BillingResponseCode.SERVICE_DISCONNECTED)
                                    .build();
                            if (null != iabRunnable) {
                                iabRunnable.run(result);
                                iabRunnable = null;
                            }

                            retryBillingServiceConnectionWithExponentialBackoff();
                            break;
                    }
                }
            });
        }

        @Override
        public void onBillingServiceDisconnected() {
            mIsServiceConnected = false;

            retryBillingServiceConnectionWithExponentialBackoff();
        }

    };

    /********************************* onPurchasesUpdated *************************************/

    /**
     * purchased callback
     * with pending transaction,this will be called multiply times!
     *
     * @param billingResult
     * @param purchases
     */
    @Override
    public void onPurchasesUpdated(BillingResult billingResult, List<Purchase> purchases) {
        Log.i(TAG, "onPurchasesUpdated");
        if (mPurchaseListener != null) {
            mPurchaseListener.onPurchaseResponse(new WrapperBillingResult(billingResult), purchases);
        }
    }

    /**
     * Retries the billing service connection with exponential backoff, maxing out at the time
     * specified by RECONNECT_TIMER_MAX_TIME_MILLISECONDS.
     */
    private void retryBillingServiceConnectionWithExponentialBackoff() {
        Log.d(TAG, "retryBillingServiceConnectionWithExponentialBackoff");
        if (reconnectMilliseconds >= RECONNECT_TIMER_MAX_TIME_MILLISECONDS) {
            return;
        }
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
//                if (mBillingClient.getConnectionState() == BillingClient.ConnectionState.CONNECTED) {
//                    return;
//                }
                startServiceConnection(new IabRunnable() {
                    @Override
                    public void run(BillingResult result) {
                        CTILog.d(TAG, "retryBillingServiceConnectionWithExponentialBackoff IabRunnable result");

                    }
                });
            }
        }, reconnectMilliseconds);
        // 重连次数现在
        reconnectMilliseconds = Math.min(reconnectMilliseconds * 2,
                RECONNECT_TIMER_MAX_TIME_MILLISECONDS);
    }

    /**
     * query purchases,block till the result back
     *
     * @return
     */
    public void queryPurchasesAsync(final OnIabQueryPurchasesListener listener) {
        Log.i(TAG, "queryPurchasesAsync");

        if (listener == null) {
            Log.e(TAG, "queryPurchasesAsync: listener is null.");
            return;
        }

        if (mBillingClient != null) {
            ArrayList<Purchase> resultList = new ArrayList<Purchase>();
            final int[] count = {0};
            mBillingClient.queryPurchasesAsync(BillingClient.SkuType.INAPP, new PurchasesResponseListener() {
                @Override
                public void onQueryPurchasesResponse(BillingResult billingResult, List<Purchase> list) {
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            Log.i(TAG, "onQueryPurchasesResponse INAPP : ");
                            //add to result list
                            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                                if (list != null && !list.isEmpty()) {
                                    resultList.addAll(list);
                                }
                            } else {
                                Log.e(TAG, "queryPurchasesAsync: Get an error response trying to query in-app purchases.");
                            }
                            Log.i(TAG, "onQueryPurchasesResponse INAPP ");
                            //callback
                            listener.onQueryPurchasesResponse(new WrapperBillingResult(billingResult), resultList);
                        }
                    });
                }
            });
        } else {
            Log.e(TAG, "queryPurchasesAsync: BillingClient is null.");

            BillingResult billingResult = BillingResult.newBuilder()
                    .setResponseCode(MRetCode.ERR_GW_UNKNOW)
                    .setDebugMessage("BillingClient is null.")
                    .build();
            listener.onQueryPurchasesResponse(new WrapperBillingResult(billingResult), null);
        }
    }

    /**
     * consume special purchase token
     * if have more than one purchase,this api will be called multiply times.
     *
     * @param params
     * @param listener
     */

    public void consumeAsync(final String params, final OnIabConsumeListener listener) {
        Log.i(TAG, "consumeAsync");

        if (listener == null) {
            Log.e(TAG, "consumeAsync: listener is null.");
            return;
        }

        if (mBillingClient != null) {
            mBillingClient.consumeAsync(params, new ConsumeResponseListener() {
                @Override
                public void onConsumeResponse(BillingResult billingResult, String purchaseToken) {
                    listener.onConsumeResponse(new WrapperBillingResult(billingResult), purchaseToken);
                }
            });
        } else {
            Log.e(TAG, "consumeAsync: BillingClient is null.");
            BillingResult billingResult = BillingResult.newBuilder()
                    .setResponseCode(MRetCode.ERR_GW_UNKNOW)
                    .setDebugMessage("BillingClient is null.")
                    .build();
            listener.onConsumeResponse(new WrapperBillingResult(billingResult), params);
        }

    }
    /********************************* API Callback *************************************/

    interface IabRunnable {
        void run(BillingResult result);
    }

    //set up callback
    public interface OnIabSetupFinishedListener {
        void onIabSetupFinished(WrapperBillingResult result);
    }

    //query purchases callback
    public interface OnIabQueryPurchasesListener {
        void onQueryPurchasesResponse(WrapperBillingResult result, List<Purchase> purchasesList);
    }


    //query sku details callback
    public interface OnIabQuerySkuDetailsListener {
        void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList);
    }

    //pay callback
    public interface OnIabPurchaseListener {
        void onPurchaseResponse(WrapperBillingResult result, List<Purchase> purchasesList);
    }

    //consume callback
    public interface OnIabConsumeListener {
        void onConsumeResponse(WrapperBillingResult billingResult, String purchaseToken);
    }
}
