package com.centauri.oversea.business.payhub.xiaomi;

import android.app.Activity;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.pay.CTIPayBaseChannel;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTIMD5;
import com.centauri.oversea.comm.CTISPTools;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.CTIPayInfo;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.xiaomi.billingclient.api.BillingClient;
import com.xiaomi.billingclient.api.Purchase;
import com.xiaomi.billingclient.api.SkuDetails;
import com.xiaomi.billingclient.api.SkuDetailsParams;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;

public class CTIPay extends CTIPayBaseChannel
        implements BillingHelper.OnIabSetupFinishedListener, BillingHelper.OnIabQueryPurchasesListener,
        BillingHelper.OnIabQuerySkuDetailsListener, BillingHelper.OnIabPurchaseListener, BillingHelper.OnIabConsumeListener {
    private final String TAG = "APPay_New";

    private String openId = "";
    private String productId = "";
    //whether is subscription
    private boolean isAutoPay = true;

    //centauri product type
    private String centauriType = "";
    //google product type
    private String xiaomiType = BillingClient.SkuType.INAPP;
    //purchase sku details
    private SkuDetails mSkuDetails;         // 支付前拉到的物品
    private SkuDetails mSecondSkuDetails;   // 支付后拉到的物品
    private int waitSecondSkuDetailsTime = 0;   //第二次拉物品信息的等待时间，由下单返回
    //purchase receipt
    private Purchase mPurchase;
    private Purchase mOldPurchase;

    //Support Google subscription upgrade mode, the default is 4
    private String OldSku = "";

    private BillingHelper billingHelper = null;

    @Override
    protected void init() {
        MTimer.start(MTimer.GW_PROCESS_INIT);

        BillingFlowParams request = mModel.getRequest();
        openId = GlobalData.singleton().openID;
        productId = request.getProductID();
        isAutoPay = request.isAutoPay();
        centauriType = request.getType();
        xiaomiType = BillingClient.SkuType.INAPP;
        CTILog.i(TAG, "productId:" + productId + ",googleType: " + xiaomiType + ",centauriType:"+centauriType);

        billingHelper = BillingHelper.getInstance(mView.getActivity());
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=startSetup");
        billingHelper.startSetup(this);
    }

    @Override
    public void prePay() {
        MTimer.start(MTimer.GW_PROCESS_PREPAY);
        //query purchases first
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=queryPurchasesAsync");
        if (null != billingHelper) {
            billingHelper.queryPurchasesAsync(this);
        }
    }

    //query sku details
    private void querySkuDetails() {
        ArrayList<String> skuList = new ArrayList<String>();
        skuList.add(productId);

        SkuDetailsParams.Builder inAppParams = SkuDetailsParams.newBuilder();
        inAppParams.setSkusList(skuList).setType(xiaomiType);
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=querySkuDetailsAsync");
        if (null != billingHelper) {
            billingHelper.querySkuDetailsAsync(inAppParams.build(), this);
        }
    }

    @Override
    public void pay(Activity act, JSONObject obj) {
//        if (obj == null ||
//                TextUtils.isEmpty(obj.optString("channel_key"))) {
//            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
//                    0,
//                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
//            reportResult("payerr", "pay info error");
//            return;
//        }
        CTILog.d(TAG, "pay user_name============" + GlobalData.singleton().getUserName());

        MTimer.start(MTimer.GW_PROCESS_PAY);
        MTimer.start(MTimer.GW_PROCESS_SHOW_DIALOG);


        com.xiaomi.billingclient.api.BillingFlowParams.Builder billBuilder = com.xiaomi.billingclient.api.BillingFlowParams.newBuilder();
        billBuilder.setSkuDetails(mSkuDetails);
        billBuilder.setObfuscatedAccountId(GlobalData.singleton().openID);

        try {
            JSONObject profileIdObject = new JSONObject();
            profileIdObject.put("bill_no", (mModel == null ? "" : mModel.getBillNo()));
            String profileIdJson = profileIdObject.toString();
            billBuilder.setObfuscatedProfileId(URLEncoder.encode(profileIdJson, "utf8"));
            String string = URLEncoder.encode(profileIdJson, "utf8");
            CTILog.i(TAG, string);
        }  catch (Exception e) {
            CTILog.i("payUrlEncode", e.toString());
            e.printStackTrace();
        }

        try {
            com.xiaomi.billingclient.api.BillingFlowParams billingFlowParams = billBuilder.build();

            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=launchPurchaseFlow");
            billingHelper.launchPurchaseFlow(mView.getActivity(), billingFlowParams, this);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
                    0,
                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
            reportResult("payerr", e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
                    0,
                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
            reportResult("payerr", e.getMessage());
        }

    }

    @Override
    public void postPay() {
        MTimer.start(MTimer.GW_PROCESS_POSTPAY);

        String purchaseToken = mPurchase.getPurchaseToken();
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=consumeParams");
        if (null != billingHelper) {
            billingHelper.consumeAsync(purchaseToken, this);
        }
    }

    @Override
    protected boolean isSdkProvide() {
        return true;
    }

    @Override
    public void onIabSetupFinished(WrapperBillingResult result) {
        CTILog.d(TAG, "onIabSetupFinished:" + ((result == null) ? "null" : result.toString()));
        MTimer.stop(MTimer.GW_PROCESS_INIT);
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onIabSetupFinished&result=" + result.resultCode() + "&msg=" + result.resultMsg());
        if (result.isSuccess()) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_SUCC, 0, null);

            reportResult("initsucc", "");
            reportTime("initsucc", MTimer.duration(MTimer.GW_PROCESS_INIT));
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_ERROR, result.unifyErrCode(), result.resultMsg());

            reportResult("initerr", result.resultMsg());
            reportTime("initerr", MTimer.duration(MTimer.GW_PROCESS_INIT));
        }
    }

    @Override
    public void onQueryPurchasesResponse(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTILog.d(TAG, "onQueryPurchasesResponse:" + ((result == null) ? "null" : result.toString()));
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryPurchasesResponse&result=" + result.resultCode() + "&msg=" + result.resultMsg() + "&purchaseList=" + ((purchasesList == null) ? "null" : purchasesList.toString()));
        if (result.isSuccess()) {
            onQueryPurchasesSuccess(result, purchasesList);
        } else {
            onQueryPurchasesFailed(result);
        }
    }

    @Override
    public void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
        CTILog.d(TAG, "onSkuDetailsResponse:" + ((result == null) ? "null" : result.toString()));
        MTimer.stop(MTimer.GW_PROCESS_PREPAY);
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onSkuDetailsResponse&result=" + result.resultCode() +
                        "&msg=" + ((result == null) ? "null" : result.toString()) + "&skuDetailsList="
                        + ((skuDetailsList == null) ? "null" : skuDetailsList.toString()));

        if (result.isSuccess()) {
            onQuerySkuDetailsSuccess(result, skuDetailsList);

            reportResult("prepaysucc", "");
            reportTime("prepaysucc", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
        } else {
            onQuerySkuDetailsFailed(result);

            reportResult("prepayerr", result.resultMsg());
            reportTime("prepayerr", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
        }
    }

    @Override
    public void onPurchaseResponse(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTILog.d(TAG, "onPurchaseResponse:" + (result == null ? "null" : result.toString()));
        CTILog.d(TAG, "purchasesList:" + ((purchasesList == null) ? "null" : purchasesList.toString()));
        MTimer.stop(MTimer.GW_PROCESS_PAY);

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseResponse&result=" + result.resultCode() + "&msg=" + result.resultMsg() + "&purchasesList=" + ((purchasesList == null) ? "null" : purchasesList.toString()));

        if (result.isSuccess()) {
            onPurchaseSuccess(result, purchasesList);

            reportResult("paysucc", result.resultMsg());
            reportTime("paysucc", MTimer.duration(MTimer.GW_PROCESS_PAY));
        } else if (result.resultCode() == BillingClient.BillingResponseCode.PAYMENT_SHOW_DIALOG) {
            return;
        } else {
            onPurchaseFailed(result);

            reportResult("payerr", result.resultMsg());
            reportTime("payerr", MTimer.duration(MTimer.GW_PROCESS_PAY));
        }

        reportTime(MTimer.GW_FIRST_SCREEN_SHOWDIALOG, MTimer.duration(MTimer.GW_FIRST_SCREEN_SHOWDIALOG));
        reportTime(MTimer.GW_PROCESS_SHOW_DIALOG, MTimer.duration(MTimer.GW_PROCESS_SHOW_DIALOG));
    }

    @Override
    public void onConsumeResponse(WrapperBillingResult result, String purchaseToken) {
        CTILog.d(TAG, "onConsumeResponse:" + ((result == null) ? "null" : result.toString()));
        MTimer.stop(MTimer.GW_PROCESS_POSTPAY);

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseResponse&result=" + result.resultCode() + "&msg=" + result.resultMsg() + "&purchaseToken=" + ((purchaseToken == null) ? "null" : purchaseToken.toString()));


        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_CONSUME_RESULT,
                "version=2.0&result=" + (result == null ? "null" : result.resultCode()) + "&msg=" + (result == null ? "null" : result.resultMsg()) + "&billno=" + (mModel == null ? "" : mModel.getBillNo()) + "&productid=" + (mModel == null ? "" : mModel.getRequest().getProductID()));

        if (result.isSuccess()) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC, 0, purchaseToken);

            reportResult("consumesucc", result.resultMsg());
            reportTime("consumesucc", MTimer.duration(MTimer.GW_PROCESS_POSTPAY));
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_ERROR, 0, result.resultMsg());

            reportResult("consumeerr", result.resultMsg());
            reportTime("consumeerr", MTimer.duration(MTimer.GW_PROCESS_POSTPAY));
        }
    }

    /****************************** api result ***************************************/

    private void onQueryPurchasesSuccess(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryPurchasesSuccess&result=" + result.resultCode() + "&purchaselist=" + purchasesList.toString());

//        if (null != mModel) {
//            BillingFlowParams request = mModel.getRequest();
//            BillingFlowParams.BillingFlowParamsExtra billExtra = request.getExtra();
//            subcribeUpgrate(billExtra.getChannelExtras());
//            CTILog.d(TAG, "onQueryPurchasesSuccess ChannelExtras: " + billExtra.getChannelExtras());
//        }

        //continue query sku details
        querySkuDetails();
    }

    private void onQueryPurchasesFailed(WrapperBillingResult result) {
        //query purchases,still query sku details
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryPurchasesFailed&result=" + result.resultCode());
        querySkuDetails();

    }

    private void onQuerySkuDetailsSuccess(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQuerySkuDetailsSuccess&result=" + result.resultCode() + "&skuDetailsList=" + skuDetailsList);
        if (skuDetailsList != null && !skuDetailsList.isEmpty()) {
            for (SkuDetails skuDetails : skuDetailsList) {
                if (productId.equals(skuDetails.getSku())) {
                    CTILog.i(TAG, skuDetails.toString());

                    mSkuDetails = skuDetails;
                    //Generate order information
                    CTIPayInfo info = new CTIPayInfo();
                    info.currency = skuDetails.getPriceCurrencyCode();
                    info.amount = CTITools.urlEncode(skuDetails.getPrice(), 1);
                    info.ext = CTITools.urlEncode(String.valueOf(skuDetails.getPriceAmountMicros()), 1);
                    sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC, 0, info);
                }
            }
        } else {
            //query exception,still order
            onQuerySkuDetailsFailed(result);
        }
    }

    private void onQuerySkuDetailsFailed(WrapperBillingResult result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQuerySkuDetailsFailed&result=" + result.resultCode());
        //Do not intercept, continue to pay, set the default value
        CTIPayInfo defInfo = new CTIPayInfo();
        defInfo.currency = "default";
        defInfo.amount = "0.0";
        defInfo.ext = "0.0";
        sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC, 0, defInfo);
    }

    private void onPurchaseSuccess(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseSuccess&result=" + result.resultCode() + "&purchasesList=" + purchasesList);
        if (purchasesList != null && !purchasesList.isEmpty()) {
            for (Purchase purchase : purchasesList) {
                if (purchase.getSkus().contains(productId)) {
                    //purchased
                    if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                        // 支付成功后重新查询物品，并将第二次物品存到 SecondSkuDetails 中
                        // 拉物品的等待时间由下单返回，范围在 0-500ms 内，如果时间为 0 就不进行第二次拉物品
                        querySecondSkuDetails();

                        new Timer().schedule(new TimerTask() {
                            @Override
                            public void run() {
                                mPurchase = purchase;
                                CTIPayReceipt rec = new CTIPayReceipt();
                                String sigString = "receipt_sig";
                                JSONObject purchaseJsonObject = new JSONObject();
                                try {
                                    purchaseJsonObject.put("orderId", mPurchase.getOrderId());
                                    purchaseJsonObject.put("packageName", mPurchase.getPackageName());
                                    purchaseJsonObject.put("productId", mPurchase.getSkus());
                                    purchaseJsonObject.put("purchaseTime", mPurchase.getPurchaseTime());
                                    purchaseJsonObject.put("purchaseState", mPurchase.getPurchaseState());
                                    purchaseJsonObject.put("purchaseToken", mPurchase.getPurchaseToken());
                                    purchaseJsonObject.put("obfuscatedAccountId", mPurchase.getObfuscatedAccountId());
                                    purchaseJsonObject.put("obfuscatedProfileId", mPurchase.getObfuscatedProfileId());
                                    purchaseJsonObject.put("quantity", mPurchase.getQuantity());
                                    purchaseJsonObject.put("acknowledged", mPurchase.isAcknowledged());

                                } catch (JSONException e) {
                                    CTILog.d(TAG,"onPurchaseSuccess exception: "+e.getMessage());
                                }
                                String data = purchaseJsonObject.toString();
                                String base64dataString = CTIBase64.encode(data.getBytes());
                                rec.receipt = base64dataString;
                                rec.receipt_sig = sigString;
                                rec.sku = mPurchase.getSkus();
                                rec.first_currency_type = mSkuDetails.getPriceCurrencyCode();
                                if(mSecondSkuDetails != null){
                                    rec.second_currency_type = mSecondSkuDetails.getPriceCurrencyCode();
                                    rec.second_amt = mSecondSkuDetails.getPrice();
                                }
                                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL,
                                        "secondCurrencyType=" + rec.second_currency_type + "&secondAmt=" + rec.second_amt
                                                + "&waitSecondSkuDetailsTime=" + waitSecondSkuDetailsTime);
                                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 0, rec);
                                CTILog.i(TAG, "purchase info: " + purchase.toString());
                            }
                        }, waitSecondSkuDetailsTime);


                    } else if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                        CTILog.e(TAG, "Purchased State Error: " + purchase.getPurchaseState());
                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_TO_BE_PAID, MRetCode.ERR_GW_BILLING_PAY_PENDING, "Items to be paid!");
                    } else {
                        CTILog.e(TAG, "Purchased State Error: " + purchase.getPurchaseState());
                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, result.resultCode(), result.resultMsg());
                    }
                }
            }
        } else {
            //The fourth mode of subscription upgrade will come here
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 100, "purchasesList is null");
        }
    }

    private void onPurchaseFailed(WrapperBillingResult result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseFailed&result=" + result.resultCode());
        int failCode = result.resultCode();

        //show sandbox error tips
        if (CTITools.isTestEnv()) {
            result.showSandboxErrTips();
        }

        if (failCode == BillingClient.BillingResponseCode.USER_CANCELED) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_CANCEL, MRetCode.ERR_GW_BILLING_USER_CANCEL, result.resultMsg());
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, result.unifyErrCode(), result.resultMsg());
        }
    }

    /****************************** private ****************************************/

    private void sendMesUIH(int what, int arg1, Object obj) {
        if (UIHandler != null) {
            Message msg = new Message();
            msg.what = what;
            msg.arg1 = arg1;
            msg.obj = obj;
            Bundle bundle = new Bundle();
            // resultCode -> InnerCode
            bundle.putString("msgErrCode", String.valueOf(arg1));
            msg.setData(bundle);
            UIHandler.sendMessage(msg);
        }
    }

    private void reportTime(String name, long times) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_TIME_20,
                "version=2.0&name=" + name
                        + "&times=" + times
                        + "&productType=" + xiaomiType
                        + "&centauriType=" + centauriType);
    }

    private void reportResult(String name, String result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_RESULT_20,
                "version=2.0&name=" + name
                        + "&result=" + result
                        + "&productType=" + xiaomiType
                        + "&centauriType=" + centauriType);
    }

    @Override
    protected boolean needShowSucc() {
        return false;
    }

    private void subcribeUpgrate(String channelExtras) {
        try {
            if (!TextUtils.isEmpty(channelExtras)) {
                HashMap<String, String> desParams = CTITools.kv2Map(channelExtras);
                if (!TextUtils.isEmpty(desParams.get("SubcribeProrationMode")) &&
                        !TextUtils.isEmpty(desParams.get("OldSku"))) {
                    OldSku = desParams.get("OldSku");
                }
            } else {
                CTILog.i(TAG, "APPay channelExtras is null ");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            CTILog.i(TAG, "subcribeUpgrate errr:" + ex.toString());
        }
    }

    private void querySecondSkuDetails() {
        // 此方法是为了防止支付前后币种不一致导致造成损失，在支付后再拉一次物品，并将信息在发货流程中传给后台
        // 支付成功后先拉一次物品信息，花费指定时间等待拉物品的结果，然后将支付前后的币种传给发货 CGI 处理，如果没有拉到支付后币种也不影响正常发货流程
        CTILog.d(TAG, "query product after pay success");
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=secondQuerySkuDetailsAsync");

        ArrayList<String> skuList = new ArrayList<String>();
        skuList.add(productId);
        SkuDetailsParams skuDetailsParams = SkuDetailsParams.newBuilder()
                .setType(xiaomiType)
                .setSkusList(skuList)
                .build();

        if (null != billingHelper) {
            billingHelper.querySkuDetailsAsync(skuDetailsParams, new BillingHelper.OnIabQuerySkuDetailsListener() {
                @Override
                public void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
                    CTILog.d(TAG, "onQuerySecondSkuDetailsResponse:" + ((result == null) ? "null" : result.toString()));
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                            "name=onQuerySecondSkuDetailsResponse&result=" + result.resultCode() +
                                    "&msg=" + ((result == null) ? "null" : result.toString()) + "&skuDetailsList="
                                    + ((skuDetailsList == null) ? "null" : skuDetailsList.toString()));

                    if (skuDetailsList != null && !skuDetailsList.isEmpty()) {
                        for (SkuDetails skuDetails : skuDetailsList) {
                            if (productId.equals(skuDetails.getSku())) {
                                mSecondSkuDetails = skuDetails;
                                CTILog.d(TAG, "mSecondSkuDetails is " + mSecondSkuDetails);
                            }
                        }
                    }
                }
            });
        }
    }

    // 从 obj(info)中取出 wait_time 作为 waitSecondSkuDetailsTime 的值，取值范围为 [0, 500]
    private int getWaitSecondSkuDetailsTime(JSONObject obj){
        if(obj != null && !TextUtils.isEmpty(obj.optString("wait_time"))){
            try {
                int waitTime = Integer.parseInt(obj.optString("wait_time"));
                if(waitTime >= 0 && waitTime <= 500) {
                    return waitTime;
                }
            } catch (Exception e) {
                CTILog.e(TAG, "getWaitSecondSkuDetailsTime(): " + e.getMessage());
            }
        }
        return 0;
    }

}
