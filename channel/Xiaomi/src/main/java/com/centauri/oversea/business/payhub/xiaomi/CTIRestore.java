package com.centauri.oversea.business.payhub.xiaomi;

import android.content.Context;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.CTIBaseRestore;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.ICallback;
import com.centauri.oversea.newapi.response.IGetPurchaseCallback;
import com.xiaomi.billingclient.api.Purchase;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class CTIRestore extends CTIBaseRestore
        implements BillingHelper.OnIabSetupFinishedListener, BillingHelper.OnIabConsumeListener {
    public final static String TAG = "APXiaomiRestore_New";

    private BillingHelper billingHelper;
    private ICallback initCallback;
    private ICTICallback postProvideCallback;
    private long reportStartTime = 0;

    //Call back external content, json format
    private JSONObject callbackJs = new JSONObject();

    //need consume purchase size
    private int mTotalConsumed = 0;
    //current consume index
    private int mCurrentIndex = 0;
    //query purchase list
    private List<Purchase> mPurchases = new ArrayList<Purchase>();

    //consume end report content
    private StringBuilder consumeReport = new StringBuilder();
    //callback
    private JSONArray list = new JSONArray();
    private JSONArray forbidList = new JSONArray();

    @Override
    public void init(Context context, ICallback callback) {
        updateTimer();
        initCallback = callback;

        billingHelper = BillingHelper.getInstance(context);
        billingHelper.startSetup(this);
    }

    @Override
    public void getPurchaseList(Context context, IGetPurchaseCallback callback) {
        updateTimer();

        if(billingHelper != null){

            try{
                //query purchases
                billingHelper.queryPurchasesAsync(new BillingHelper.OnIabQueryPurchasesListener() {
                    @Override
                    public void onQueryPurchasesResponse(WrapperBillingResult result, List<Purchase> purchasesList) {
                        CTILog.i(TAG, "onQueryPurchasesResponse: " + result);

                        if (result.isSuccess()) {
                            if (purchasesList != null && !purchasesList.isEmpty()) {
                                ArrayList<CTIPayReceipt> receiptList = new ArrayList<CTIPayReceipt>();
                                for (Purchase purchase : purchasesList) {
                                    CTILog.i(TAG,purchase.toString());

                                    if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                                        mPurchases.add(purchase);

                                        CTIPayReceipt rec = new CTIPayReceipt();
                                        JSONObject purchaseJsonObject = new JSONObject();
                                        try {
                                            purchaseJsonObject.put("orderId", purchase.getOrderId());
                                            purchaseJsonObject.put("packageName", purchase.getPackageName());
                                            purchaseJsonObject.put("productId", purchase.getSkus());
                                            purchaseJsonObject.put("purchaseTime", purchase.getPurchaseTime());
                                            purchaseJsonObject.put("purchaseState", purchase.getPurchaseState());
                                            purchaseJsonObject.put("purchaseToken", purchase.getPurchaseToken());
                                            purchaseJsonObject.put("obfuscatedAccountId", purchase.getObfuscatedAccountId());
                                            purchaseJsonObject.put("obfuscatedProfileId", purchase.getObfuscatedProfileId());
                                            purchaseJsonObject.put("quantity", purchase.getQuantity());
                                            purchaseJsonObject.put("acknowledged", purchase.isAcknowledged());

                                        } catch (JSONException e) {
                                            CTILog.d(TAG,"onPurchaseSuccess exception: "+e.getMessage());
                                        }
                                        String data = purchaseJsonObject.toString();
                                        String base64dataString = CTIBase64.encode(data.getBytes());
                                        rec.receipt = base64dataString;
                                        rec.receipt_sig = "receipt_sig";
                                        rec.sku = purchase.getSkus();
                                        rec.orderId = purchase.getOrderId();
                                        receiptList.add(rec);   //Add to bill list
                                    }
                                }

                                callback.callback(receiptList);
                            } else {
                                callback.callback(null);
                            }
                        } else {
                            callback.callback(null);
                        }

                        reportEnd("queryEnd", result.resultCode() + "_" + mPurchases.size()+"_"+mPurchases.toString());
                    }
                });
            }catch (Exception ex){
                ex.printStackTrace();
                callback.callback(null);
                reportEnd("queryEnd",ex.getMessage());
            }
        }else{
            callback.callback(null);
            reportEnd("queryEnd","mHelper is null or mHelper not setup.");
        }
    }

    @Override
    public void postProvide(List<String> list, ICTICallback callback) {
        postProvideCallback = callback;
        if (list == null || list.isEmpty()) {
            callbackSuper(MRetCode.ERR_OTHER);
            return;
        }

        /**
         * add need consumed purchases.
         * as Purchases doesn't specify product type,so added purchases maybe subs.
         */
        List<Purchase> consumeList = new ArrayList<Purchase>();
        for (String productId : list) {
            for (Purchase purchase : mPurchases) {
                if (purchase.getSkus().contains(productId)) {
                    consumeList.add(purchase);
                    CTILog.d(TAG, "consumeList add:" + productId);
                }
            }
        }

        CTILog.d(TAG, "consumeList size:" + consumeList.size());
        if (!consumeList.isEmpty()) {
            consumeAsync(consumeList);
        } else {
            callbackSuper(MRetCode.OK);
        }
    }

    //consume
    private void consumeAsync(List<Purchase> consumeList) {
        updateTimer();
        mTotalConsumed = consumeList.size();

        for (Purchase purchase : consumeList) {
            billingHelper.consumeAsync(purchase.getPurchaseToken(), this);
        }
    }

    @Override
    public void dispose() {
        CTILog.i(TAG, "dispose()");
        if (!mPurchases.isEmpty()) {
            mPurchases.clear();
        }
        super.dispose();
    }

    @Override
    public void onIabSetupFinished(WrapperBillingResult result) {
        CTILog.d(TAG, "onIabSetupFinished: " + result);
        if (result.isSuccess()) {
            initCallback.callback(MRetCode.OK);
        } else {
            initCallback.callback(MRetCode.ERR_OTHER);
        }
        reportEnd("initEnd", String.valueOf(result.resultCode()));
    }

    @Override
    public void onConsumeResponse(WrapperBillingResult result, String purchaseToken) {
        CTILog.d(TAG, "onConsumeResponse: " + result + ",mCurrentIndex=" + mCurrentIndex);
        if (null == mPurchases || mPurchases.size() <= mCurrentIndex) {
            callbackSuper(MRetCode.ERR_OTHER);
            return;
        }
        Purchase purchase = mPurchases.get(mCurrentIndex);

        //set report content
        consumeReport.append(purchase.getOrderId())
                .append("|")
                .append(result.isSuccess())
                .append("|");

        // Determine whether the game does not need to be shipped, such as the redemption code exceeds the limit
        String sku = "";
        for (String s : purchase.getSkus()) {
            sku = s;
            break;
        }
        for (String s : purchase.getSkus()) {
            if (forbiddenPrompts.contains(s)) {
                forbidList.put(s);
                break;
            }
        }


        try {
            JSONObject item = new JSONObject();
            item.put("productid", sku);
            item.put("gwalletOrderId", purchase.getOrderId());
            list.put(item);
        } catch (JSONException e) {
            CTILog.e(TAG, "JSONException1: " + e.getMessage());
        }

        //callback
        if (++mCurrentIndex == mTotalConsumed) {
            reportEnd("consumeEnd", consumeReport.toString());

            try {
                if (list.length() != 0 || forbidList.length() != 0) {
                    addCallbackItem("ret",MRetCode.OK);
                    addCallbackItem("paychannelid", "gwallet");
                    addCallbackItem("products", list);
                    addCallbackItem("error_products", forbidList);
                }
            } catch (JSONException e) {
                CTILog.e(TAG, "JSONException2: " + e.getMessage());
            }

            callbackSuper(MRetCode.OK);
        }
    }

    private void addCallbackItem(String key, Object value)
            throws JSONException {
        if (callbackJs == null) {
            callbackJs = new JSONObject();
        }

        callbackJs.put(key, value);
    }

    //Add shipping subscription bill information
    private void addSubInfo() {
        if (provideSdkRetMap != null && !provideSdkRetMap.isEmpty()) {
            Iterator<String> _it = provideSdkRetMap.keySet().iterator();
            try {
                JSONArray jsSubArray = new JSONArray();
                while (_it.hasNext()) {
                    String productId = _it.next();
                    String provideSdkRet = provideSdkRetMap.get(productId);

                    //gw_subscription
                    JSONObject _jSdkRet = new JSONObject(provideSdkRet);
                    if (_jSdkRet.has("gw_subscription")) {
                        JSONObject _jSubItem = _jSdkRet.getJSONObject("gw_subscription");
                        _jSubItem.put("productId", productId);
                        //add to JSONArray
                        jsSubArray.put(_jSubItem);
                    }

                }

                //add to callbackJs
                if (jsSubArray.length() > 0) {
                    addCallbackItem("subInfo", CTIBase64.encode(jsSubArray.toString().getBytes()));
                }
            } catch (JSONException e) {
                CTILog.d(TAG, "addSubInfo exception: " + e.getMessage());
            }
        }
    }

    private void callbackSuper(int retCode) {
        if (postProvideCallback != null) {
            addSubInfo();
            if (callbackJs != null && callbackJs.length() > 0) {
                postProvideCallback.callback(retCode, callbackJs.toString());
            } else {
                postProvideCallback.callback(retCode, DEFAULT_RET_MSG);
            }
        }
    }

    private void updateTimer() { reportStartTime = System.currentTimeMillis(); }

    private void reportEnd(String name, String retMsg) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_REPROVIDE_TIME_CONSUME_20,
                "version=2.0&name=" + name
                        + "&times=" + (System.currentTimeMillis() - reportStartTime)
                        + "&result=" + retMsg); //Report interface duration
    }
}
