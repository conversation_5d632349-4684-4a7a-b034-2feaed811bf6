package com.centauri.oversea.business.payhub.xiaomi;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.centauri.comm.CTILog;
import com.centauri.oversea.api.request.CTIGameRequest;
import com.centauri.oversea.business.IGetProduct;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.response.InfoCallback;
import com.xiaomi.billingclient.api.BillingClient;
import com.xiaomi.billingclient.api.SkuDetails;
import com.xiaomi.billingclient.api.SkuDetailsParams;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CTIProductInfo
        implements IGetProduct, BillingHelper.OnIabSetupFinishedListener, BillingHelper.OnIabQuerySkuDetailsListener {

    private final static String TAG = "APProductInfo_New";
    private BillingHelper billingHelper;
    private InfoCallback mCallback;
    private HashMap<String, String> mSkus = null;
    private Context mContext;

    private final static String SUCCESS = "0";
    private final static String ERROR = "-1";
    private final static String ERROR_DATA = "-2";
    private final static String ERROR_PARAM = "-3";
    private Handler mHandler = new Handler();


    //callback count
    private int callbackCount = 1;
    private final int CALLBACK_TIMES = 2;
    //callback result
    private JSONArray jsRes = new JSONArray();

    @Override
    public void getProductInfo(Context context, HashMap<String, String> productIdMap, InfoCallback callback) {
        mCallback = callback;
        mSkus = productIdMap;
        mContext = context;
        CTILog.d(TAG, "init");
        billingHelper = BillingHelper.getInstance(context);
        if (mSkus != null && !mSkus.isEmpty()) {
            init(mContext, new BillingHelper.OnIabSetupFinishedListener() {
                @Override
                public void onIabSetupFinished(WrapperBillingResult result) {
                    CTILog.i(TAG, "onIabSetupFinished:" + result);
                    if (result.isSuccess()) {
                        queryProductsInfo();
                    } else {
                        callback(ERROR, result.resultMsg());
                    }
                }
            });
        } else {
            callback(ERROR_PARAM, "query productList is empty");
        }
    }

    @Override
    public void getProductInfo(Activity activity, HashMap<String, String> productIdMap, InfoCallback callback) {

    }

    @Override
    public void getProductInfo(Activity activity, CTIGameRequest ctiGameRequest, InfoCallback infoCallback) {

    }

    @Override
    public void clearPurchase(Context context) {

    }

    private void init(Context context, BillingHelper.OnIabSetupFinishedListener listener) {
        CTILog.d(TAG, "init");
        billingHelper = BillingHelper.getInstance(context);
        billingHelper.startSetup(listener);
    }

    //Get the productlist by category from the map
    private List<String> getListByTypeFromMap(HashMap<String, String> map, String type) {

        if (map == null || map.isEmpty()) {
            return null;
        }

        List<String> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {

            if (entry.getValue().equals(type)) {
                list.add(entry.getKey());
            }
        }
        return list;
    }

    private void queryProductsInfo() {
        CTILog.d(TAG, "queryProductsInfo");

        List<String> inappList = getListByTypeFromMap(mSkus, BillingClient.SkuType.INAPP);
//        List<String> subsList = getListByTypeFromMap(mSkus, BillingClient.SkuType.SUBS);

        SkuDetailsParams.Builder inAppParams = SkuDetailsParams.newBuilder();
        inAppParams.setSkusList(inappList).setType(BillingClient.SkuType.INAPP);

//        SkuDetailsParams.Builder subsParams = SkuDetailsParams.newBuilder();
//        inAppParams.setSkusList(subsList).setType(BillingClient.SkuType.SUBS);

        if (null == billingHelper) {
            callback(ERROR, "billingHelper is null");
            return;
        }
        //query in-apps
        billingHelper.querySkuDetailsAsync(inAppParams.build(), this);
        //query subs
//        billingHelper.querySkuDetailsAsync(subsParams.build(), this);
    }



    @Override
    public void onIabSetupFinished(WrapperBillingResult result) {

    }

    @Override
    public void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
        CTILog.i(TAG, "onSkuDetailsResponse:" + result + ",callbackCount=" + callbackCount);
        if (result.isSuccess() &&
                skuDetailsList != null && !skuDetailsList.isEmpty()) {
            try {
                for (SkuDetails skuDetails : skuDetailsList) {
                    JSONObject item = new JSONObject();
                    item.put("productId", skuDetails.getSku());
                    item.put("price", skuDetails.getPrice());
                    item.put("currency", skuDetails.getPriceCurrencyCode());
                    item.put("microprice", skuDetails.getPriceAmountMicros());
                    item.put("originalPrice", skuDetails.getOriginalPrice());
                    item.put("originalMicroprice", skuDetails.getOriginalPriceAmountMicros());
                    jsRes.put(item);
                }

                GlobalData.singleton().setCurrencyInGw(TextUtils.isEmpty(skuDetailsList.get(0).getPriceCurrencyCode()) ? "default" : skuDetailsList.get(0).getPriceCurrencyCode());

            } catch (JSONException e) {
                CTILog.e(TAG, "onSkuDetailsResponse exception: " + e.getMessage());
            }
        }

//        if (callbackCount++ == CALLBACK_TIMES) {
        callback(SUCCESS, "success");
//        }
    }

    private void callback(String retCode, String retMsg) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("ret", retCode);
            obj.put("msg", retMsg);
            obj.put("productInfo", jsRes);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Log.d(TAG, "callback:" + obj.toString());
        if (mCallback != null) {
            mCallback.callback(obj.toString());
        }

    }
}
