package com.centauri.oversea.business.payhub.xiaomi;

import com.centauri.oversea.comm.MRetCode;
import com.xiaomi.billingclient.api.BillingClient;
import com.xiaomi.billingclient.api.BillingResult;
import com.centauri.comm.CTILog;


public class WrapperBillingResult {
    private final String TAG = "WrapperBillingResult";

    private final BillingResult billingResult;

    public WrapperBillingResult(BillingResult billingResult) {  this.billingResult = billingResult; }

    public boolean isSuccess() {
        return billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK;
    }

    public int resultCode() {
        return billingResult.getResponseCode();
    }

    public String resultMsg() {
        return billingResult.getDebugMessage();
    }

    /**
     * Unified conversion Xiaomi error code starts with *-200x*
     */
    public int unifyErrCode() {
        CTILog.i(TAG, "unifyXiaomiErrCode(),gw origin error code: " + resultMsg());

        int res = MRetCode.ERR_GW_UNKNOW;

        switch (resultCode()) {
            case BillingClient.BillingResponseCode.USER_CANCELED:
                res = MRetCode.ERR_GW_BILLING_USER_CANCEL;
                break;
            case BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE:
                res = MRetCode.ERR_GW_BILLING_SERVICE_UNAVAILABLE;
                break;
            case BillingClient.BillingResponseCode.BILLING_UNAVAILABLE:
                if (resultMsg().contains("unavailable on device")) {
                    res = MRetCode.ERR_GW_BILLING_UNAVAILABLE_DEVICE;
                } else {
                    res = MRetCode.ERR_GW_BILLING_UNAVAILABLE_ACCOUNT;
                }
                break;
            case BillingClient.BillingResponseCode.ITEM_UNAVAILABLE:
                res = MRetCode.ERR_GW_BILLING_ITEM_UNAVAILABLE;
                break;
            case BillingClient.BillingResponseCode.DEVELOPER_ERROR:
                res = MRetCode.ERR_GW_BILLING_DEVELOPER_ERROR;
                break;
            case BillingClient.BillingResponseCode.ERROR:
                res = MRetCode.ERR_GW_BILLING_RESULT_ERROR;
                break;
            case BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED:
                res = MRetCode.ERR_GW_BILLING_ITEM_ALREADY_OWNED;
                break;
            case BillingClient.BillingResponseCode.ITEM_NOT_OWNED:
                res = MRetCode.ERR_GW_BILLING_ITEM_NOT_OWNED;
                break;
            case BillingClient.BillingResponseCode.SERVICE_TIMEOUT:
                res = MRetCode.ERR_GW_BILLING_SERVICE_TIMEOUT;
                break;
            case BillingClient.BillingResponseCode.SERVICE_DISCONNECTED:
                res = MRetCode.ERR_GW_BILLING_SERVICE_DISCONNECTED;
                break;
        }

        return res;
    }

    /**
     * In the sandbox environment, when an error occurs, tips are displayed to the developer
     */
    public void showSandboxErrTips() {
        switch (resultCode()) {
            case BillingClient.BillingResponseCode.USER_CANCELED:
                CTILog.e(TAG, resultMsg() + "\nReason: the user pressed the return key or cancelled the dialog box\n");
                break;
            case BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE:
                CTILog.e(TAG, resultMsg() + "\nreason: network connection lost\n");
                break;
            case BillingClient.BillingResponseCode.BILLING_UNAVAILABLE:
                CTILog.e(TAG, resultMsg() + "\nReason: The Billing API version is not supported by the requested type\n");
                break;
            case BillingClient.BillingResponseCode.ITEM_UNAVAILABLE:
                CTILog.e(TAG, resultMsg() + "\nReason: The requested product cannot be purchased\n");
                break;
            case BillingClient.BillingResponseCode.DEVELOPER_ERROR:
                CTILog.e(TAG, resultMsg() + "\nReason: The parameters provided to the API are invalid. This error may also indicate that the app is not properly signed or set up for in-app purchase billing in Google Play, or is not required in the list Permissions\n");
                break;
            case BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED:
                StringBuilder tips = new StringBuilder();
                tips.append(resultMsg()).append("\n")
                        .append("Reason:").append("\n")
                        .append("Already own the item, it may be caused by the failure of delivery after the last purchase of the item").append("\n")
                        .append("Solution:").append("\n")
                        .append("1, log out of the application, log in again, and try again").append("\n")
                        .append("2, can't clear the google play store cache and try again");
                CTILog.e(TAG, tips.toString());
                break;
            case BillingClient.BillingResponseCode.ITEM_NOT_OWNED:
                CTILog.e(TAG, resultMsg() + "\nReason: Because you do not own the product, the consumption failed\n");
                break;
            case BillingClient.BillingResponseCode.ERROR:
                StringBuilder tips2 = new StringBuilder();
                tips2.append(resultMsg()).append("\n")
                        .append("Reason 1:").append("\n")
                        .append("Google play returns wrong content, maybe the phone has not opened the system pop-up permission, Xiaomi has not opened it by default").append("\n")
                        .append("Solution:").append("\n")
                        .append("Open the system pop-up permission of google play store in the settings, try again\n")
                        .append("Reason 2:").append("\n")
                        .append("Fatal error occurred during API operation");
                CTILog.e(TAG, tips2.toString());
                break;
        }
    }
}
