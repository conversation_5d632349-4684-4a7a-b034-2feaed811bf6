apply plugin: 'com.android.library'


android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        versionCode rootProject.ext.centauriCoreVersionCode
        versionName rootProject.ext.centauriCoreVersionName
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
    }

    compileOptions {
        sourceCompatibility rootProject.ext.javaCompileVersion
        targetCompatibility rootProject.ext.javaCompileVersion
    }

    lintOptions {
        abortOnError false
    }
//    packageBuildConfig = false
    buildFeatures {
        buildConfig = false
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

/**
 * build jar and rename jar by dongbingliu 2020-10-30 星期五
 */
task ctiXiaomiSourceJar(type: Copy) {
    String jarPackageName = "centauri-xiaomi-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-4.05.547.jar"
    //删除存在的
    delete("build/libs")
    String libDir = file('build/libs')
    //设置拷贝的文件来源
//    from('build/intermediates/packaged-classes/release/classes.jar')
    from('build/intermediates/compile_library_classes_jar/release/classes.jar')
    ////新生成的jar包的目录
    into(libDir)
    //将新生成的jar包classes.jar(新生成的jar文件名默认为classes.jar)放入上面的目录下目录下
    include('classes.jar')
    //重命名成我们设定的名字
    rename ('classes.jar', jarPackageName)
}
ctiXiaomiSourceJar.dependsOn(build)

dependencies {

    implementation "com.xiaomi.billingclient:billing:1.1.3"
    implementation project(path: ':Core')
    compileOnly 'com.centauri.comm:centauricommon-log:2.0.3'

}