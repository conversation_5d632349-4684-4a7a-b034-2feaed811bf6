plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 30
        versionCode 101
        versionName "1.0.1"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }
    setVersion(defaultConfig.versionName)
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        abortOnError false
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

/**
 * build jar and rename jar by dongbingliu 2020-10-30 星期五
 */
task ctiOneStoreSourceJar(type: Copy) {
    String jarPackageName = "centauri-onestore-pay-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-${getVersion()}.jar"

    //删除存在的
    delete("build/libs")
    String libDir = file('build/libs')
    //设置拷贝的文件来源
//    from('build/intermediates/packaged-classes/release/classes.jar')
    from('build/intermediates/compile_library_classes_jar/release/classes.jar')
    ////新生成的jar包的目录
    into(libDir)
    //将新生成的jar包classes.jar(新生成的jar文件名默认为classes.jar)放入上面的目录下目录下
    include('classes.jar')
    //重命名成我们设定的名字
    rename ('classes.jar', jarPackageName)
}
ctiOneStoreSourceJar.dependsOn(build)

dependencies {

    implementation 'com.android.support:appcompat-v7:28.0.0'
//    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation project(path: ':Core')
    implementation(name:'iap_sdk-v19.00.01', ext:'aar')
    compileOnly 'com.centauri.comm:centauricommon-log:2.0.3'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
}