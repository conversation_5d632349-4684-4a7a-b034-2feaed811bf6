package com.centauri.oversea.business.payhub.onestore;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.comm.CTILog;
import com.gaa.sdk.iap.AcknowledgeParams;
import com.gaa.sdk.iap.ConsumeListener;
import com.gaa.sdk.iap.ConsumeParams;
import com.gaa.sdk.iap.IapResult;
import com.gaa.sdk.iap.ProductDetail;
import com.gaa.sdk.iap.ProductDetailsListener;
import com.gaa.sdk.iap.ProductDetailsParams;
import com.gaa.sdk.iap.PurchaseClient;
import com.gaa.sdk.iap.PurchaseClientStateListener;
import com.gaa.sdk.iap.PurchaseData;
import com.gaa.sdk.iap.PurchaseFlowParams;
import com.gaa.sdk.iap.PurchasesListener;
import com.gaa.sdk.iap.PurchasesUpdatedListener;
import com.gaa.sdk.iap.Security;

import org.json.JSONException;

import java.util.ArrayList;
import java.util.List;

public class PurchaseHelper implements PurchasesUpdatedListener {
    public static final String TAG = "OneStorePurchaseHelper";

    private PurchaseClient mPurchaseClient;
    private OnIabPurchaseListener mPurchaseListener;
    private IapResult mIapResult;
    private List<PurchaseData> mList;

    //true if billing service is connected
    private boolean mIsServiceConnected = false;
    private Security AppSecurity;

    private static final Handler mHandler = new Handler(Looper.getMainLooper());

    public PurchaseHelper(Context context){
        mPurchaseClient = PurchaseClient.newBuilder(context)
                .setListener(this)
                .setBase64PublicKey("")
                .build();
    }

    /**
     * connect to purchase service
     * @param listener
     */
    public void startSetup(final OnIabSetupFinishedListener listener){
        CTILog.i(TAG,"startSetup");
        startServiceConnection(new IabRunnable() {
            @Override
            public void run(IapResult result) {
                if(listener != null){
                    listener.onIabSetupFinished(result);
                }
            }
        });
    }

    /**
     * dispose
     */
    public void dispose() {
        Log.i(TAG, "dispose");
        if (mPurchaseClient != null) {
            mPurchaseClient.endConnection();
            mIsServiceConnected = false;
            mPurchaseClient = null;
            mPurchaseListener = null;
        }
    }

    private void startServiceConnection(final IabRunnable iabRunnable) {
        if(mIsServiceConnected){
            CTILog.i(TAG,"Service is connected.");
            return;
        }

        if(mPurchaseClient != null) {
            mPurchaseClient.startConnection(new PurchaseClientStateListener() {
                @Override
                public void onSetupFinished(IapResult iapResult) {
                    if (iapResult.isSuccess()) {
                        mIsServiceConnected = true;
                    } else if (iapResult.getResponseCode() == PurchaseClient.ResponseCode.RESULT_NEED_LOGIN) {
                        CTILog.i(TAG, "need login");
                    } else if (iapResult.getResponseCode() == PurchaseClient.ResponseCode.RESULT_NEED_UPDATE) {
                        CTILog.i(TAG, "need update");
                    } else {
                        CTILog.i(TAG, "ResponseCode: " + iapResult.getResponseCode() + " ResponseMsg: " + iapResult.getMessage());
                    }
                    iabRunnable.run(iapResult);
                }

                @Override
                public void onServiceDisconnected() {
                    mIsServiceConnected = false;

                    IapResult result = IapResult.newBuilder()
                            .setResponseCode(PurchaseClient.ResponseCode.ERROR_SERVICE_DISCONNECTED)
                            .build();
                    iabRunnable.run(result);
                }
            });
        }else{
            Log.e(TAG,"startServiceConnection: PurchaseClient is null.");
            IapResult result = IapResult.newBuilder()
                    .setResponseCode(MRetCode.ERR_OTHER)
                    .setMessage("PurchaseClient is null.")
                    .build();
            iabRunnable.run(result);
        }
    }

    /**
     * query purchases,block till the result back
     * @return
     */
    public void queryProductDetailsAsync(final ProductDetailsParams productDetailsParams, final OnIabQueryProductDetailsListener listener){
        CTILog.i(TAG, "queryProductsDetailsAsync");

        if(listener == null){
            CTILog.e(TAG, "queryProductDetailsAsync: listener is null.");
            return;
        }

        IabRunnable iabRunnalble = new IabRunnable(){
            @Override
            public void run(IapResult result) {
                if(mPurchaseClient != null){
                    try{
                        mPurchaseClient.queryProductDetailsAsync(productDetailsParams, new ProductDetailsListener() {
                            @Override
                            public void onProductDetailsResponse(IapResult iapResult, List<ProductDetail> productDetailsList) {
                                listener.onIabQueryProductDetailsResponse(iapResult, productDetailsList);
                            }
                        });
                    } catch(IllegalArgumentException e) {
                        e.printStackTrace();
                    }


                }else {
                    CTILog.e(TAG,"queryProductDetailsAsync: PurchaseClient is null.");

                    IapResult iapResult = IapResult.newBuilder()
                            .setResponseCode(MRetCode.ERR_OTHER)
                            .setMessage("PurchaseClient is null.")
                            .build();
                    listener.onIabQueryProductDetailsResponse(iapResult, null);
                }
            }
        };
        executeServiceRequest(iabRunnalble);
    }

    /**
     * query purchases,block till the result back
     *
     * @return
     */
    public void queryPurchasesAsync(final OnIabQueryPurchasesListener listener) {
        Log.i(TAG, "queryPurchasesAsync");

        if (listener == null) {
            Log.e(TAG, "queryPurchasesAsync: listener is null.");
            return;
        }

        IabRunnable iabRunnable = new IabRunnable() {
            @Override
            public void run(IapResult result) {

                if (mPurchaseClient != null) {
                    ArrayList<PurchaseData> resultList = new ArrayList<PurchaseData>();
                    final int[] count = {0};
                    mPurchaseClient.queryPurchasesAsync(PurchaseClient.ProductType.INAPP, new PurchasesListener() {
                        @Override
                        public void onPurchasesResponse(IapResult iapResult, List<PurchaseData> list) {
                            mHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    count[0]++;
                                    Log.i(TAG, "onQueryPurchasesResponse INAPP : " + count[0]);
                                    //add to result list
                                    if (iapResult.getResponseCode() == PurchaseClient.ResponseCode.RESULT_OK) {
                                        if (list != null && !list.isEmpty()) {
                                            resultList.addAll(list);
                                        }
                                    } else {
                                        Log.e(TAG, "queryPurchasesAsync: Get an error response trying to query in-app purchases.");
                                    }
                                    Log.i(TAG, "onQueryPurchasesResponse INAPP ");
                                    //callback
                                    listener.OnIabQueryPurchasesResponse(iapResult, resultList);
//                                    if (count[0] > 1) {
//
//
//                                        Log.i(TAG, "onQueryPurchasesResponse INAPP ");
//                                        //callback
//                                        listener.OnIabQueryPurchasesResponse(iapResult, resultList);
//
//                                        count[0] = 0;
//                                    }
                                }
                            });
                        }
                    });
                } else {
                    Log.e(TAG, "queryPurchasesAsync: BillingClient is null.");

                    IapResult iapResult = IapResult.newBuilder()
                            .setResponseCode(MRetCode.ERR_GW_UNKNOW)
                            .setMessage("PurchaseClient is null.")
                            .build();
                    listener.OnIabQueryPurchasesResponse(iapResult, null);
                }
            }
        };

        executeServiceRequest(iabRunnable);
    }

    public void launchPurchaseFlow(Activity activity, PurchaseFlowParams purchaseFlowParams, final OnIabPurchaseListener listener) {
        CTILog.i(TAG, "launchPurchaseFlow");

        if(listener == null){
            Log.e(TAG,"launchPurchaseFlow: listener is null.");
            return;
        }

        //cached
        mPurchaseListener = listener;

        IabRunnable runnable = new IabRunnable(){
            @Override
            public void run(IapResult result) {
                if(mPurchaseClient != null){
                    mPurchaseClient.launchPurchaseFlow(activity, purchaseFlowParams);
                }else{
                    CTILog.e(TAG, "launchPurchaseFlow: PurchaseClient is null");

                    IapResult iapResult = IapResult.newBuilder()
                            .setResponseCode(MRetCode.ERR_OTHER)
                            .setMessage("PurchaseClient is null.")
                            .build();

                    listener.onPurchaseResponse(iapResult, null);
                }
            }
        };
        executeServiceRequest(runnable);
    }

    /**
     * consume special purchase token
     * if have more than one purchase,this api will be called multiply times.
     * @param listener
     */
    public void consumeAsync(final ConsumeParams params, final OnIabConsumeListener listener){
        Log.i(TAG,"consumeAsync");

        if(listener == null){
            Log.e(TAG,"consumeAsync: listener is null.");
            return;
        }

        IabRunnable iabRunnable = new IabRunnable() {
            @Override
            public void run(final IapResult result){
                if(mPurchaseClient != null) {
                    mPurchaseClient.consumeAsync(params, new ConsumeListener(){
                        @Override
                        public void onConsumeResponse(IapResult iapResult, PurchaseData purchaseData) {
                            listener.onConsumeResponse(iapResult, purchaseData);
                        }
                    });
                } else{
                    CTILog.e(TAG, "consumeAsync： PurchaseClient is null.");
                    IapResult iapResult = IapResult.newBuilder()
                            .setResponseCode(MRetCode.ERR_OTHER)
                            .setMessage("PurchaseClient is null")
                            .build();
                    try {
                        listener.onConsumeResponse(iapResult, new PurchaseData(""));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        };

        executeServiceRequest(iabRunnable);
    }

    private void executeServiceRequest(IabRunnable runnable){
        if(mIsServiceConnected){
            runnable.run(null);
        }else {
            //if billing service was disconnected,we try to reconnect 1 time.
            startServiceConnection(runnable);
        }
    }

    /********************************* onPurchasesUpdated *************************************/

    /**
     * purchased callback
     * with pending transaction,this will be called multiply times!
     * @param iapResult
     * @param list
     */
    @Override
    public void onPurchasesUpdated(IapResult iapResult, List<PurchaseData> list) {
        CTILog.i(TAG, "onPurchasesUpdated");
        if(mPurchaseListener != null){
            mPurchaseListener.onPurchaseResponse(iapResult, list);
        }
    }

    /********************************* API Callback *************************************/

    interface IabRunnable{
        void run(IapResult result);
    }

    //set up callback
    public interface OnIabSetupFinishedListener{
        void onIabSetupFinished(IapResult result);
    }

    public interface OnIabQueryProductDetailsListener {
        void onIabQueryProductDetailsResponse(IapResult result, List<ProductDetail> productDetailsList);
    }

    public interface OnIabQueryPurchasesListener {
        void OnIabQueryPurchasesResponse(IapResult result, List<PurchaseData> productDetailsList);
    }

    public interface OnIabPurchaseListener {
        void onPurchaseResponse(IapResult result, List<PurchaseData> list);
    }

    public interface OnIabConsumeListener {
        void onConsumeResponse(IapResult result, PurchaseData purchaseData);
    }
}
