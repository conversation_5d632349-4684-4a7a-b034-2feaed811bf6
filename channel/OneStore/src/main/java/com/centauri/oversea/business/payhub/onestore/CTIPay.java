package com.centauri.oversea.business.payhub.onestore;

import android.app.Activity;
import android.os.Message;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.pay.CTIPayBaseChannel;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.CTIPayInfo;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.gaa.sdk.iap.AcknowledgeParams;
import com.gaa.sdk.iap.ConsumeParams;
import com.gaa.sdk.iap.IapResult;
import com.gaa.sdk.iap.IapResultListener;
import com.gaa.sdk.iap.ProductDetail;
import com.gaa.sdk.iap.ProductDetailsParams;
import com.gaa.sdk.iap.PurchaseClient;
import com.gaa.sdk.iap.PurchaseData;
import com.gaa.sdk.iap.PurchaseFlowParams;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class CTIPay extends CTIPayBaseChannel
        implements PurchaseHelper.OnIabSetupFinishedListener, PurchaseHelper.OnIabQueryProductDetailsListener,
        PurchaseHelper.OnIabPurchaseListener, PurchaseHelper.OnIabConsumeListener {

    private final String TAG = "OneStore_CTIPay";

    private String productId = "";
    private String productType = PurchaseClient.ProductType.INAPP;

    private PurchaseHelper mPurchaseHelper = null;
    private PurchaseData mPurchase;

    @Override
    protected void init() {
        BillingFlowParams request = mModel.getRequest();
        productId = request.getProductID();
        productType = request.isAutoPay() ? PurchaseClient.ProductType.AUTO : PurchaseClient.ProductType.INAPP;

        mPurchaseHelper = new PurchaseHelper(mView.getActivity());
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_CALL, "name=startSetup");
        mPurchaseHelper.startSetup(this);
    }

    @Override
    public void prePay() {
        ArrayList<String> productIdList = new ArrayList<String>();
        productIdList.add(productId);
        ProductDetailsParams productDetailsParams = ProductDetailsParams.newBuilder()
                .setProductType(productType)
                .setProductIdList(productIdList)
                .build();

        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_CALL, "name=queryProductDetailsAsync");
        mPurchaseHelper.queryProductDetailsAsync(productDetailsParams, this);
    }

    @Override
    public void pay(Activity act, JSONObject obj) {
        CTILog.d(TAG,"pay user_name============"+ GlobalData.singleton().getUserName());

        // 将 order_id 以 HashMap Json 格式放入支付中用于后台验证
        HashMap<String,String> devPayloadMap = new HashMap<String,String>();
        devPayloadMap.put("order_id",mModel.getBillNo());
        JSONObject devPayloadJson = new JSONObject(devPayloadMap);
        String devPayload = devPayloadJson.toString();

        CTILog.d(TAG,"devPayload :"+ devPayload);

        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_CALL, "name=launchPurchaseFlow");

        PurchaseFlowParams params = PurchaseFlowParams.newBuilder()
                .setProductId(productId)    // productDetail.getProductId()
                .setProductName("")
                .setProductType(productType)
                .setDeveloperPayload(devPayload)
                .build();

        mPurchaseHelper.launchPurchaseFlow(mView.getActivity(), params, this);
    }

    @Override
    public void postPay() {
        if (PurchaseClient.ProductType.AUTO.equals(productType)) {    //Subscription does not need to be consumed
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC, 0,null);
        } else {
            CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_CALL, "name=consumeParams");
            ConsumeParams consumeParams = ConsumeParams.newBuilder()
                    .setPurchaseData(mPurchase)
                    .build();
            mPurchaseHelper.consumeAsync(consumeParams, this);
        }
    }

    /****************************** override ****************************************/

    @Override
    protected boolean isSdkProvide() {
        return true;
    }

    @Override
    protected String getProductType() {
        return productType;
    }

    @Override
    public void dispose() {
        CTILog.i(TAG, "dispose()");
        super.dispose();
        if (mPurchaseHelper != null) {
            mPurchaseHelper.dispose();
            mPurchaseHelper = null;
        }
        mPurchase = null;
    }

    /****************************** listeners ****************************************/

    @Override
    public void onIabSetupFinished(IapResult result) {
        CTILog.d(TAG,"onIabSetupFinished:"+((result == null)?"null":result.toString()));
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onIabSetupFinished&result="+result.getResponseCode()+"&msg="+result.getMessage());
        if(result.isSuccess()){
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_SUCC,0,null);
            reportResult("initsucc","");
        }else{
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_ERROR,result.getResponseCode(),result.getMessage());
            reportResult("initerr",result.getMessage());
        }
    }

    @Override
    public void onIabQueryProductDetailsResponse(IapResult result, List<ProductDetail> productDetailsList) {
        CTILog.d(TAG,"onQueryProductDetailsResponse:"+((result == null)?"null":result.toString()));
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onQueryProductDetailsResponse&result="+result.getResponseCode()+"&msg="+result.getMessage()+"&purchaseList="+((productDetailsList == null)?"null":productDetailsList.toString()));
        if(result.isSuccess()){
            onQueryProductDetailsSuccess(result, productDetailsList);
            reportResult("prepaysucc","");
        }else{
            onQueryProductDetailsFailed(result);
            reportResult("prepayerr",result.getMessage());
        }
    }

    @Override
    public void onPurchaseResponse(IapResult result, List<PurchaseData> list) {
        CTILog.d(TAG,"onPurchaseResponse:"+(result == null?"null":result.toString()));
        CTILog.d(TAG,"purchasesList:"+((list == null)?"null":list.toString()));

        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onPurchaseResponse&result="+result.getResponseCode()+"&msg="+result.getMessage()+"&purchasesList="+ ((list == null)?"null":list.toString()));

        if(result.isSuccess()){
            onPurchaseSuccess(result, list);
            reportResult("paysucc",result.getMessage());
        }else {
            onPurchaseFailed(result);
            reportResult("payerr",result.getMessage());
        }

    }

    @Override
    public void onConsumeResponse(IapResult result, PurchaseData purchaseData) {
        CTILog.d(TAG,"onConsumeResponse:"+((result == null)?"null":result.toString()));

        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onPurchaseResponse&result="+result.getResponseCode()+"&msg="+result.getMessage()+"&purchaseData=" + purchaseData.toString());


        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_CONSUME_RESULT,
                "result="+(result==null?"null":result.getResponseCode())+"&msg="+(result==null?"null":result.getMessage())+"&billno="+mModel.getBillNo()+"&productid="+mModel.getRequest().getProductID());

        if (result.isSuccess()) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC, 0,purchaseData.toString());

            reportResult("consumesucc",result.getMessage());
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_ERROR, 0,result.getMessage());

            reportResult("consumeerr",result.getMessage());
        }
    }

    /****************************** api result ***************************************/

    private void onQueryProductDetailsSuccess(IapResult result, List<ProductDetail> productDetailsList) {
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onQueryProductDetailsSuccess&result="+result.getResponseCode()+"&productDetailsList="+productDetailsList);
        if(productDetailsList != null && !productDetailsList.isEmpty()){
            for(ProductDetail productDetail : productDetailsList){
                if (productId.equals(productDetail.getProductId())) {
                    CTILog.i(TAG, productDetail.toString());

                    //Generate order information
                    CTIPayInfo info = new CTIPayInfo();
                    info.currency = productDetail.getPriceCurrencyCode();
                    info.amount = CTITools.urlEncode(productDetail.getPrice(), 1);
                    info.ext = CTITools.urlEncode(String.valueOf(productDetail.getPriceAmountMicros()), 1);
                    sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC, 0,info);
                }
            }
        }else {
            //query exception,still order
            onQueryProductDetailsFailed(result);
        }
    }

    private void onQueryProductDetailsFailed(IapResult result) {
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onQueryProductDetailsFailed&result="+result.getResponseCode());

        String defInfo = productId + " query failed";
        sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_ERROR,0, defInfo);
    }

    private void onPurchaseSuccess(IapResult result, List<PurchaseData> list) {
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onPurchaseSuccess&result="+result.getResponseCode()+"&purchasesList="+list);
        if(list != null && !list.isEmpty()){
            for(PurchaseData purchaseData : list){
                if(productId.equals(purchaseData.getProductId())) {
                    mPurchase = purchaseData;
                    CTIPayReceipt rec = new CTIPayReceipt();
                    String sigString = purchaseData.getSignature();
                    String data = purchaseData.getOriginalJson();
                    CTILog.i(TAG, "Signature: " + sigString);
                    CTILog.i(TAG, "OriginalJson: " + data);
                    String base64dataString = CTIBase64.encode(data.getBytes());
                    rec.receipt = base64dataString;
                    rec.receipt_sig = sigString;
                    sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC,0, rec);
                    CTILog.i(TAG, "purchase info: " + purchaseData.toString());
                    
                    // There is a problem with the Purchase status of OneStore in this version and cannot be displayed normally, so the Purchase status is not checked.

                    //purchased
//                    if (purchaseData.getPurchaseState() == PurchaseData.PurchaseState.PURCHASED) {
//                        CTIPayReceipt rec = new CTIPayReceipt();
//                        String sigString = purchaseData.getSignature();
//                        String data = purchaseData.getOriginalJson();
//                        String base64dataString = CTIBase64.encode(data.getBytes());
//                        rec.receipt = base64dataString;
//                        rec.receipt_sig = sigString;
//                        //rec.sku = purchaseData.getProductId();
//                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC,0, rec);
//                        CTILog.i(TAG, "purchase info: " + purchaseData.toString());
//                    } else {
//                        CTILog.e(TAG,"Purchased State Error: "+purchaseData.getPurchaseState());
//                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, result.getResponseCode() ,result.getMessage());
//                    }
                }
            }
        }else{
            //The fourth mode of subscription upgrade will come here
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC,100, "purchasesList is null");
        }
    }

    private void onPurchaseFailed(IapResult result) {
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=onPurchaseFailed&result="+result.getResponseCode());

        if (result.getResponseCode() == PurchaseClient.ResponseCode.RESULT_USER_CANCELED){
            sendMesUIH(MConstants.MSG_PAYCHANNEL_CANCEL,MRetCode.ERR_CANCEL,result.getMessage());
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,result.getResponseCode(),result.getMessage());
        }
    }


    /****************************** private ****************************************/

    private void sendMesUIH(int what,int arg1,Object obj){
        if(UIHandler != null) {
            Message msg = new Message();
            msg.what = what;
            msg.arg1 = arg1;
            msg.obj = obj;
            UIHandler.sendMessage(msg);
        }
    }

    private void reportResult(String name,String result) {
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESULT, "name="+name+"&result="+result);
    }

}
