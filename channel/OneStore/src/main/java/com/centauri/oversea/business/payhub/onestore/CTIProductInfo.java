package com.centauri.oversea.business.payhub.onestore;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.centauri.comm.CTILog;
import com.centauri.oversea.api.request.CTIGameRequest;
import com.centauri.oversea.business.IGetProduct;
import com.centauri.oversea.newapi.response.InfoCallback;
import com.gaa.sdk.iap.ConsumeParams;
import com.gaa.sdk.iap.IapResult;
import com.gaa.sdk.iap.ProductDetail;
import com.gaa.sdk.iap.ProductDetailsParams;
import com.gaa.sdk.iap.PurchaseClient;
import com.gaa.sdk.iap.PurchaseData;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CTIProductInfo
        implements IGetProduct, PurchaseHelper.OnIabSetupFinishedListener, PurchaseHelper.OnIabQueryProductDetailsListener {

    private final static String TAG = "OneStore_CTIProductInfo";
    private PurchaseHelper mPurchaseHelper;
    private InfoCallback mCallback;
    private HashMap<String, String> mProducts = null;
    private Context mContext;

    private final static String SUCCESS = "0";
    private final static String ERROR = "-1";
    private final static String ERROR_DATA = "-2";
    private final static String ERROR_PARAM = "-3";
    private Handler mHandler = new Handler();


    //callback count
    private int callbackCount = 1;
    private final int CALLBACK_TIMES = 2;
    //callback result
    private JSONArray jsRes = new JSONArray();

    public void getProductInfo(Context context, HashMap<String, String> productIdMap, InfoCallback callback) {
        mCallback = callback;
        mProducts = productIdMap;
        mContext = context;
        CTILog.d(TAG, "init");
//        mPurchaseHelper = new PurchaseHelper(context);
        if (mProducts != null && !mProducts.isEmpty()) {
//
            init(mContext, new PurchaseHelper.OnIabSetupFinishedListener() {
                @Override
                public void onIabSetupFinished(IapResult result) {
                    CTILog.i(TAG, "onIabSetupFinished:" + result);
                    if (result.isSuccess()) {
                        queryProductsInfo();
                    } else {
                        callback(ERROR, result.getMessage());
                    }
                }
            });
        } else {
            callback(ERROR_PARAM, "query productList is empty");
        }
    }

    @Override
    public void getProductInfo(Activity activity, HashMap<String, String> productIdMap, InfoCallback callback) {

    }

    @Override
    public void getProductInfo(Activity activity, CTIGameRequest ctiGameRequest, InfoCallback infoCallback) {

    }

    @Override
    public void clearPurchase(Context context) {

        mPurchaseHelper = new PurchaseHelper(context);
        try {
            //query purchases
            mPurchaseHelper.queryPurchasesAsync(new PurchaseHelper.OnIabQueryPurchasesListener() {
                @Override
                public void OnIabQueryPurchasesResponse(IapResult result, List<PurchaseData> productDetailsList) {
                    CTILog.i(TAG, "onQueryPurchasesResponse: " + result);
                    if (result.isSuccess()) {
                        if (productDetailsList != null && !productDetailsList.isEmpty()) {
                            for (PurchaseData purchaseData : productDetailsList) {
                                CTILog.i(TAG, purchaseData.toString());

                                // There is a problem with the Purchase status of OneStore in this version and cannot be displayed normally, so the Purchase status is not checked.
//                                if (purchaseData.getPurchaseState() == PurchaseData.PurchaseState.PURCHASED) {
                                    ConsumeParams consumeParams = ConsumeParams.newBuilder()
                                            .setPurchaseData(purchaseData)
                                            .build();
                                    mPurchaseHelper.consumeAsync(consumeParams, new PurchaseHelper.OnIabConsumeListener() {
                                        @Override
                                        public void onConsumeResponse(IapResult result, PurchaseData purchaseData) {
                                            CTILog.d(TAG, "clearPurchase onConsumeResponse: " + result + " | purchaseData : " + purchaseData);

                                        }
                                    });
//                                }
                            }
                        }
                    }

                }
            });
        } catch (Exception ex) {
            ex.printStackTrace();
        }



    }

    private void init(Context context, PurchaseHelper.OnIabSetupFinishedListener listener) {
        CTILog.d(TAG, "init");
        mPurchaseHelper = new PurchaseHelper(context);
        mPurchaseHelper.startSetup(listener);
    }

    //Get the productlist by category from the map
    private List<String> getListByTypeFromMap(HashMap<String, String> map, String type) {

        if (map == null || map.isEmpty()) {
            return null;
        }

        List<String> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {

            if (entry.getValue().equals(type)) {
                list.add(entry.getKey());
            }
        }
        return list;
    }

    private void queryProductsInfo() {
        CTILog.d(TAG, "queryProductsInfo");

        List<String> inappList = getListByTypeFromMap(mProducts, PurchaseClient.ProductType.INAPP);
//        List<String> subsList = getListByTypeFromMap(mProducts, PurchaseClient.ProductType.AUTO);

        ProductDetailsParams inAppParams = ProductDetailsParams.newBuilder()
                .setProductType(PurchaseClient.ProductType.INAPP)
                .setProductIdList(inappList)
                .build();

//        ProductDetailsParams subsParams = ProductDetailsParams.newBuilder()
//                .setProductType(PurchaseClient.ProductType.AUTO)
//                .setProductIdList(subsList)
//                .build();


        if (null == mPurchaseHelper) {
            callback(ERROR, "billingHelper is null");
            return;
        }
        //query in-apps
        mPurchaseHelper.queryProductDetailsAsync(inAppParams, this);;
        //query subs
//        mPurchaseHelper.queryProductDetailsAsync(subsParams, this);
    }

    @Override
    public void onIabSetupFinished(IapResult result) {

    }

    @Override
    public void onIabQueryProductDetailsResponse(IapResult result, List<ProductDetail> productDetailsList) {
        CTILog.i(TAG, "onSkuDetailsResponse:" + result + ",productDetailsList=" + productDetailsList);

        if (result.isSuccess()) {
            if(productDetailsList != null && !productDetailsList.isEmpty()) {
                try {
                    for (ProductDetail productDetails : productDetailsList) {
                        JSONObject item = new JSONObject();
                        item.put("productId", productDetails.getProductId());
                        item.put("price", productDetails.getPrice());
                        item.put("currency", productDetails.getPriceCurrencyCode());
                        item.put("microprice", productDetails.getPriceAmountMicros());
                        item.put("originalPrice", productDetails.getPrice());
                        item.put("originalMicroprice", productDetails.getPriceAmountMicros());
                        item.put("productType", productDetails.getType());
                        jsRes.put(item);
                    }

                } catch (JSONException e) {
                    CTILog.e(TAG, "onSkuDetailsResponse exception: " + e.getMessage());
                }
                callback(SUCCESS, "success");
            } else {
                callback(ERROR_DATA, "productDetailsList is empty!");
            }

        } else{
            callback(ERROR, result.getMessage());
        }
    }

    private void callback(String retCode, String retMsg) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("ret", retCode);
            obj.put("msg", retMsg);
            obj.put("productInfo", jsRes);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Log.d(TAG, "callback:" + obj.toString());
        if (mCallback != null) {
            mCallback.callback(obj.toString());
        }

    }

}
