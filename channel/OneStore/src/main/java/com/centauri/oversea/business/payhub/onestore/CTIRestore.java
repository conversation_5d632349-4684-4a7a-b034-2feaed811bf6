package com.centauri.oversea.business.payhub.onestore;

import android.content.Context;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.CTIBaseRestore;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.ICallback;
import com.centauri.oversea.newapi.response.IGetPurchaseCallback;
import com.gaa.sdk.iap.ConsumeParams;
import com.gaa.sdk.iap.IapResult;
import com.gaa.sdk.iap.PurchaseClient;
import com.gaa.sdk.iap.PurchaseData;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;



public class CTIRestore extends CTIBaseRestore
        implements PurchaseHelper.OnIabSetupFinishedListener, PurchaseHelper.OnIabQueryPurchasesListener,
        PurchaseHelper.OnIabConsumeListener {
    public final static String TAG = "OneStore_CTIRestore";

    private PurchaseHelper mPurchaseHelper;
    private ICallback initCallback;
    private ICTICallback postProvideCallback;
    private IGetPurchaseCallback getPurchaseCallback;

    //Call back external content, json format
    private JSONObject callbackJs = new JSONObject();

    //need consume purchase size
    private int mTotalConsumed = 0;
    //current consume index
    private int mCurrentIndex = 0;
    //query purchase list
    private List<PurchaseData> mPurchases = new ArrayList<PurchaseData>();

    //consume end report content
    private StringBuilder consumeReport = new StringBuilder();
    //callback
    private JSONArray consumeListJson = new JSONArray();


    @Override
    public void init(Context context, ICallback callback) {
        initCallback = callback;

        mPurchaseHelper = new PurchaseHelper(context);;
        mPurchaseHelper.startSetup(this);
//        initCallback.callback(MRetCode.OK);
    }


    @Override
    public void getPurchaseList(Context context, final IGetPurchaseCallback callback) {
        getPurchaseCallback = callback;

        if(mPurchaseHelper != null){

            try{
                //query purchases
                mPurchaseHelper.queryPurchasesAsync(this);
            }catch (Exception ex){
                ex.printStackTrace();
                callback.callback(null);
                reportEnd("queryEnd",ex.getMessage());
            }
        }else{
            callback.callback(null);
            reportEnd("queryEnd","mHelper is null or mHelper not setup.");
        }

    }


    @Override
    public void postProvide(List<String> list, ICTICallback callback) {
        postProvideCallback = callback;
        if (list == null || list.isEmpty()) {
            callbackSuper(MRetCode.ERR_OTHER);
            return;
        }

        /**
         * add need consumed purchases.
         * as Purchases doesn't specify product type,so added purchases maybe subs.
         */
        List<PurchaseData> consumeList = new ArrayList<PurchaseData>();
        for (String productId : list) {
            for (PurchaseData purchaseData : mPurchases) {
                if (purchaseData.getProductId() == productId) {
                    consumeList.add(purchaseData);
                    CTILog.d(TAG, "consumeList add:" + productId);
                }
            }
        }

        CTILog.d(TAG, "consumeList size:" + consumeList.size());
        if (!consumeList.isEmpty()) {
            consumeAsync(consumeList);
        } else {
            callbackSuper(MRetCode.OK);
        }
    }

//    consume
    private void consumeAsync(List<PurchaseData> consumeList) {
        mTotalConsumed = consumeList.size();

        for (PurchaseData purchaseData : consumeList) {
            ConsumeParams consumeParams = ConsumeParams.newBuilder()
                    .setPurchaseData(purchaseData)
                    .build();

            mPurchaseHelper.consumeAsync(consumeParams, this);
        }
    }


    @Override
    public void dispose() {
        CTILog.i(TAG, "dispose()");
        if (mPurchaseHelper != null) {
            mPurchaseHelper.dispose();
            mPurchaseHelper = null;
        }

        if (!mPurchases.isEmpty()) {
            mPurchases.clear();
        }

        super.dispose();
    }

    /****************************** listeners ****************************************/
    @Override
    public void onIabSetupFinished(IapResult result) {
        CTILog.d(TAG, "onIabSetupFinished: " + result);


        if (result.isSuccess()) {
            initCallback.callback(MRetCode.OK);
        } else {
            initCallback.callback(MRetCode.ERR_OTHER);
        }

        reportEnd("initEnd", String.valueOf(result.getResponseCode()));
    }


    @Override
    public void OnIabQueryPurchasesResponse(IapResult result, List<PurchaseData> purchasesList) {
        CTILog.i(TAG, "onQueryPurchasesResponse: " + result);

        if (result.isSuccess()) {
            if (purchasesList != null && !purchasesList.isEmpty()) {
                ArrayList<CTIPayReceipt> receiptList = new ArrayList<CTIPayReceipt>();
                for (PurchaseData purchaseData : purchasesList) {
                    CTILog.i(TAG,purchaseData.toString());

                    // There is a problem with the Purchase status of OneStore in this version and cannot be displayed normally, so the Purchase status is not checked.

//                    if (purchaseData.getPurchaseState() == PurchaseData.PurchaseState.PURCHASED) {
                        mPurchases.add(purchaseData);

                        CTIPayReceipt rec = new CTIPayReceipt();
                        rec.receipt = CTIBase64.encode(purchaseData.getOriginalJson().getBytes());
                        rec.receipt_sig = purchaseData.getSignature();
                        rec.orderId = purchaseData.getOrderId();
                        rec.productId = purchaseData.getProductId();
                        rec.sku.add(rec.productId);
                        receiptList.add(rec);   //Add to bill list
//                    }
                }

                getPurchaseCallback.callback(receiptList);
            } else {
                getPurchaseCallback.callback(null);
            }
        } else {
            getPurchaseCallback.callback(null);
        }

        reportEnd("queryEnd", result.getResponseCode() + "_" + mPurchases.size()+"_"+mPurchases.toString());
    }

    @Override
    public void onConsumeResponse(IapResult result, PurchaseData purchaseData){
        CTILog.d(TAG, "onConsumeResponse: " + result + ",mCurrentIndex=" + mCurrentIndex);

        //set report content
        consumeReport.append(purchaseData.getOrderId())
                .append("|")
                .append(result.isSuccess())
                .append("|");


        try {
            JSONObject item = new JSONObject();
            item.put("productid", purchaseData.getProductId());
            item.put("orderId", purchaseData.getOrderId());
            consumeListJson.put(item);
        } catch (JSONException e) {
            CTILog.e(TAG, "JSONException1: " + e.getMessage());
        }

        //callback
        if (++mCurrentIndex == mTotalConsumed) {
            reportEnd("consumeEnd", consumeReport.toString());

            try {
                if (consumeListJson.length() != 0) {
                    addCallbackItem("ret",MRetCode.OK);
                    addCallbackItem("paychannelid", "onestore");
                    addCallbackItem("products", consumeListJson);
                }
            } catch (JSONException e) {
                CTILog.e(TAG, "JSONException2: " + e.getMessage());
            }

            callbackSuper(MRetCode.OK);
        }
    }

    private void addCallbackItem(String key, Object value)
            throws JSONException {
        if (callbackJs == null) {
            callbackJs = new JSONObject();
        }

        callbackJs.put(key, value);
    }

//    //Add shipping subscription bill information
//    private void addSubInfo() {
//        if (provideSdkRetMap != null && !provideSdkRetMap.isEmpty()) {
//            Iterator<String> _it = provideSdkRetMap.keySet().iterator();
//            try {
//                JSONArray jsSubArray = new JSONArray();
//                while (_it.hasNext()) {
//                    String productId = _it.next();
//                    String provideSdkRet = provideSdkRetMap.get(productId);
//
//                    //gw_subscription
//                    JSONObject _jSdkRet = new JSONObject(provideSdkRet);
//                    if (_jSdkRet.has("gw_subscription")) {
//                        JSONObject _jSubItem = _jSdkRet.getJSONObject("gw_subscription");
//                        _jSubItem.put("productId", productId);
//                        //add to JSONArray
//                        jsSubArray.put(_jSubItem);
//                    }
//
//                }
//
//                //add to callbackJs
//                if (jsSubArray.length() > 0) {
//                    addCallbackItem("subInfo", CTIBase64.encode(jsSubArray.toString().getBytes()));
//                }
//            } catch (JSONException e) {
//                CTILog.d(TAG, "addSubInfo exception: " + e.getMessage());
//            }
//        }
//    }

    private void callbackSuper(int retCode) {
        if (postProvideCallback != null) {
            //addSubInfo();
            if (callbackJs != null && callbackJs.length() > 0) {
                postProvideCallback.callback(retCode, callbackJs.toString());
            } else {
                postProvideCallback.callback(retCode, DEFAULT_RET_MSG);
            }
        }
    }


    private void reportEnd(String name, String retMsg) {
        CTIDataReportManager.instance().insertData(CTIChannel.SDK_OVERSEA_ONESTORE_CTICTIPAY_RESPONSE,
                "name=" + name + "&result=" + retMsg); //Report interface duration
    }


}
