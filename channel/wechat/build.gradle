apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        versionCode rootProject.ext.centauriWxVersionCode
        versionName rootProject.ext.centauriWxVersionName
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
    }

    /**
     * build aar and rename aar by dongbingliu 2020-10-30 星期五
     */
    setVersion(defaultConfig.versionName)
    libraryVariants.all { variant ->
        if (variant.buildType.name == 'release') {
            variant.outputs.all {
                outputFileName = "centauri-wx-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.aar"
            }
        }else{
            variant.outputs.all {
                outputFileName = "centauri-wx-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-debug.aar"
            }
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.javaCompileVersion
        targetCompatibility rootProject.ext.javaCompileVersion
    }
    lintOptions {
        abortOnError false
    }
//    packageBuildConfig = false
    buildFeatures {
        buildConfig = false
    }
}

task ctiChannelGwJar(type: Jar, dependsOn: "assembleRelease") {
    //jar package name
    archivesBaseName = "centauri-wx-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}"
    //路径会跟随gradle版本变更而变化
    from "build/intermediates/javac/release/classes"
    include('com/')

    //依赖的maven仓库包打进一个jar包
//    from {configurations.myConfig.collect { it.isDirectory() ? it : zipTree(it) }}

    exclude('**/test/*.class', '**/sample/*.class')
    exclude "**/R.class"
    exclude "**/R\$*.class"
    exclude('**/BuildConfig.class')

    // copy 至ctiLibs目录,build/libs目录是中间目录,编译时候自动删除不可控
    copy {
        String libDir = file('build/ctiLibs')
        from("build/libs")
        into("build/ctiLibs")
    }
}


dependencies {
    implementation fileTree(dir: 'libs', include: '*.jar')
    implementation project(':Core')

    compileOnly 'com.centauri.comm:centauricommon-log:2.0.3'
    api 'com.tencent.mm.opensdk:wechat-sdk-android:+'
}
