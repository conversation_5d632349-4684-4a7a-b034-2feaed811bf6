<?xml version="1.0" encoding="utf-8"?>
<manifest package="com.centauri.oversea.wechat.lib"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <queries>
        <package android:name="com.tencent.mm" />
    </queries>
    <application>
<!--        <activity-->
<!--            android:name="com.centauri.oversea.business.payhub.mp.wc.wxapi.WXEntryActivity"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar"-->
<!--            android:exported="true"-->
<!--            android:taskAffinity="com.tencent.xrplatform.mobile_app"-->
<!--            android:launchMode="singleTask">-->
<!--        </activity>-->
    </application>
</manifest>