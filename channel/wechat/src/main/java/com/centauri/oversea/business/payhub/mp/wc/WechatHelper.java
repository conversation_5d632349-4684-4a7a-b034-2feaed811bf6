package com.centauri.oversea.business.payhub.mp.wc;

import android.content.Context;

import com.tencent.mm.opensdk.constants.Build;
import com.tencent.mm.opensdk.modelbiz.WXOpenBusinessView;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

/**
 * <AUTHOR>
 * @Date 05/12/2023
 */
public class WechatHelper {
    // APP_ID 替换为你的应用从官方网站申请到的合法appID
    private String APP_ID = "";

    // IWXAPI 是第三方app和微信通信的openApi接口
    private IWXAPI api;

    private Context context;
    private String sessionID;
    private OnWechatPayCallBack onWechatPayCallBack;
    private static WechatHelper INSTANCE;

    private WechatHelper() {

    }

    public static WechatHelper getInstance() {
        if (null == INSTANCE) {
            synchronized (WechatHelper.class) {
                if (null == INSTANCE) {
                    INSTANCE = new WechatHelper();
                }
            }
        }
        return INSTANCE;
    }

    public void init(Context context, String appId) {
        APP_ID = appId;
        this.context = context;
        // 通过WXAPIFactory工厂，获取IWXAPI的实例
        api = WXAPIFactory.createWXAPI(context, APP_ID, true);

        // 将应用的appId注册到微信
        api.registerApp(APP_ID);

        //建议动态监听微信启动广播进行注册到微信
//        registerReceiver(new BroadcastReceiver() {
//            @Override
//            public void onReceive(Context context, Intent intent) {
//
//                // 将该app注册到微信
//                api.registerApp("Constants.APP_ID");
//            }
//        }, new IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP));

    }

    public boolean signWechat(String sessionID, OnWechatPayCallBack onWechatPayCallBack) {
        Boolean ret;
        if (null == api) {
            api = WXAPIFactory.createWXAPI(context, APP_ID, true);
        }
        this.sessionID = sessionID;
        this.onWechatPayCallBack = onWechatPayCallBack;
        // 发起签约
        int wxSdkVersion = api.getWXAppSupportAPI();
        if (wxSdkVersion >= Build.OPEN_BUSINESS_VIEW_SDK_INT) {
            WXOpenBusinessView.Req req = new WXOpenBusinessView.Req();
            req.businessType = "wxpayOverseaEntrustAuthorization"; // 固定值
//            String SesssionId = "20211115171992080208"; // 预签约会话ID，通过下述预签约API获取
            req.query = "sessionId=" + sessionID; // 拼接参数
            req.extInfo = "{\"miniProgramType\": 0}"; // 固定值
            ret = api.sendReq(req);
        } else {
            // 微信版本太低，在这里提示用户升级后再完成签约
            ret = false;
        }
        return ret;
    }

    public void onResp(WXOpenBusinessView.Resp resp) {

        if (null != onWechatPayCallBack) {
            onWechatPayCallBack.onSignResp(resp);
        }

    }

    public IWXAPI getApi() {
        return api;
    }

    public void unRegister() {

    }

    public void removeOnWechatPayCallBack() {
        onWechatPayCallBack = null;
    }

    public interface OnWechatPayCallBack{
        void onSignResp(WXOpenBusinessView.Resp resp);
    }
}
