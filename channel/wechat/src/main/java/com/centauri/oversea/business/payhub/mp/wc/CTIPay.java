package com.centauri.oversea.business.payhub.mp.wc;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.pay.CTIPayBaseChannel;
import com.centauri.oversea.business.pay.CTIPayBaseView;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.WXOpenBusinessView;

import org.json.JSONObject;

import java.util.HashMap;

/**
 * @<PERSON> kalwang
 * @Date 05/12/2023
 */
public class CTIPay extends CTIPayBaseChannel implements WechatHelper.OnWechatPayCallBack {
    private final String TAG = "CTIPay";

    private String appId = "";
    private String openId = "";
    private String productId = "";

    private String bizType = "";//bizType:sign签约, pay支付


    private String channelExtras = "";
    private String sessionId = "";
    private WechatHelper wechatHelper;

    @Override
    public void init(CTIPayBaseView view) {
        super.init(view);
    }

    @Override
    public void startPayCheckEnv() {
        super.startPayCheckEnv();
    }

    @Override
    public void startPay() {
        super.startPay();
    }

    @Override
    public int getOrderKey() {
        return super.getOrderKey();
    }

    @Override
    public void release() {
        super.release();
    }

    @Override
    protected void init() {
        BillingFlowParams request = mModel.getRequest();
        openId = GlobalData.singleton().openID;
        productId = request.getProductID();
        channelExtras = request.getExtra().getChannelExtras();
        HashMap<String, String> desParams = CTITools.kv2Map(channelExtras);
        sessionId = desParams.get("sessionId");
        appId = desParams.get("appId");
        bizType = desParams.get("bizType");
        CTILog.i(TAG,"bizType : " +bizType+"  ----  sId : " +sessionId+" ---- aId : " +appId);
        if (TextUtils.isEmpty(appId)) {
            reportResult("initerr", "appId null");
            reportTime("initerr", MTimer.duration(MTimer.GW_PROCESS_INIT));
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_ERROR, -1, "");
            return;
        }
        if (TextUtils.isEmpty(sessionId)) {
            reportResult("initerr", "sessionId null");
            reportTime("initerr", MTimer.duration(MTimer.GW_PROCESS_INIT));
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_ERROR, -1, "");
            return;
        }
        WechatHelper.getInstance().init(mView.getActivity(), appId);
        super.init();
    }

    @Override
    public void prePay() {
        super.prePay();
    }

    @Override
    public void pay(Activity act, JSONObject obj) {
        super.pay(act, obj);
        if ("sign".equals(bizType)) {
            if (!WechatHelper.getInstance().signWechat(sessionId, this)) {
                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_W_SDK_RESPONSE,
                        "name=signWechat&result=-1" +
                                "&msg=");
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, -1, "");
            }
        }

    }

    @Override
    public void postPay() {
        super.postPay();
    }

    @Override
    protected JSONObject addChannelExtra(JSONObject in) {
        return super.addChannelExtra(in);
    }

    @Override
    protected String getProductType() {
        return super.getProductType();
    }

    @Override
    protected boolean hasGoodsList() {
        return super.hasGoodsList();
    }

    @Override
    protected boolean needOrder() {
        return false;
    }

    @Override
    protected void onSaveReceipt(CTIPayReceipt receipt) {
        super.onSaveReceipt(receipt);
    }

    @Override
    protected boolean needShowSucc() {
        return super.needShowSucc();
    }

    @Override
    protected boolean isSdkProvide() {
        return super.isSdkProvide();
    }

    @Override
    public boolean handleActivityResult(int requestCode, int resultCode, Intent data) {
        return super.handleActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void dispose() {
        super.dispose();
    }

    private void sendMesUIH(int what, int arg1, Object obj) {
        if (UIHandler != null) {
            Message msg = new Message();
            msg.what = what;
            msg.arg1 = arg1;
            msg.obj = obj;
            Bundle bundle = new Bundle();
            // resultCode -> InnerCode
            bundle.putString("msgErrCode", String.valueOf(arg1));
            msg.setData(bundle);
            UIHandler.sendMessage(msg);
        }
    }

    @Override
    public void onSignResp(WXOpenBusinessView.Resp resp) {

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_W_SDK_RESPONSE,
                "name=onSignResp&result=" + resp.errCode +
                        "&msg=" + resp.errStr);
        if (resp.errCode != BaseResp.ErrCode.ERR_OK) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_ERROR, resp.errCode, resp.errStr);
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC, 0, "");
        }
    }

    private void reportTime(String name, long times) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_W_TIME,
                "version=2.0&name=" + name
                        + "&times=" + times);
    }

    private void reportResult(String name, String result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_W_RESULT,
                "version=2.0&name=" + name
                        + "&result=" + result);
    }
}
