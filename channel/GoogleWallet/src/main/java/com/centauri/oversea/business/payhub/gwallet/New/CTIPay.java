package com.centauri.oversea.business.payhub.gwallet.New;

import android.app.Activity;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.android.billingclient.api.AccountIdentifiers;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingConfig;
import com.android.billingclient.api.BillingConfigResponseListener;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ConsumeParams;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.SkuDetails;
import com.android.billingclient.api.SkuDetailsParams;
import com.centauri.comm.CTILog;
import com.centauri.oversea.business.pay.CTIPayBaseChannel;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTIMD5;
import com.centauri.oversea.comm.CTISPTools;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.CTIPayInfo;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.params.BillingFlowParams;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 */
public class CTIPay extends CTIPayBaseChannel
        implements BillingHelper.OnIabSetupFinishedListener, BillingHelper.OnIabQueryPurchasesListener,
        BillingHelper.OnIabQuerySkuDetailsListener, BillingHelper.OnIabQueryProductDetailsListener, BillingHelper.OnIabPurchaseListener, BillingHelper.OnIabConsumeListener, BillingConfigResponseListener {

    private final String TAG = "APPay_New";

    private String openId = "";
    private String productId = "";
    private String offerToken = "";
    private String basePlanId = "";
    private String gw_offerId = "";
    private int selectedOfferIndex = 0;
    //whether is subscription
    private boolean isAutoPay = true;


    //centauri product type
    private String centauriType = "";
    //google product type
    private String googleType = BillingClient.SkuType.INAPP;
    //purchase sku details
    private SkuDetails mSkuDetails;         // 支付前拉到的物品
    private SkuDetails mSecondSkuDetails;   // 支付后拉到的物品

    private ProductDetails mProductDetails;         // GW5.1支付前拉到的物品
    private ProductDetails mSecondProductDetails;   // GW5.1支付后拉到的物品

    private int waitSecondSkuDetailsTime = 0;   //第二次拉物品信息的等待时间，由下单返回
    //purchase receipt
    private Purchase mPurchase;
    private Purchase mOldPurchase;

    //Support Google subscription upgrade mode, the default is 4
    private int SubcribeProrationMode = com.android.billingclient.api.BillingFlowParams.SubscriptionUpdateParams.ReplacementMode.DEFERRED;
    private String OldSku = "";
    private String gpCountryCode = "";

    private BillingHelper billingHelper = null;

    @Override
    protected void init() {
        MTimer.start(MTimer.GW_PROCESS_INIT);

        BillingFlowParams request = mModel.getRequest();
        openId = GlobalData.singleton().openID;
        productId = request.getProductID();
        isAutoPay = request.isAutoPay();
        centauriType = request.getType();
        googleType = isAutoPay ? BillingClient.SkuType.SUBS : BillingClient.SkuType.INAPP;
        //GW5.1
        basePlanId = request.getBasePlanId();
        gw_offerId = request.getGw_offerId();
        CTILog.i(TAG, "productId:" + productId + ",googleType: " + googleType + ",centauriType:" + centauriType + ",basePlanId:" + request.getBasePlanId() + ",offerId:" + request.getGw_offerId());

        billingHelper = BillingHelper.getInstance(mView.getActivity());
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=startSetup");
        if (null != billingHelper) {
            billingHelper.startSetup(this);
        }
    }

    @Override
    public void prePay() {
        MTimer.start(MTimer.GW_PROCESS_PREPAY);
        //query purchases first
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=queryPurchasesAsync");
        if (null != billingHelper) {
            billingHelper.queryPurchasesAsync(this);
        }
    }

    //query sku details
    private void querySkuDetails() {
        ArrayList<String> skuList = new ArrayList<String>();
        skuList.add(productId);

        SkuDetailsParams skuDetailsParams = SkuDetailsParams.newBuilder()
                .setType(googleType)
                .setSkusList(skuList)
                .build();

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=querySkuDetailsAsync");
        if (null != billingHelper) {
            billingHelper.querySkuDetailsAsync(skuDetailsParams, this);
        }
    }

    //query product details
    private void queryProductDetails() {
        List<QueryProductDetailsParams.Product> productList = new ArrayList<>();
        productList.add(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(productId)
                .setProductType(googleType)
                .build());
//        ImmutableList<QueryProductDetailsParams.Product> imProductList = ImmutableList.copyOf(productList);
        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();
        if (null != billingHelper) {
            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=queryProductDetailsAsync");
            billingHelper.queryProductDetailsAsync(params, this);
        }
    }

    @Override
    public void pay(Activity act, JSONObject obj) {
        if (obj == null ||
                TextUtils.isEmpty(obj.optString("channel_key"))) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
                    0,
                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
            reportResult("payerr", "pay info error");
            return;
        }

        if (billingHelper.isGw5Supported()) {
            newPay(act, obj);
        } else {
            oldPay(act, obj);
        }
    }

    public void newPay(Activity act, JSONObject obj) {
        // 从 obj(info)中取出 wait_time 作为 waitSecondSkuDetailsTime 的值
        waitSecondSkuDetailsTime = getWaitSecondSkuDetailsTime(obj);

        CTILog.d(TAG, "pay user_name============" + GlobalData.singleton().getUserName());

        MTimer.start(MTimer.GW_PROCESS_PAY);
        MTimer.start(MTimer.GW_PROCESS_SHOW_DIALOG);

        if (mProductDetails == null) {
            CTILog.i(TAG, "productid not found");
            int subErrCode = MRetCode.ERR_GW_BILLING_PAY_NOTFOUNDPRODUCTID;
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, "PRODUCTID NOT FOUND");
            return;
        }

        if (googleType != "inapp") {
            try {
                offerToken = mProductDetails
                        .getSubscriptionOfferDetails()
                        .get(selectedOfferIndex)
                        .getOfferToken();
            } catch (Exception e) {
                CTILog.e(TAG, "newPay(): " + e.getMessage());
                int subErrCode = MRetCode.ERR_GW_BILLING_PAY_NOTFOUNDPRODUCTID;
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, "PRODUCTID NOT FOUND");
                return;
            }
        }

        List<com.android.billingclient.api.BillingFlowParams.ProductDetailsParams> productDetailsParamsList = new ArrayList<>();
        try {
            com.android.billingclient.api.BillingFlowParams.ProductDetailsParams.Builder builder = com.android.billingclient.api.BillingFlowParams.ProductDetailsParams.newBuilder()
                    .setProductDetails(mProductDetails);
            if (googleType != "inapp") {
                builder.setOfferToken(offerToken);
            }
            productDetailsParamsList.add(builder.build());
        } catch (Exception e) {
            CTILog.e(TAG, "productDetailsParams: " + e.getMessage());
            int subErrCode = MRetCode.ERR_GW_BILLING_PAY_NOTFOUNDPRODUCTID;
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, "PRODUCTID NOT FOUND");
            return;
        }

        com.android.billingclient.api.BillingFlowParams.Builder billBuilder = com.android.billingclient.api.BillingFlowParams.newBuilder();
        billBuilder.setProductDetailsParamsList(productDetailsParamsList);
        billBuilder.setObfuscatedAccountId(GlobalData.singleton().getUserName());
        billBuilder.setObfuscatedProfileId(GlobalData.singleton().zoneID);
        //4_4_2 version starts to support subscription upgrade
        if (!TextUtils.isEmpty(OldSku)) {
            //Upgrade new parameters
            //Upgrade must find the original ticket
            if (mOldPurchase == null || TextUtils.isEmpty(mOldPurchase.getPurchaseToken())) {
                int subErrCode = MRetCode.ERR_GW_SUB_UPGRADE_OLD_PURCHASE_TOKEN_NULL;
                String mes = CTICommMethod.getStringId(mView.getActivity(), "unipay_error_sub_upgrade_purchase_null");
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, mes);
                return;
            }

            billBuilder.setSubscriptionUpdateParams(
                            com.android.billingclient.api.BillingFlowParams.SubscriptionUpdateParams.newBuilder()
                                    // purchaseToken can be found in Purchase#getPurchaseToken
                                    .setOldPurchaseToken(mOldPurchase.getPurchaseToken())
                                    .setReplaceProrationMode(SubcribeProrationMode)
                                    .build())
                    .build();
        }

        try {
            com.android.billingclient.api.BillingFlowParams billingFlowParams = billBuilder.build();

            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=launchPurchaseFlow");
            billingHelper.launchPurchaseFlow(mView.getActivity(), billingFlowParams, this);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
                    0,
                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
            reportResult("payerr", e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
                    0,
                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
            reportResult("payerr", e.getMessage());
        }
    }

    public void oldPay(Activity act, JSONObject obj) {
        // 从 obj(info)中取出 wait_time 作为 waitSecondSkuDetailsTime 的值
        waitSecondSkuDetailsTime = getWaitSecondSkuDetailsTime(obj);

        CTILog.d(TAG, "pay user_name============" + GlobalData.singleton().getUserName());

        MTimer.start(MTimer.GW_PROCESS_PAY);
        MTimer.start(MTimer.GW_PROCESS_SHOW_DIALOG);

        com.android.billingclient.api.BillingFlowParams.Builder billBuilder = com.android.billingclient.api.BillingFlowParams.newBuilder();
        billBuilder.setSkuDetails(mSkuDetails);
        billBuilder.setObfuscatedAccountId(GlobalData.singleton().getUserName());
        billBuilder.setObfuscatedProfileId(GlobalData.singleton().zoneID);
//        billBuilder.setDeveloperId(openId);//google payment risk evaluation 3.0去掉

        //4_4_2 version starts to support subscription upgrade
        if (!TextUtils.isEmpty(OldSku)) {
            //Upgrade new parameters
            //Upgrade must find the original ticket
            if (mOldPurchase == null || TextUtils.isEmpty(mOldPurchase.getPurchaseToken())) {
                int subErrCode = MRetCode.ERR_GW_SUB_UPGRADE_OLD_PURCHASE_TOKEN_NULL;
                String mes = CTICommMethod.getStringId(mView.getActivity(), "unipay_error_sub_upgrade_purchase_null");
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, mes);
                return;
            }
            billBuilder.setSubscriptionUpdateParams(
                    com.android.billingclient.api.BillingFlowParams.SubscriptionUpdateParams.newBuilder()
                            .setOldSkuPurchaseToken(mOldPurchase.getPurchaseToken())
                            .build());
//            billBuilder.setOldSku(OldSku, mOldPurchase.getPurchaseToken());//指定用户要升级或降级的SKU和购买令牌。这将消除设备上的多个帐户拥有相同sku的情况的歧义。
//            billBuilder.setReplaceSkusProrationMode(SubcribeProrationMode);
        }

        try {
            com.android.billingclient.api.BillingFlowParams billingFlowParams = billBuilder.build();

            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=launchPurchaseFlow");
            billingHelper.launchPurchaseFlow(mView.getActivity(), billingFlowParams, this);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
                    0,
                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
            reportResult("payerr", e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR,
                    0,
                    CTICommMethod.getStringId(act, "unipay_pay_error_tip"));
            reportResult("payerr", e.getMessage());
        }
    }

    @Override
    public void postPay() {
        if (BillingClient.SkuType.SUBS.equals(googleType)) {    //Subscription does not need to be consumed
            CTISPTools.putLong(CTISPTools.SP_NAME_PURCHASE, CTIMD5.toMd5(mPurchase.getOrderId().getBytes()), System.currentTimeMillis());
            CTISPTools.sortSPByPurchase();
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC, 0, null);

        } else {
            MTimer.start(MTimer.GW_PROCESS_POSTPAY);

            ConsumeParams consumeParams = ConsumeParams.newBuilder()
                    .setPurchaseToken(mPurchase.getPurchaseToken())
                    //.setDeveloperPayload(createDeveloperPayLoad())
                    .build();
            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=consumeParams");
            if (null != billingHelper) {
                billingHelper.consumeAsync(consumeParams, this);
            }
        }
    }

    /****************************** override ****************************************/

    @Override
    protected boolean isSdkProvide() {
        return true;
    }

    @Override
    protected String getProductType() {
        return googleType;
    }


    @Override
    protected JSONObject addChannelExtra(JSONObject in) {
        if (in == null) {
            in = new JSONObject();
        }

        try {
            //1. Add gp order number
            if (mPurchase != null && !TextUtils.isEmpty(mPurchase.getOrderId())) {
                in.put("gwalletOrderId", mPurchase.getOrderId());
            }

            //2. After the delivery is completed, the extended content issued in the background
            String provideSdkRet = mModel.getProvideSdkRet();
            if (!TextUtils.isEmpty(provideSdkRet)) {
                JSONObject js = new JSONObject(provideSdkRet);
                if (js.has("gw_subscription")) {
                    String gwSub = js.getJSONObject("gw_subscription").toString();
                    if (!TextUtils.isEmpty(gwSub)) {
                        //base64 encode
                        in.put("subInfo", CTIBase64.encode(gwSub.getBytes()));
                    }
                }
            }

        } catch (JSONException e) {
            CTILog.e(TAG, "addChannelExtra exception: " + e.getMessage());
        } catch (Exception e) {
            CTILog.e(TAG, "addChannelExtra exception: " + e.getMessage());
        }
        return in;
    }

    @Override
    public void dispose() {
        super.dispose();
//        if (billingHelper != null) {
//            billingHelper.dispose();
//            billingHelper = null;
//        }
        mPurchase = null;
        mSkuDetails = null;
        mProductDetails = null;
    }

    private void initSucc() {
        CTILog.d(TAG, "initSucc -------- ");
        MTimer.stop(MTimer.GW_PROCESS_INIT);
//        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
//                "name=onIabSetupFinished&result=" + result.resultCode() + "&msg=" + result.resultMsg());
        sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_SUCC, 0, null);

        reportResult("initsucc", "");
        reportTime("initsucc", MTimer.duration(MTimer.GW_PROCESS_INIT));
    }

    /****************************** listeners ****************************************/

    @Override
    public void onIabSetupFinished(WrapperBillingResult result) {
        CTILog.d(TAG, "onIabSetupFinished:" + ((result == null) ? "null" : result.toString()));
        MTimer.stop(MTimer.GW_PROCESS_INIT);
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onIabSetupFinished&result=" + result.resultCode() + "&msg=" + result.resultMsg());
        if (result.isSuccess()) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_SUCC, 0, null);

            reportResult("initsucc", "");
            reportTime("initsucc", MTimer.duration(MTimer.GW_PROCESS_INIT));
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_INIT_ERROR, result.unifyErrCode(), result.resultMsg());

            reportResult("initerr", result.resultMsg());
            reportTime("initerr", MTimer.duration(MTimer.GW_PROCESS_INIT));
        }
    }

    @Override
    public void onQueryPurchasesResponse(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTILog.d(TAG, "onQueryPurchasesResponse:" + ((result == null) ? "null" : result.toString()));
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryPurchasesResponse&result=" + result.resultCode() + "&msg=" + result.resultMsg() + "&purchaseList=" + ((purchasesList == null) ? "null" : purchasesList.toString()));
        if (result.isSuccess()) {
            if (billingHelper.isGw5Supported()) {
                onQueryPurchasesSuccess(result, purchasesList);
            } else {
                onQueryOldPurchasesSuccess(result, purchasesList);
            }
        } else {
            onQueryPurchasesFailed(result);
        }
    }

    @Override
    public void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
        CTILog.d(TAG, "onSkuDetailsResponse:" + ((result == null) ? "null" : result.toString()));
        MTimer.stop(MTimer.GW_PROCESS_PREPAY);
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onSkuDetailsResponse&result=" + result.resultCode() +
                        "&msg=" + ((result == null) ? "null" : result.toString()) + "&skuDetailsList="
                        + ((skuDetailsList == null) ? "null" : skuDetailsList.toString()));

        if (result.isSuccess()) {
            onQuerySkuDetailsSuccess(result, skuDetailsList);

            reportResult("prepaysucc", "");
            reportTime("prepaysucc", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
        } else {
            onQuerySkuDetailsFailed(result);

            reportResult("prepayerr", result.resultMsg());
            reportTime("prepayerr", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
        }
    }

    @Override
    public void onProductDetailsResponse(WrapperBillingResult result, List<ProductDetails> productDetailsList) {
        CTILog.d(TAG, "onProductDetailsResponse:" + ((result == null) ? "null" : result.toString()));
        MTimer.stop(MTimer.GW_PROCESS_PREPAY);
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onProductDetailsResponse&result=" + result.resultCode() +
                        "&msg=" + ((result == null) ? "null" : result.toString()) + "&productDetailsList="
                        + ((productDetailsList == null) ? "null" : productDetailsList.toString()));
        if (result.isSuccess()) {
            onQueryProductDetailsSuccess(result, productDetailsList);

            reportResult("prepaysucc", "");
            reportTime("prepaysucc", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
        } else {
            onQueryProductDetailsFailed(result);

            reportResult("prepayerr", result.resultMsg());
            reportTime("prepayerr", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
        }
    }

    @Override
    public void onPurchaseResponse(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTILog.d(TAG, "onPurchaseResponse:" + (result == null ? "null" : result.toString()));
        CTILog.d(TAG, "purchasesList:" + ((purchasesList == null) ? "null" : purchasesList.toString()));
        MTimer.stop(MTimer.GW_PROCESS_PAY);

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseResponse&result=" + result.resultCode() + "&msg=" + result.resultMsg() + "&purchasesList=" + ((purchasesList == null) ? "null" : purchasesList.toString()));


        if (result.isSuccess()) {
            if (billingHelper.isGw5Supported()) {
                onPurchaseSuccess(result, purchasesList);
            } else {
                onOldPurchaseSuccess(result, purchasesList);
            }
            reportResult("paysucc", result.resultMsg());
            reportTime("paysucc", MTimer.duration(MTimer.GW_PROCESS_PAY));
        } else {
            onPurchaseFailed(result);

            reportResult("payerr", result.resultMsg());
            reportTime("payerr", MTimer.duration(MTimer.GW_PROCESS_PAY));
        }

        reportTime(MTimer.GW_FIRST_SCREEN_SHOWDIALOG, MTimer.duration(MTimer.GW_FIRST_SCREEN_SHOWDIALOG));
        reportTime(MTimer.GW_PROCESS_SHOW_DIALOG, MTimer.duration(MTimer.GW_PROCESS_SHOW_DIALOG));
    }

    @Override
    public void onConsumeResponse(WrapperBillingResult result, String purchaseToken) {
        CTILog.d(TAG, "onConsumeResponse:" + ((result == null) ? "null" : result.toString()));
        MTimer.stop(MTimer.GW_PROCESS_POSTPAY);

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseResponse&result=" + result.resultCode() + "&msg=" + result.resultMsg() + "&purchaseToken=" + ((purchaseToken == null) ? "null" : purchaseToken.toString()));


        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_CONSUME_RESULT,
                "version=2.0&result=" + (result == null ? "null" : result.resultCode()) + "&msg=" + (result == null ? "null" : result.resultMsg()) + "&billno=" + (mModel == null ? "" : mModel.getBillNo()) + "&productid=" + (mModel == null ? "" : mModel.getRequest().getProductID()));

        if (result.isSuccess()) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC, 0, purchaseToken);

            reportResult("consumesucc", result.resultMsg());
            reportTime("consumesucc", MTimer.duration(MTimer.GW_PROCESS_POSTPAY));
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_POSTPAY_ERROR, 0, result.resultMsg());

            reportResult("consumeerr", result.resultMsg());
            reportTime("consumeerr", MTimer.duration(MTimer.GW_PROCESS_POSTPAY));
        }
    }

    /****************************** api result ***************************************/

    private void onQueryPurchasesSuccess(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryPurchasesSuccess&result=" + result.resultCode() + "&purchaselist=" + purchasesList.toString());

        //test demo
//        String testChannelExtras = "OldSku=com.mscti.sub.resurrection&SubcribeProrationMode=4";
//        String testChannelExtras = "OldSku=subsweek_autopay_default_pid&SubcribeProrationMode=4";
//        subcribeUpgrate(testChannelExtras);
        if (null != mModel) {
            BillingFlowParams request = mModel.getRequest();
            BillingFlowParams.BillingFlowParamsExtra billExtra = request.getExtra();
            subcribeUpgrate(billExtra.getChannelExtras());
            CTILog.d(TAG, "onQueryPurchasesSuccess ChannelExtras: " + billExtra.getChannelExtras());
        }

        if (purchasesList != null && !purchasesList.isEmpty()
                && BillingClient.ProductType.SUBS.equals(googleType)) {

            for (Purchase purchase : purchasesList) {
                //subscription still valid,forbidden purchase

                if (purchase.getProducts().contains(productId)
                        && purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                    MTimer.stop(MTimer.GW_PROCESS_PREPAY);

                    String mes = "";
                    int subErrCode = MRetCode.ERR_GW_SUB_OWNED_SAME_USER;
//                    String dpl = purchase.getAccountIdentifiers();//getDeveloperPayload();

                    AccountIdentifiers accoutObj = purchase.getAccountIdentifiers();
                    String openid = accoutObj.getObfuscatedAccountId();

                    if (!TextUtils.isEmpty(openid)) {
                        if (!GlobalData.singleton().openID.equals(openid)) {
                            subErrCode = MRetCode.ERR_GW_SUB_OWNED_DIFF_USER;
                            mes = CTICommMethod.getStringId(mView.getActivity(), "unipay_error_sub_owned_diffuser");
                        }
                    }

                    sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, mes);

                    reportResult("prepayerr", "SubValidErr_" + subErrCode);
                    reportTime("prepayerr", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
                    return;
                }

                //Modify, if it is an upgrade, it will be interrupted if no purchase is found, because there is no original order without purchase
                //Without the original order information, I don’t know if it’s upgraded with the same openid, and it may cause delivery errors.
                if (!TextUtils.isEmpty(OldSku) && purchase.getProducts().contains(OldSku)
                        && purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {

                    if (purchase != null) {
                        mOldPurchase = purchase;

                        MTimer.stop(MTimer.GW_PROCESS_PREPAY);

                        AccountIdentifiers accoutObj = purchase.getAccountIdentifiers();
                        String openid = accoutObj.getObfuscatedAccountId();

                        if (!TextUtils.isEmpty(openid)) {
                            if (!GlobalData.singleton().openID.equals(openid)) {
                                int subErrCode = MRetCode.ERR_GW_SUB_OWNED_DIFF_USER;
                                String mes = CTICommMethod.getStringId(mView.getActivity(), "unipay_error_sub_owned_diffuser");

                                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, mes);

                                reportResult("prepayerr", "SubValidErr_" + subErrCode);
                                reportTime("prepayerr", MTimer.duration(MTimer.GW_PROCESS_PREPAY));

                                return;
                            }
                        }
                    }
                }
            }
        }
        //continue query product details
        queryProductDetails();
        //continue query sku details
//        querySkuDetails();
    }

    private void onQueryOldPurchasesSuccess(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryPurchasesSuccess&result=" + result.resultCode() + "&purchaselist=" + purchasesList.toString());

        if (null != mModel) {
            BillingFlowParams request = mModel.getRequest();
            BillingFlowParams.BillingFlowParamsExtra billExtra = request.getExtra();
            subcribeUpgrate(billExtra.getChannelExtras());
            CTILog.d(TAG, "onQueryPurchasesSuccess ChannelExtras: " + billExtra.getChannelExtras());
        }

        if (purchasesList != null && !purchasesList.isEmpty()
                && BillingClient.SkuType.SUBS.equals(googleType)) {

            for (Purchase purchase : purchasesList) {
                //subscription still valid,forbidden purchase

                if (purchase.getSkus().contains(productId)
                        && purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                    MTimer.stop(MTimer.GW_PROCESS_PREPAY);

                    String mes = "";
                    int subErrCode = MRetCode.ERR_GW_SUB_OWNED_SAME_USER;
//                    String dpl = purchase.getAccountIdentifiers();//getDeveloperPayload();

                    AccountIdentifiers accoutObj = purchase.getAccountIdentifiers();
                    String openid = accoutObj.getObfuscatedAccountId();

                    if (!TextUtils.isEmpty(openid)) {
                        if (!GlobalData.singleton().openID.equals(openid)) {
                            subErrCode = MRetCode.ERR_GW_SUB_OWNED_DIFF_USER;
                            mes = CTICommMethod.getStringId(mView.getActivity(), "unipay_error_sub_owned_diffuser");
                        }
                    }

                    sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, mes);

                    reportResult("prepayerr", "SubValidErr_" + subErrCode);
                    reportTime("prepayerr", MTimer.duration(MTimer.GW_PROCESS_PREPAY));
                    return;
                }

                //Modify, if it is an upgrade, it will be interrupted if no purchase is found, because there is no original order without purchase
                //Without the original order information, I don’t know if it’s upgraded with the same openid, and it may cause delivery errors.

                if (!TextUtils.isEmpty(OldSku) && purchase.getSkus().contains(OldSku)
                        && purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {

                    if (purchase != null) {
                        mOldPurchase = purchase;

                        MTimer.stop(MTimer.GW_PROCESS_PREPAY);

                        AccountIdentifiers accoutObj = purchase.getAccountIdentifiers();
                        String openid = accoutObj.getObfuscatedAccountId();

                        if (!TextUtils.isEmpty(openid)) {
                            if (!GlobalData.singleton().openID.equals(openid)) {
                                int subErrCode = MRetCode.ERR_GW_SUB_OWNED_DIFF_USER;
                                String mes = CTICommMethod.getStringId(mView.getActivity(), "unipay_error_sub_owned_diffuser");

                                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, mes);

                                reportResult("prepayerr", "SubValidErr_" + subErrCode);
                                reportTime("prepayerr", MTimer.duration(MTimer.GW_PROCESS_PREPAY));

                                return;
                            }
                        }
                    }
                }
            }
        }
        //continue query product details
//        queryProductDetails();
        //continue query sku details
        querySkuDetails();
    }

    private void onQueryPurchasesFailed(WrapperBillingResult result) {
        //query purchases,still query sku details
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryPurchasesFailed&result=" + result.resultCode());
        querySkuDetails();
    }

    private void onQuerySkuDetailsSuccess(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQuerySkuDetailsSuccess&result=" + result.resultCode() + "&skuDetailsList=" + skuDetailsList);
        if (skuDetailsList != null && !skuDetailsList.isEmpty()) {
            for (SkuDetails skuDetails : skuDetailsList) {
                if (productId.equals(skuDetails.getSku())) {
                    CTILog.i(TAG, skuDetails.toString());

                    mSkuDetails = skuDetails;
                    //Generate order information
                    CTIPayInfo info = new CTIPayInfo();
                    info.currency = skuDetails.getPriceCurrencyCode();
                    info.amount = CTITools.urlEncode(skuDetails.getPrice(), 1);
                    info.ext = CTITools.urlEncode(String.valueOf(skuDetails.getPriceAmountMicros()), 1);
                    info.gw_version = GlobalData.singleton().getGw_version();
                    sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC, 0, info);
                }
            }
        } else {
            //query exception,still order
            onQuerySkuDetailsFailed(result);
        }
    }

    private void onQuerySkuDetailsFailed(WrapperBillingResult result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQuerySkuDetailsFailed&result=" + result.resultCode());
        //Do not intercept, continue to pay, set the default value
        CTIPayInfo defInfo = new CTIPayInfo();
        defInfo.currency = "".equals(gpCountryCode) ? "default" : gpCountryCode;
        defInfo.amount = "0.0";
        defInfo.ext = "0.0";
//        defInfo.gw_version = GlobalData.singleton().getGw_version();
        sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC, 0, defInfo);
    }

    //GW5.1
    private void onQueryProductDetailsSuccess(WrapperBillingResult result, List<ProductDetails> productDetailsList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryProductDetailsSuccess&result=" + result.resultCode() + "&productDetailsList=" + productDetailsList);
        if (productDetailsList != null && !productDetailsList.isEmpty()) {
            for (ProductDetails productDetails : productDetailsList) {
                if (productId.equals(productDetails.getProductId())) {
                    CTILog.i(TAG, productDetails.toString());
                    mProductDetails = productDetails;
                    //Generate order information
                    CTIPayInfo info = new CTIPayInfo();
                    if (googleType == "inapp") {
                        if (productDetails != null && productDetails.getOneTimePurchaseOfferDetails() != null) {
                            ProductDetails.OneTimePurchaseOfferDetails offerDetails = productDetails.getOneTimePurchaseOfferDetails();
                            info.currency = offerDetails.getPriceCurrencyCode();
                            info.amount = CTITools.urlEncode(offerDetails.getFormattedPrice(), 1);
                            info.ext = CTITools.urlEncode(String.valueOf(offerDetails.getPriceAmountMicros()), 1);
                        } else {
                            info.currency = "default";
                            info.amount = "0.0";
                            info.ext = "0.0";
                        }
                        info.basePlanId = "default";
                        info.gw_version = "";
                    } else {
                        getSelectedOfferIndex(productDetails);

                        List<ProductDetails.SubscriptionOfferDetails> offerDetailsList = productDetails.getSubscriptionOfferDetails();
                        info.currency = "default";
                        info.amount = "0.0";
                        info.ext = "0.0";
                        if (selectedOfferIndex >= 0 && selectedOfferIndex < offerDetailsList.size()) {
                            ProductDetails.SubscriptionOfferDetails offerDetails = offerDetailsList.get(selectedOfferIndex);
                            List<ProductDetails.PricingPhase> pricingPhaseList = offerDetails.getPricingPhases().getPricingPhaseList();
                            if (!pricingPhaseList.isEmpty()) {
                                ProductDetails.PricingPhase pricingPhase = pricingPhaseList.get(0);
                                info.currency = pricingPhase.getPriceCurrencyCode();
                                info.amount = CTITools.urlEncode(pricingPhase.getFormattedPrice(), 1);
                                info.ext = CTITools.urlEncode(String.valueOf(pricingPhase.getPriceAmountMicros()), 1);
                            }
                        }
                        info.basePlanId = basePlanId;
                        info.gw_version = GlobalData.singleton().getGw_version();
                        Class<?> clazz = ProductDetails.class;
                        try {
                            Field field = clazz.getDeclaredField("zzb");
                            field.setAccessible(true);
                            JSONObject productObject = new JSONObject(String.valueOf(field.get(productDetails)));
                            JSONArray subscriptionArray = productObject.getJSONArray("subscriptionOfferDetails");
                            JSONObject pricingPhases = new JSONObject(String.valueOf(subscriptionArray.get(selectedOfferIndex)));
                            info.gw_pricingPhases = String.valueOf(pricingPhases.get("pricingPhases"));
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            e.printStackTrace();
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                    sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC, 0, info);
                }
            }
        } else {
            //query exception,still order
            onQuerySkuDetailsFailed(result);
        }
    }

    private void onQueryProductDetailsFailed(WrapperBillingResult result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onQueryProductDetailsFailed&result=" + result.resultCode());
        //Do not intercept, continue to pay, set the default value
        CTIPayInfo defInfo = new CTIPayInfo();
        defInfo.currency = "default";
        defInfo.amount = "0.0";
        defInfo.ext = "0.0";
        defInfo.gw_version = GlobalData.singleton().getGw_version();
        sendMesUIH(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC, 0, defInfo);
    }

    private void onPurchaseSuccess(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseSuccess&result=" + result.resultCode() + "&purchasesList=" + purchasesList);
        if (purchasesList != null && !purchasesList.isEmpty()) {
            for (Purchase purchase : purchasesList) {
                if (!TextUtils.isEmpty(OldSku) ? purchase.getProducts().contains(OldSku) : purchase.getProducts().contains(productId)) {
                    //purchased
                    if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                        // 支付成功后重新查询物品，并将第二次物品存到 SecondSkuDetails 中
                        // 拉物品的等待时间由下单返回，范围在 0-500ms 内，如果时间为 0 就不进行第二次拉物品
                        // 在 XML 文件中设置开关，控制是否进行支付后的查物品，开关没开就置等待时间为 0
                        if (mView == null) {
                            CTILog.e(TAG, "Fail to get mView Activity");
                            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 100, "fail to get mView Activity");
                            return;
                        }
                        if (!CTICommMethod.isConfigQuerySecondSkuDetails(mView.getActivity())) {
                            waitSecondSkuDetailsTime = 0;
                        }
                        if (waitSecondSkuDetailsTime != 0) {
                            getBillingConfigAsync();
                            if (billingHelper.isGw5Supported()) {
                                querySecondProductDetails();
                            } else {
                                querySecondSkuDetails();
                            }
                        }

                        new Timer().schedule(new TimerTask() {
                            @Override
                            public void run() {
                                mPurchase = purchase;
                                CTIPayReceipt rec = new CTIPayReceipt();
                                String sigString = mPurchase.getSignature();
                                String data = mPurchase.getOriginalJson();
                                String base64dataString = CTIBase64.encode(data.getBytes());
                                rec.receipt = base64dataString;
                                rec.receipt_sig = sigString;
                                rec.sku = mPurchase.getProducts();
                                if (googleType == "inapp") {
                                    rec.gw_version = "";
                                    rec.first_currency_type = mProductDetails.getOneTimePurchaseOfferDetails().getPriceCurrencyCode();
                                } else {
                                    rec.gw_version = "5";
                                    rec.basePlanId = basePlanId;
                                    rec.first_currency_type = mProductDetails.getSubscriptionOfferDetails().get(0).getPricingPhases().getPricingPhaseList().get(0).getPriceCurrencyCode();
                                }

                                if (mSecondProductDetails != null) {
                                    if (googleType == "inapp") {
                                        rec.second_currency_type = "".equals(gpCountryCode) ? mSecondProductDetails.getOneTimePurchaseOfferDetails().getPriceCurrencyCode() : gpCountryCode;
                                        rec.second_amt = mSecondProductDetails.getOneTimePurchaseOfferDetails().getFormattedPrice();
                                    } else {
                                        rec.second_currency_type = "".equals(gpCountryCode) ? mSecondProductDetails.getSubscriptionOfferDetails().get(selectedOfferIndex).getPricingPhases().getPricingPhaseList().get(0).getPriceCurrencyCode() : gpCountryCode;
                                        rec.second_amt = Long.toString(mSecondProductDetails.getSubscriptionOfferDetails().get(selectedOfferIndex).getPricingPhases().getPricingPhaseList().get(0).getPriceAmountMicros());
                                        rec.gw_pricingPhases = "";
                                    }
                                }
                                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL,
                                        "secondCurrencyType=" + rec.second_currency_type + "&secondAmt=" + rec.second_amt
                                                + "&waitSecondProductDetailsTime=" + waitSecondSkuDetailsTime);
                                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 0, rec);
                                CTILog.i(TAG, "purchase info: " + purchase.toString());
                            }
                        }, waitSecondSkuDetailsTime);


                    } else if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                        CTILog.e(TAG, "Purchased State Error: " + purchase.getPurchaseState());
                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_TO_BE_PAID, MRetCode.ERR_GW_BILLING_PAY_PENDING, "Items to be paid!");
                    } else {
                        CTILog.e(TAG, "Purchased State Error: " + purchase.getPurchaseState());
                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, result.resultCode(), result.resultMsg());
                    }
                }
            }
        } else {
            //The fourth mode of subscription upgrade will come here
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 100, "purchasesList is null");
        }
    }

    private void onOldPurchaseSuccess(WrapperBillingResult result, List<Purchase> purchasesList) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onOldPurchaseSuccess&result=" + result.resultCode() + "&purchasesList=" + purchasesList);
        if (purchasesList != null && !purchasesList.isEmpty()) {
            for (Purchase purchase : purchasesList) {
                if (purchase.getSkus().contains(productId)) {
                    //purchased
                    if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                        // 支付成功后重新查询物品，并将第二次物品存到 SecondSkuDetails 中
                        // 拉物品的等待时间由下单返回，范围在 0-500ms 内，如果时间为 0 就不进行第二次拉物品
                        // 在 XML 文件中设置开关，控制是否进行支付后的查物品，开关没开就置等待时间为 0
                        if (mView == null) {
                            CTILog.e(TAG, "Fail to get mView Activity");
                            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 100, "fail to get mView Activity");
                            return;
                        }
                        if (!CTICommMethod.isConfigQuerySecondSkuDetails(mView.getActivity())) {
                            waitSecondSkuDetailsTime = 0;
                        }
                        if (waitSecondSkuDetailsTime != 0) {
                            querySecondSkuDetails();
                        }

                        new Timer().schedule(new TimerTask() {
                            @Override
                            public void run() {
                                mPurchase = purchase;
                                CTIPayReceipt rec = new CTIPayReceipt();
                                String sigString = mPurchase.getSignature();
                                String data = mPurchase.getOriginalJson();
                                String base64dataString = CTIBase64.encode(data.getBytes());
                                rec.receipt = base64dataString;
                                rec.receipt_sig = sigString;
                                rec.sku = mPurchase.getSkus();
                                rec.first_currency_type = mSkuDetails.getPriceCurrencyCode();
                                if (mSecondSkuDetails != null) {
                                    rec.second_currency_type = mSecondSkuDetails.getPriceCurrencyCode();
                                    rec.second_amt = mSecondSkuDetails.getPrice();
                                }
                                rec.gw_version = "4";
                                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL,
                                        "secondCurrencyType=" + rec.second_currency_type + "&secondAmt=" + rec.second_amt
                                                + "&waitSecondSkuDetailsTime=" + waitSecondSkuDetailsTime);
                                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 0, rec);
                                CTILog.i(TAG, "purchase info: " + purchase.toString());
                            }
                        }, waitSecondSkuDetailsTime);


                    } else if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                        CTILog.e(TAG, "Purchased State Error: " + purchase.getPurchaseState());
                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_TO_BE_PAID, MRetCode.ERR_GW_BILLING_PAY_PENDING, "Items to be paid!");
                    } else {
                        CTILog.e(TAG, "Purchased State Error: " + purchase.getPurchaseState());
                        sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, result.resultCode(), result.resultMsg());
                    }
                }
            }
        } else {
            //The fourth mode of subscription upgrade will come here
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_SUCC, 100, "purchasesList is null");
        }
    }

    private void onPurchaseFailed(WrapperBillingResult result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                "name=onPurchaseFailed&result=" + result.resultCode());
        int failCode = result.resultCode();

        //show sandbox error tips
        if (CTITools.isTestEnv()) {
            result.showSandboxErrTips();
        }

        if (failCode == BillingClient.BillingResponseCode.USER_CANCELED) {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_CANCEL, MRetCode.ERR_GW_BILLING_USER_CANCEL, result.resultMsg());
        } else {
            sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, result.unifyErrCode(), result.resultMsg());
        }
    }

    /****************************** private ****************************************/

    private void sendMesUIH(int what, int arg1, Object obj) {
        if (UIHandler != null) {
            Message msg = new Message();
            msg.what = what;
            msg.arg1 = arg1;
            msg.obj = obj;
            Bundle bundle = new Bundle();
            // resultCode -> InnerCode
            bundle.putString("msgErrCode", String.valueOf(arg1));
            msg.setData(bundle);
            UIHandler.sendMessage(msg);
        }
    }

    private void reportTime(String name, long times) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_TIME_20,
                "version=2.0&name=" + name
                        + "&times=" + times
                        + "&productType=" + googleType
                        + "&centauriType=" + centauriType);
    }

    private void reportResult(String name, String result) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_GW_RESULT_20,
                "version=2.0&name=" + name
                        + "&result=" + result
                        + "&productType=" + googleType
                        + "&centauriType=" + centauriType);
    }

    @Override
    protected boolean needShowSucc() {
        return false;
    }

    private void subcribeUpgrate(String channelExtras) {
        try {
            if (!TextUtils.isEmpty(channelExtras)) {
                HashMap<String, String> desParams = CTITools.kv2Map(channelExtras);
                if (!TextUtils.isEmpty(desParams.get("SubcribeProrationMode")) &&
                        !TextUtils.isEmpty(desParams.get("OldSku"))) {
                    OldSku = desParams.get("OldSku");
                    SubcribeProrationMode = Integer.parseInt(desParams.get("SubcribeProrationMode"));
                }
            } else {
                CTILog.i(TAG, "APPay channelExtras is null ");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            CTILog.i(TAG, "subcribeUpgrate errr:" + ex.toString());
        }
    }

    private void querySecondSkuDetails() {
        // 此方法是为了防止支付前后币种不一致导致造成损失，在支付后再拉一次物品，并将信息在发货流程中传给后台
        // 支付成功后先拉一次物品信息，花费指定时间等待拉物品的结果，然后将支付前后的币种传给发货 CGI 处理，如果没有拉到支付后币种也不影响正常发货流程
        CTILog.d(TAG, "query product after pay success");
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=secondQuerySkuDetailsAsync");

        ArrayList<String> skuList = new ArrayList<String>();
        skuList.add(productId);
        SkuDetailsParams skuDetailsParams = SkuDetailsParams.newBuilder()
                .setType(googleType)
                .setSkusList(skuList)
                .build();

        if (null != billingHelper) {
            billingHelper.querySkuDetailsAsync(skuDetailsParams, new BillingHelper.OnIabQuerySkuDetailsListener() {
                @Override
                public void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
                    CTILog.d(TAG, "onQuerySecondSkuDetailsResponse:" + ((result == null) ? "null" : result.toString()));
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                            "name=onQuerySecondSkuDetailsResponse&result=" + result.resultCode() +
                                    "&msg=" + ((result == null) ? "null" : result.toString()) + "&skuDetailsList="
                                    + ((skuDetailsList == null) ? "null" : skuDetailsList.toString()));

                    if (skuDetailsList != null && !skuDetailsList.isEmpty()) {
                        for (SkuDetails skuDetails : skuDetailsList) {
                            if (productId.equals(skuDetails.getSku())) {
                                mSecondSkuDetails = skuDetails;
                                CTILog.d(TAG, "mSecondSkuDetails is " + mSecondSkuDetails);
                            }
                        }
                    }
                }
            });
        }
    }

    private void querySecondProductDetails() {
        // 此方法是为了防止支付前后币种不一致导致造成损失，在支付后再拉一次物品，并将信息在发货流程中传给后台
        // 支付成功后先拉一次物品信息，花费指定时间等待拉物品的结果，然后将支付前后的币种传给发货 CGI 处理，如果没有拉到支付后币种也不影响正常发货流程
        CTILog.d(TAG, "query product after pay success");
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_CALL, "name=secondQuerySkuDetailsAsync");

        List<QueryProductDetailsParams.Product> productList = new ArrayList<>();
        productList.add(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(productId)
                .setProductType(googleType)
                .build());
//        ImmutableList<QueryProductDetailsParams.Product> imProductList = ImmutableList.copyOf(productList);
        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();

        if (null != billingHelper) {
            billingHelper.queryProductDetailsAsync(params, new BillingHelper.OnIabQueryProductDetailsListener() {
                @Override
                public void onProductDetailsResponse(WrapperBillingResult result, List<ProductDetails> productDetailsList) {
                    CTILog.d(TAG, "onQuerySecondProductDetailsResponse:" + ((result == null) ? "null" : result.toString()));
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CALL_GW20_SDK_RESPONSE,
                            "name=onQuerySecondProductDetailsResponse&result=" + result.resultCode() +
                                    "&msg=" + ((result == null) ? "null" : result.toString()) + "&productDetailsList="
                                    + ((productDetailsList == null) ? "null" : productDetailsList.toString()));

                    if (productDetailsList != null && !productDetailsList.isEmpty()) {
                        for (ProductDetails productDetails : productDetailsList) {
                            if (productId.equals(productDetails.getProductId())) {
                                getSelectedOfferIndex(productDetails);
                                mSecondProductDetails = productDetails;
                                CTILog.d(TAG, "mSecondProductDetails is " + mSecondProductDetails);
                            }
                        }
                    }
                }
            });
        }
    }

    // 从 obj(info)中取出 wait_time 作为 waitSecondSkuDetailsTime 的值，取值范围为 [0, 500]
    private int getWaitSecondSkuDetailsTime(JSONObject obj) {
        if (obj != null && !TextUtils.isEmpty(obj.optString("wait_time"))) {
            try {
                int waitTime = Integer.parseInt(obj.optString("wait_time"));
                if (waitTime >= 0 && waitTime <= 500) {
                    return waitTime;
                }
            } catch (Exception e) {
                CTILog.e(TAG, "getWaitSecondSkuDetailsTime(): " + e.getMessage());
            }
        }
        return 0;
    }

    public void getBillingConfigAsync() {
        if (null != billingHelper) {
            billingHelper.getBillingConfigAsync(this);
        }
    }

    private void getSelectedOfferIndex(ProductDetails productDetails) {
        Boolean isFound = false;
        if (googleType != "inapp") {
            for (int i = 0; i < productDetails.getSubscriptionOfferDetails().size(); i++) {
                if (productDetails.getSubscriptionOfferDetails().get(i).getBasePlanId().equals(basePlanId)) {
                    try {
                        if (gw_offerId.isEmpty() && productDetails.getSubscriptionOfferDetails().get(i).getOfferId() == null) {
                            selectedOfferIndex = i;
                            isFound = true;
                        } else if (gw_offerId.equals(productDetails.getSubscriptionOfferDetails().get(i).getOfferId())) {
                            selectedOfferIndex = i;
                            isFound = true;
                        }
                    } catch (Exception e) {
                        CTILog.e(TAG, "getSelectedOfferIndex: " + e.getMessage());
                        selectedOfferIndex = i;
                        isFound = true;
                    }
                }
            }
            if (!isFound) {
                int subErrCode = MRetCode.ERR_GW_BILLING_PAY_NOTFOUNDBASEPLANID;
                String mes = "Item not found";
                sendMesUIH(MConstants.MSG_PAYCHANNEL_PAY_ERROR, subErrCode, mes);
                return;
            }
        }
    }

    @Override
    public void onBillingConfigResponse(@NonNull BillingResult billingResult, @Nullable BillingConfig billingConfig) {
        CTILog.e(TAG,"getBillingConfigAsync billingResult : " +billingResult.getResponseCode());
        if (null != billingResult && billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
            gpCountryCode = billingConfig.getCountryCode();
            CTILog.e(TAG,"getBillingConfigAsync gpCountryCode : " +gpCountryCode);
        }
    }
}

