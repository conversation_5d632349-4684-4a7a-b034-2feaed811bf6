package com.centauri.oversea.business.payhub.gwallet.New;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.SkuDetails;
import com.android.billingclient.api.SkuDetailsParams;
import com.centauri.comm.CTILog;
import com.centauri.oversea.business.CTIBaseIntroInfo;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.InfoCallback;
//import com.google.firebase.crashlytics.buildtools.reloc.com.google.common.collect.ImmutableList;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CTIIntroInfo extends CTIBaseIntroInfo
    implements BillingHelper.OnIabSetupFinishedListener {

    public static final String TAG = "APIntroInfo_New";
    private BillingHelper billingHelper;
    private InfoCallback mCallback;
    private Context mContext;

    private boolean isSDKQueryFinished = false;    //sdk asynchronous query completed
    private boolean isServerQueryFinished = false;//server asynchronous query completed
    private String intro_sdkJInfo = ""; //Discount information: sdk asynchronous query result
    private String intro_serverJInfo = ""; //Discount information: server asynchronous query result

    @Override
    public void getIntroInfo(Context _context, String _channel, HashMap<String, String> pMap, InfoCallback _callback) {
        mContext = _context.getApplicationContext();
        channel = _channel;
        mCallback = _callback;
        productIdMap = pMap;
        if (productIdMap == null || productIdMap.isEmpty()) {
            callbackSDK(RET_ERR,null);
        }
        init(mContext);
        if (billingHelper.isGw5Supported()) {
            getPurchaseList();
        } else {
            getOldPurchaseList();
        }
    }

    private void init(Context context) {
        billingHelper = BillingHelper.getInstance(context);
//        billingHelper.startSetup(this);

        //Need to check discount information, check in the background
        queryServerInfo(new ICTICallback() {
            @Override
            public void callback(int retCode, String jsIntroInfo) {
                callbackRspDiscountServer(retCode, jsIntroInfo);
            }
        });
    }

    @Override
    public void onIabSetupFinished(WrapperBillingResult result) {
        Log.d(TAG, "donIabSetupFinished: "+result);
        if (result.isSuccess()) {
            if (billingHelper.isGw5Supported()) {
                getPurchaseList();
            } else {
                getOldPurchaseList();
            }
        } else {
            callbackSDK(RET_ERR, null);
        }
    }

    private void getPurchaseList() {

        if(productIdMap == null || productIdMap.isEmpty()){

            callbackSDK(RET_ERR, null);
            return;
        }

        Collection<String> valueCollection = productIdMap.keySet();
        List<QueryProductDetailsParams.Product> subBridageList = new ArrayList<>();
        Iterator subIterator = valueCollection.iterator();
        while (subIterator.hasNext()) {
            subBridageList.add(QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(subIterator.next().toString())
                    .setProductType(BillingClient.ProductType.SUBS)
                    .build());
        }
//        ImmutableList<QueryProductDetailsParams.Product> subProductList = ImmutableList.copyOf(subBridageList);
        if (subBridageList != null && !subBridageList.isEmpty()) {
            QueryProductDetailsParams subParams = QueryProductDetailsParams.newBuilder()
                    .setProductList(subBridageList)
                    .build();
            billingHelper.queryProductDetailsAsync(subParams, new BillingHelper.OnIabQueryProductDetailsListener() {
                @Override
                public void onProductDetailsResponse(WrapperBillingResult result, List<ProductDetails> productDetailsList) {
                    Log.d(TAG, "querySkuDetailsAsync:" + result);
                    int retCode = RET_ERR;
                    JSONArray jInfo = null;

                    if(result.isSuccess()
                            && productDetailsList != null && !productDetailsList.isEmpty()){
                        JSONArray array = new JSONArray();
                        try {
                            for (ProductDetails productDetails : productDetailsList) {
                                JSONObject item = new JSONObject();
                                item.put("productId", productDetails.getProductId());
                                Class<?> clazz = ProductDetails.class;
                                try {
                                    Field field = clazz.getDeclaredField("zzb");
                                    field.setAccessible(true);
                                    JSONObject productObject = new JSONObject(String.valueOf(field.get(productDetails)));
                                    JSONArray subscriptionArray = productObject.getJSONArray("subscriptionOfferDetails");
                                    item.put("subscriptionOfferDetails", subscriptionArray);
                                } catch (NoSuchFieldException | IllegalAccessException e) {
                                    CTILog.e(TAG, "onProductDetailsResponse exception: " + e.getMessage());
                                    e.printStackTrace();
                                } catch (JSONException e) {
                                    CTILog.e(TAG, "onProductDetailsResponse exception: " + e.getMessage());
                                    e.printStackTrace();
                                }
                                array.put(item);
                            }
                            jInfo = array;
                            retCode = RET_OK;
                        }catch (JSONException e){
                            CTILog.e(TAG,"Query intro price exception.");
                        }
                    }
                    dispose();
                    callbackSDK(retCode,jInfo);
                }
            });
        }
    }

    private void getOldPurchaseList() {

        if(productIdMap == null || productIdMap.isEmpty()){

            callbackSDK(RET_ERR, null);
            return;
        }
        Collection<String> valueCollection = productIdMap.keySet();
        List<String> plist = new ArrayList(valueCollection);

        SkuDetailsParams skuDetailsParams = SkuDetailsParams.newBuilder()
                .setSkusList(plist)
                .setType(BillingClient.SkuType.SUBS)
                .build();

        billingHelper.querySkuDetailsAsync(skuDetailsParams, new BillingHelper.OnIabQuerySkuDetailsListener() {
            @Override
            public void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
                Log.d(TAG, "querySkuDetailsAsync:" + result);
                int retCode = RET_ERR;
                JSONArray jInfo = null;

                if(result.isSuccess()
                        && skuDetailsList != null && !skuDetailsList.isEmpty()){
                    JSONArray array = new JSONArray();
                    try {
                        for (SkuDetails skuDetails : skuDetailsList) {
                            JSONObject item = new JSONObject();
                            item.put("productId", skuDetails.getSku());
                            item.put("price", skuDetails.getPrice());
                            item.put("microprice", skuDetails.getPriceAmountMicros());
                            item.put("currency", skuDetails.getPriceCurrencyCode());
                            item.put("introPrice", skuDetails.getIntroductoryPrice());
                            item.put("introMicroPrice", skuDetails.getIntroductoryPriceAmountMicros());
                            item.put("introPeriod", skuDetails.getIntroductoryPriceCycles());
                            item.put("introCycles", skuDetails.getIntroductoryPricePeriod());
                            //new
                            item.put("originalPrice", skuDetails.getOriginalPrice());
                            item.put("originalMicroprice", skuDetails.getOriginalPriceAmountMicros());
                            array.put(item);
                        }

                        jInfo = array;
                        retCode = RET_OK;
                    }catch (JSONException e){
                        CTILog.e(TAG,"Query intro price exception.");
                    }
                }

                dispose();
                callbackSDK(retCode,jInfo);
            }
        });
    }

    private void dispose() {
//        if(billingHelper != null){
//            billingHelper.dispose();
//        }
    }

    /*************************************回调*********************************************/
    //SDK query callback, distinguish different versions
    private void callbackSDK(int retCode,JSONArray jInfo){
        isSDKQueryFinished = true;
        if(jInfo != null) {
            intro_sdkJInfo = jInfo.toString();
        }

        //After the sdk and server have been queried, the external callback
        if(isServerQueryFinished){
            callbackIntro();
        }
    }


    //server query callback
    private void callbackRspDiscountServer(int retCode,String jInfo){
        isServerQueryFinished = true;
        intro_serverJInfo = jInfo;

        //After sdk and server are all inquired, it will call back to the outside
        if(isSDKQueryFinished){
            callbackIntro();
        }
    }


    //After the sdk and server asynchronous callbacks are completed, the result is processed
    private void callbackIntro(){
        String res = "";

        try {
            if (!TextUtils.isEmpty(intro_sdkJInfo)) {
                //1, server information is not empty, splicing SDK
                if (!TextUtils.isEmpty(intro_serverJInfo)) {
                    JSONObject jRes = new JSONObject(intro_serverJInfo);
                    jRes.put("ret", 0); //Set the result code to 0
                    jRes.put("gwallet_productInfo", new JSONArray(intro_sdkJInfo));
                    res = jRes.toString();
                }
                //2, server information is empty, return SDK
                else {
                    JSONObject jInfo = new JSONObject();
                    jInfo.put("ret", 0); //Set the result code to 0
                    jInfo.put("gwallet_productInfo", new JSONArray(intro_sdkJInfo));
                    res = jInfo.toString();
                }
            } else {
                //3, sdk information is empty, directly return server information
                JSONObject jRes = new JSONObject(intro_serverJInfo);
                //ret is empty or not 0, the result code is set to -1
                if(!jRes.has("ret") || jRes.getInt("ret") != 0){
                    jRes.put("ret",-1);
                }
                res = jRes.toString();
            }
        }catch (JSONException e){
            CTILog.e(TAG, "CallbackErr: "+ e.getMessage());
        }

        //4, res is empty
        if(TextUtils.isEmpty(res)){
            res = "{\"ret\":-1}";
        }

        CTILog.d(TAG,"Callback Response: "+res);
        if(mCallback != null){
            mCallback.callback(res);
        }
    }
}
