package com.centauri.oversea.business.payhub.gwallet.New;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.ConsumeParams;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.SkuDetails;
import com.android.billingclient.api.SkuDetailsParams;
import com.centauri.comm.CTILog;
import com.centauri.oversea.api.request.CTIGameRequest;
import com.centauri.oversea.business.IGetProduct;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.response.InfoCallback;
//import com.google.firebase.crashlytics.buildtools.reloc.com.google.common.collect.ImmutableList;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public class CTIProductInfo
        implements IGetProduct, BillingHelper.OnIabSetupFinishedListener, BillingHelper.OnIabQuerySkuDetailsListener, BillingHelper.OnIabQueryProductDetailsListener {

    private final static String TAG = "APProductInfo_New";
    private BillingHelper billingHelper;
    private InfoCallback mCallback;
    private HashMap<String, String> mSkus = null;
    private HashMap<String, String> mOfficialSkus = null;
    private Context mContext;

    private final static String SUCCESS = "0";
    private final static String ERROR = "-1";
    private final static String ERROR_DATA = "-2";
    private final static String ERROR_PARAM = "-3";
    private Handler mHandler = new Handler();


    //callback count
    private int callbackCount = 1;
    private final int CALLBACK_TIMES = 2;
    //callback result
    private JSONArray jsRes = new JSONArray();

    public void getProductInfo(Context context, HashMap<String, String> productIdMap, InfoCallback callback) {
        mCallback = callback;
        mSkus = productIdMap;
        mContext = context;
        CTILog.d(TAG, "init");
        billingHelper = BillingHelper.getInstance(context);
        if (mSkus != null && !mSkus.isEmpty()) {
            init(mContext, new BillingHelper.OnIabSetupFinishedListener() {
                @Override
                public void onIabSetupFinished(WrapperBillingResult result) {
                    CTILog.i(TAG, "onIabSetupFinished:" + result);
                    if (result.isSuccess()) {
                        if (billingHelper.isGw5Supported()) {
                            queryProductsInfo();
                        } else {
                            querySkuInfo();
                        }
                    } else {
                        callback(ERROR, result.resultMsg());
                    }
                }
            });
        } else {
            callback(ERROR_PARAM, "query productList is empty");
        }
    }

    public void getProductInfo(Context context, HashMap<String, String> productIdMap, String resultData, InfoCallback callback) {
        CTILog.d(TAG, "init");

        try {
            JSONObject jsonObject = new JSONObject(resultData);
            JSONObject resultObject = jsonObject.getJSONObject("result");
            JSONArray productList = resultObject.getJSONArray("product_list");
            HashMap<String, String> productInfoMap = new HashMap<>();
            HashMap<String, String> officialSkus = new HashMap<>();
            for (int i = 0; i < productList.length(); i++) {
                JSONObject productObject = productList.getJSONObject(i);
                String platformProductId = productObject.getString("platform_product_id");
                String sdkProductType = new JSONObject(productObject.getString("platform_product_info")).getString("sdk_product_type");
                String unifiedProductId = productObject.getString("unified_product_id");
                productInfoMap.put(platformProductId, sdkProductType);
                officialSkus.put(platformProductId, unifiedProductId);
            }
            // Convert product_list to JSONArray
            mSkus = productInfoMap;
            mOfficialSkus = officialSkus;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        mCallback = callback;
        mContext = context;
        billingHelper = BillingHelper.getInstance(context);
        if (mSkus != null && !mSkus.isEmpty()) {
            init(mContext, new BillingHelper.OnIabSetupFinishedListener() {
                @Override
                public void onIabSetupFinished(WrapperBillingResult result) {
                    CTILog.i(TAG, "onIabSetupFinished:" + result);
                    if (result.isSuccess()) {
                        if (billingHelper.isGw5Supported()) {
                            queryProductsInfo();
                        } else {
                            querySkuInfo();
                        }
                    } else {
                        callback(ERROR, result.resultMsg());
                    }
                }
            });
        } else {
            callback(ERROR_PARAM, "query productList is empty");
        }
    }
    @Override
    public void getProductInfo(Activity activity, HashMap<String, String> productIdMap, InfoCallback callback) {

    }

    @Override
    public void getProductInfo(Activity activity, CTIGameRequest ctiGameRequest, InfoCallback infoCallback) {

    }

    @Override
    public void clearPurchase(Context context) {

        billingHelper = BillingHelper.getInstance(context);
        try {
            //query purchases
            billingHelper.queryPurchasesAsync(new BillingHelper.OnIabQueryPurchasesListener() {
                @Override
                public void onQueryPurchasesResponse(WrapperBillingResult result, List<Purchase> purchasesList) {
                    CTILog.i(TAG, "onQueryPurchasesResponse: " + result);
                    if (result.isSuccess()) {
                        if (purchasesList != null && !purchasesList.isEmpty()) {
                            for (Purchase purchase : purchasesList) {
                                CTILog.i(TAG, purchase.toString());
                                if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                                    ConsumeParams consumeParams = ConsumeParams.newBuilder()
                                            .setPurchaseToken(purchase.getPurchaseToken())
                                            .build();
                                    billingHelper.consumeAsync(consumeParams, new BillingHelper.OnIabConsumeListener() {
                                        @Override
                                        public void onConsumeResponse(WrapperBillingResult billingResult, String purchaseToken) {
                                            CTILog.d(TAG, "clearPurchase onConsumeResponse: " + billingResult + " | purchaseToken : " + purchaseToken);

                                        }
                                    });
                                }
                            }
                        }
                    }

                 }
            });
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void init(Context context, BillingHelper.OnIabSetupFinishedListener listener) {
        CTILog.d(TAG, "init");
        billingHelper = BillingHelper.getInstance(context);
        billingHelper.startSetup(listener);
    }

    //Get the productlist by category from the map
    private List<String> getListByTypeFromMap(HashMap<String, String> map, String type) {

        if (map == null || map.isEmpty()) {
            return null;
        }

        List<String> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {

            if (entry.getValue().equals(type)) {
                list.add(entry.getKey());
            }
        }
        return list;
    }

    private void querySkuInfo(){
        List<String> inappList = getListByTypeFromMap(mSkus, BillingClient.SkuType.INAPP);
        List<String> subsList = getListByTypeFromMap(mSkus, BillingClient.SkuType.SUBS);

        SkuDetailsParams inAppParams = SkuDetailsParams.newBuilder()
                .setType(BillingClient.SkuType.INAPP)
                .setSkusList(inappList)
                .build();

        SkuDetailsParams subsParams = SkuDetailsParams.newBuilder()
                .setType(BillingClient.SkuType.SUBS)
                .setSkusList(subsList)
                .build();

        if (null == billingHelper) {
            callback(ERROR, "billingHelper is null");
            return;
        }
        //query in-apps
        billingHelper.querySkuDetailsAsync(inAppParams, this);
        //query subs
        billingHelper.querySkuDetailsAsync(subsParams, this);
    }

    private void queryProductsInfo() {
        CTILog.d(TAG, "queryProductsInfo");

        List<String> inappList = getListByTypeFromMap(mSkus, BillingClient.ProductType.INAPP);
        List<String> subsList = getListByTypeFromMap(mSkus, BillingClient.ProductType.SUBS);

        //SKU Transform
        List<QueryProductDetailsParams.Product> inappBridageList = new ArrayList<>();
        List<QueryProductDetailsParams.Product> subBridageList = new ArrayList<>();

        Iterator inappIterator = inappList.iterator();
        Iterator subIterator = subsList.iterator();

        while (inappIterator.hasNext()) {
            inappBridageList.add(QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(inappIterator.next().toString())
                    .setProductType(BillingClient.ProductType.INAPP)
                    .build());
        }
        while (subIterator.hasNext()) {
            subBridageList.add(QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(subIterator.next().toString())
                    .setProductType(BillingClient.ProductType.SUBS)
                    .build());
        }

//        ImmutableList<QueryProductDetailsParams.Product> inappProductList = ImmutableList.copyOf(inappBridageList);
//        ImmutableList<QueryProductDetailsParams.Product> subProductList = ImmutableList.copyOf(subBridageList);


        if (null == billingHelper) {
            callback(ERROR, "billingHelper is null");
            return;
        }

        if (inappBridageList != null && !inappBridageList.isEmpty()){
            QueryProductDetailsParams inappParams = QueryProductDetailsParams.newBuilder()
                    .setProductList(inappBridageList)
                    .build();
            billingHelper.queryProductDetailsAsync(inappParams, this);
        } else {
            callbackCount ++;
        }

        if (subBridageList != null && !subBridageList.isEmpty()) {
            QueryProductDetailsParams subParams = QueryProductDetailsParams.newBuilder()
                    .setProductList(subBridageList)
                    .build();
            billingHelper.queryProductDetailsAsync(subParams, this);
        } else {
            callbackCount ++;
        }
    }

    @Override
    public void onIabSetupFinished(WrapperBillingResult result) {

    }

    @Override
    public void onSkuDetailsResponse(WrapperBillingResult result, List<SkuDetails> skuDetailsList) {
        CTILog.i(TAG, "onSkuDetailsResponse:" + result + ",callbackCount=" + callbackCount);

        if (result.isSuccess() &&
                skuDetailsList != null && !skuDetailsList.isEmpty()) {
            try {
                for (SkuDetails skuDetails : skuDetailsList) {
                    JSONObject item = new JSONObject();
                    item.put("unified_product_id", mOfficialSkus.get(skuDetails.getSku()));
                    item.put("platform_product_id", skuDetails.getSku());
                    item.put("product_name", skuDetails.getTitle());
                    item.put("currency_code", skuDetails.getPriceCurrencyCode());
                    item.put("region_code", "");
                    item.put("original_price", skuDetails.getOriginalPriceAmountMicros());
                    item.put("current_price", skuDetails.getPriceAmountMicros());
                    item.put("decimal_point", 4);
                    item.put("display_price", skuDetails.getPrice());

                    jsRes.put(item);
                }

                GlobalData.singleton().setCurrencyInGw(TextUtils.isEmpty(skuDetailsList.get(0).getPriceCurrencyCode()) ? "default" : skuDetailsList.get(0).getPriceCurrencyCode());


            } catch (JSONException e) {
                CTILog.e(TAG, "onSkuDetailsResponse exception: " + e.getMessage());
            }
        }

        if (callbackCount++ == CALLBACK_TIMES) {
            callback(SUCCESS, "success");
        }
    }

    @Override
    public void onProductDetailsResponse(WrapperBillingResult result, List<ProductDetails> productDetailsList) {
        CTILog.i(TAG, "onProductDetailsResponse:" + result + ",callbackCount=" + callbackCount);

        if (result.isSuccess() &&
                productDetailsList != null && !productDetailsList.isEmpty()) {
            try {
                if (BillingClient.ProductType.INAPP.equals(productDetailsList.get(0).getProductType())) {
                    for (ProductDetails productDetails : productDetailsList) {
                        JSONObject item = new JSONObject();
//                        item.put("productId", productDetails.getProductId());
//                        item.put("price", productDetails.getOneTimePurchaseOfferDetails().getFormattedPrice());
//                        item.put("currency", productDetails.getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
//                        item.put("microprice", productDetails.getOneTimePurchaseOfferDetails().getPriceAmountMicros());
//                        item.put("originalPrice", productDetails.getOneTimePurchaseOfferDetails().getFormattedPrice());
//                        item.put("originalMicroprice", productDetails.getOneTimePurchaseOfferDetails().getPriceAmountMicros());
                        item.put("unified_product_id", mOfficialSkus.get(productDetails.getProductId()));
                        item.put("platform_product_id", productDetails.getProductId());
                        item.put("product_name", productDetails.getName());
                        item.put("currency_code", productDetails.getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
                        item.put("region_code", "");
                        item.put("original_price", productDetails.getOneTimePurchaseOfferDetails().getPriceAmountMicros());
                        item.put("current_price", productDetails.getOneTimePurchaseOfferDetails().getPriceAmountMicros());
                        item.put("decimal_point", 4);
                        item.put("display_price", productDetails.getOneTimePurchaseOfferDetails().getFormattedPrice());

                        jsRes.put(item);
                    }
                    GlobalData.singleton().setCurrencyInGw(TextUtils.isEmpty(productDetailsList.get(0).getOneTimePurchaseOfferDetails().getPriceCurrencyCode()) ? "default" : productDetailsList.get(0).getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
                } else {
                    for (ProductDetails productDetails : productDetailsList) {
                        JSONObject item = new JSONObject();
                        item.put("productId", productDetails.getProductId());
                        Class<?> clazz = ProductDetails.class;
                        try {
                            Field field = clazz.getDeclaredField("zzb");
                            field.setAccessible(true);
                            JSONObject productObject = new JSONObject(String.valueOf(field.get(productDetails)));
                            JSONArray subscriptionArray = productObject.getJSONArray("subscriptionOfferDetails");
                            item.put("subscriptionOfferDetails", subscriptionArray);
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            CTILog.e(TAG, "onProductDetailsResponse exception: " + e.getMessage());
                            e.printStackTrace();
                        } catch (JSONException e) {
                            CTILog.e(TAG, "onProductDetailsResponse exception: " + e.getMessage());
                            e.printStackTrace();
                        }
                        jsRes.put(item);
                    }
                    GlobalData.singleton().setCurrencyInGw(TextUtils.isEmpty(productDetailsList.get(0).getSubscriptionOfferDetails().get(0).getPricingPhases().getPricingPhaseList().get(0).getPriceCurrencyCode()) ? "default" : productDetailsList.get(0).getSubscriptionOfferDetails().get(0).getPricingPhases().getPricingPhaseList().get(0).getPriceCurrencyCode());
                }
            } catch (Exception e) {
                CTILog.e(TAG, "onProductDetailsResponse exception: " + e.getMessage());
            }
        }

        if (callbackCount++ == CALLBACK_TIMES) {
            callback(SUCCESS, "success");
        }
    }

    private void callback(String retCode, String retMsg) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("retCode", retCode);
            obj.put("innerCode", "");
            obj.put("retMsg", retMsg);
            obj.put("respInfo", jsRes);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Log.d(TAG, "callback:" + obj.toString());
        if (mCallback != null) {
            mCallback.callback(obj.toString());
        }
    }

    /****************************** private ****************************************/

    private void sendMesUIH(int what, int arg1, Object obj) {
//        if (UIHandler != null) {
//            Message msg = new Message();
//            msg.what = what;
//            msg.arg1 = arg1;
//            msg.obj = obj;
//            Bundle bundle = new Bundle();
//            // resultCode -> InnerCode
//            bundle.putString("msgErrCode", String.valueOf(arg1));
//            msg.setData(bundle);
//            UIHandler.sendMessage(msg);
//        }
    }

}
