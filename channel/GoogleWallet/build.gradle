apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        versionCode rootProject.ext.centauriGoogleWalletVersionCode
        versionName rootProject.ext.centauriGoogleWalletVersionName
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
        }
    }

    /**
     * build aar and rename aar by dongbingliu 2020-10-30 星期五
     */
    setVersion(defaultConfig.versionName)
    libraryVariants.all { variant ->
        if (variant.buildType.name == 'release') {
            variant.outputs.all {
                outputFileName = "centauri-googlewallet-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.aar"
            }
        }else{
            variant.outputs.all {
                outputFileName = "centauri-googlewallet-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-debug.aar"
            }
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.javaCompileVersion
        targetCompatibility rootProject.ext.javaCompileVersion
    }
    lintOptions {
        abortOnError false
    }
//    packageBuildConfig = false
    buildFeatures {
        buildConfig = false
    }
}

task ctiChannelGwJar(type: Jar, dependsOn: "assembleRelease") {
    //jar package name
    archivesBaseName = "centauri-googlewallet-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}"
    //路径会跟随gradle版本变更而变化
    from "build/intermediates/javac/release/classes"
    include('com/')

    //依赖的maven仓库包打进一个jar包
//    from {configurations.myConfig.collect { it.isDirectory() ? it : zipTree(it) }}

    exclude('**/test/*.class', '**/sample/*.class')
    exclude "**/R.class"
    exclude "**/R\$*.class"
    exclude('**/BuildConfig.class')

    // copy 至ctiLibs目录,build/libs目录是中间目录,编译时候自动删除不可控
    copy {
        String libDir = file('build/ctiLibs')
        from("build/libs")
        into("build/ctiLibs")
    }
}


dependencies {
    implementation fileTree(dir: 'libs', include: '*.jar')
    implementation project(':Core')
//    implementation project(':http-core')
//    implementation 'com.google.firebase:firebase-crashlytics-buildtools:2.9.2'
//    implementation 'androidx.annotation:annotation:1.1.0'

    compileOnly 'com.centauri.comm:centauricommon-log:2.0.3'
//    implementation 'com.android.billingclient:billing:2.2.0'
    implementation 'com.android.billingclient:billing:6.1.0'
}


//task sourcesJar(type: Jar) {
//    from "build/intermediates/classes/release"
//    //添加依赖包
//    from {
//        configurations.compile.collect {
//            it.isDirectory() ? it : zipTree(it)
//        }
//    }
//}

group 'com.centauri.sdk'

//发布到thirdparty-snapshots版本需要添加-SNAPSHOT后缀
version "${android.defaultConfig.versionName}"

publishing {
    repositories {
        maven {
            credentials {
                username 'rdm'
                password 'rdm'
            }
            url project.uri("http://maven.oa.com/nexus/content/repositories/thirdparty-snapshots")
        }
    }
    publications {
        log(MavenPublication) {
            artifactId 'oversea-googleplay'
            artifact "build/intermediates/packaged-classes/release/classes.jar"
            artifact "build/outputs/aar/${project.getName()}-release.aar"
            //The publication doesn't know about our dependencies, so we have to manually add them to the pom
            pom.withXml {
                def dependenciesNode = asNode().appendNode('dependencies')

                //Iterate over the compile dependencies (we don't want the test ones), adding a <dependency> node for each
                configurations.compile.allDependencies.each {
                    if (it instanceof ExternalModuleDependency) {
                        def dependencyNode = dependenciesNode.appendNode('dependency')
                        dependencyNode.appendNode('groupId', it.group)
                        dependencyNode.appendNode('artifactId', it.name)
                        dependencyNode.appendNode('version', it.version)
                    }
                }
            }
        }
    }
}

gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        if (project.hasProperty("forCoverity")) {
            options.sourcepath = files('/this/directory/must/not/exists')
        }
    }
}
