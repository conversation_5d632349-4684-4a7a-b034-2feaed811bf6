//内核
include ':Core'

//商城页
//include ':MidasOverSeaMall'

//渠道
//include ':channel:37Wan:FacebookLib'
//include ':channel:37Wan:GM99Demo'
//include ':channel:37Wan:GMSLib'
//include ':channel:37Wan:Lib'
//include ':channel:37Wan:Sample'
//include ':channel:37Wan:SDK'
//include ':channel:Feiliu:Sample'
//include ':channel:Feiliu:SDK'

//include ':channel:Garena:Lib'
//include ':channel:Garena:Sample'
//include ':channel:Garena:SDK'

//include ':channel:Link:Lib'
//include ':channel:Link:Sample'
//include ':channel:Link:SDK'
//include ':channel:Stove:Lib'
//include ':channel:Stove:Sample'
//include ':channel:Stove:SDK'
//include ':channel:Stove:UnitySample'
//
//include ':channel:TStore:Lib'
//
//include ':channel:Netmarble:Lib'
//include ':channel:Netmarble:Sample'
//include ':channel:Netmarble:SDK'
//include ':channel:Netmarble:kakao-v2.3.5'
//include ':channel:Netmarble:netmarble-kakao2-1.1.0.4000'
//
//include ':channel:Ledou_korea:app'
//include ':channel:Ledou_korea:korealibrary'
//include ':channel:Ledou_korea:FacebookLibrary'
//include ':channel:Ledou_korea:NaverLibrary'
//include ':channel:Ledou_korea:midas_pay_lib_ledou_korea'

//include ':channel:huawei:lib'
//include ':channel:SamSung:Lib'
//include ':channel:SamSung:SDK'
//include ':channel:huawei:app'
include ':channel:GoogleWallet'
//include ':channel:Garena'
//include ':channel:Netmarble'
//include ':channel:VNG'
//include ':channel:ToyPay'
//include ':channel:Xiaomi'

//include ':http-core'
//include  ':channel:MidasOversea_amazon'
//include ':channel:Boku'
//include ':channel:Doku'
//include ':channel:Fortumo'
//include ':channel:Mol'
//include ':channel:PaymentWall'
//include ':channel:Xsolla'
//include ':channel:MyCard'

//include ':channel:OneStore'

//Demo
//include ':Sample:NewSample'
include ':Sample:SampleForAAR'
//include ':debugview'
//include ':channel:huawei'
//include ':channel:huawei:app'
include ':centauri-cores'
include ':centauri-jar'
