<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.centauri.oversea">

    <!--Centauri 支付通用权限 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Google Wallet支付需要的权限 -->
    <uses-permission android:name="com.android.vending.BILLING" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <application
        android:usesCleartextTraffic="true">
        <!-- Centauri集成通用 Activity -->
        <activity
            android:name="com.centauri.oversea.business.pay.CTIProxyActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
        </activity>

    </application>


</manifest>