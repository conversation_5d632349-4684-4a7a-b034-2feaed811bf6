apply plugin: 'com.android.application'

android {
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 14
        targetSdkVersion 34
        versionCode 501061
        versionName "5.01.061"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        flavorDimensions "versionCode"
    }
    productFlavors {
        unityapk {

            dimension "versionCode"
        }

        ueapk {
            dimension "versionCode"
        }

        appapk {
            dimension "versionCode"
        }
    }
    buildTypes {
        debug {
            minifyEnabled false
//            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        abortOnError false
    }
    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                //这里修改apk文件名
                outputFileName = "centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk"
            }
    }
    /**
     * build aar and rename aar by dongbingliu 2020-10-30 星期五
     */
    setVersion(defaultConfig.versionName)

}
// 把jar放到libs目录后，调用脚本执行
// 脚本先生成apk，把apk转换成jar包，生成后的jar包在build/ctiLibs
task runCmdUe(type: Exec) {
    dependsOn 'assembleRelease'
    workingDir 'dex-tools'
//    commandLine 'sh', '-c', "./d2j-dex2jar.sh ../build/outputs/apk/release/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk --force"
    commandLine 'sh', '-c', "./d2j-dex2jar.sh ../build/outputs/apk/ueapk/release/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk --force"
    doLast {
        copy {
            String libDir = file('build/ctiLibs')
            from("dex-tools")
            into("build/ctiLibs")
            include("centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-dex2jar.jar")
            rename { String fileName ->
                fileName = "centauri-ue-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.jar"
            }
        }
        delete("dex-tools/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-dex2jar.jar")
    }
}

task runCmdUnity(type: Exec) {
    dependsOn 'assembleRelease'
    workingDir 'dex-tools'
//    commandLine 'sh', '-c', "./d2j-dex2jar.sh ../build/outputs/apk/release/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk --force"
    commandLine 'sh', '-c', "./d2j-dex2jar.sh ../build/outputs/apk/unityapk/release/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk --force"
    doLast {
        copy {
            String libDir = file('build/ctiLibs')
            from("dex-tools")
            into("build/ctiLibs")
            include("centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-dex2jar.jar")
            rename { String fileName ->
                fileName = "centauri-unity-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.jar"
            }
        }
        delete("dex-tools/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-dex2jar.jar")
    }

}

task runCmdAndroid(type: Exec) {
    dependsOn 'assembleRelease'
    workingDir 'dex-tools'
//    commandLine 'sh', '-c', "./d2j-dex2jar.sh ../build/outputs/apk/release/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk --force"
    commandLine 'sh', '-c', "./d2j-dex2jar.sh ../build/outputs/apk/appapk/release/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk --force"
    doLast {
        copy {
            String libDir = file('build/ctiLibs')
            from("dex-tools")
            into("build/ctiLibs")
            include("centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-dex2jar.jar")
            rename { String fileName ->
                fileName = "centauri-android-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.jar"
            }
        }
        delete("dex-tools/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-dex2jar.jar")
    }
}
//build.finalizedBy runCmd
//task runShAfterAssembleRelease(type: Exec) {
//    dependsOn 'assembleRelease'
//    doLast {
//        workingDir 'dex-tools'
//        commandLine 'sh', '-c', "rm centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.jar"
//        commandLine 'sh', '-c', "./d2j-dex2jar.sh ../build/outputs/apk/release/centauri-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.apk"
//
//    }
//}


dependencies {

    implementation fileTree(dir: 'libs', include: '*.jar')
    unityapkImplementation fileTree(dir: 'libs_unity', include: '*.jar')
    ueapkImplementation fileTree(dir: 'libs_ue', include: '*.jar')
//    implementation 'com.android.billingclient:billing:5.1.0'
//    implementation 'com.android.support:appcompat-v7:28.1.1'
}