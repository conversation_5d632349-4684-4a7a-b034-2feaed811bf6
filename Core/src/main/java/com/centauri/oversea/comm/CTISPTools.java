
package com.centauri.oversea.comm;


import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.centauri.comm.CTILog;
import com.centauri.oversea.newapi.CTIPayNewAPI;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * SP tools
 */
public class CTISPTools {
    public static final String TAG = "CTISPTools";
    public static final String SP_NAME = "CentauriUnipay";        // By default, only read and write data from this SP
    public static final String SP_IP_INFO = "CentauriOverseaIP";   // IP information is stored separately
    public static final String SP_NAME_PURCHASE = "CentauriUnipay_pp";        // By default, only read and write data from this SP


    public static String getString(Context context, String key) {
        return context.getSharedPreferences(SP_NAME, 0).getString(key, "");
    }

    public static String getString(Context context, String spName, String key) {
        return context.getSharedPreferences(spName, 0).getString(key, "");
    }

    public static Map<String, ?> getAll(String spName) {
        return CTIPayNewAPI.singleton().getApplicationContext().getSharedPreferences(spName, 0).getAll();
    }

    /**
     * Sort by value and remove more than 10 caches
     */
    public static void sortSPByPurchase() {
        Map<String, Long> allMap = (Map<String, Long>) getAll(SP_NAME_PURCHASE);
        Log.e(TAG, "sortSPByPurchase allMap " + allMap.size());
        if (null != allMap && allMap.size() > 20) {
            List<Map.Entry<String, Long>> list = new ArrayList<>(allMap.entrySet());
            //The comparator is then used to implement sorting
            Collections.sort(list, new Comparator<Map.Entry<String, Long>>() {
                //升序排序
                public int compare(Map.Entry<String, Long> o1,
                                   Map.Entry<String, Long> o2) {
                    return o1.getValue().compareTo(o2.getValue());
                }

            });
            for (Map.Entry<String, Long> key : list) {
                allMap.remove(key.getKey());
                removeByKey(SP_NAME_PURCHASE, key.getKey());
                if (allMap.size() <= 20) {
                    return;
                }
            }
        }

    }

    public static long getLong(String spName, String key) {
        return CTIPayNewAPI.singleton().getApplicationContext().getSharedPreferences(spName, 0).getLong(key, -1);
    }

    public static void putString(Context context, String key, String value) {
        context.getSharedPreferences(SP_NAME, 0).edit()
                .putString(key, value)
                .apply();
    }

    public static void putBoolean(Context context, String key, boolean value) {
        context.getSharedPreferences(SP_NAME, 0).edit()
                .putBoolean(key, value)
                .apply();
    }

    public static void putLong(String spName, String key, long value) {
        CTIPayNewAPI.singleton().getApplicationContext().getSharedPreferences(spName, 0).edit()
                .putLong(key, value)
                .apply();
    }

    public static void putString(Context context, String spName, String key, String value) {
        context.getSharedPreferences(spName, 0).edit()
                .putString(key, value)
                .apply();
    }

    public static int getInt(Context context, String key) {
        return context.getSharedPreferences(SP_NAME, 0).getInt(key, 0);
    }

    public static boolean getBoolean(Context context, String key) {
        return context.getSharedPreferences(SP_NAME, 0).getBoolean(key, true);
    }

    public static void putInt(Context context, String key, int value) {
        context.getSharedPreferences(SP_NAME, 0).edit()
                .putInt(key, value)
                .apply();
    }

    public static void removeByKey(String spName, String key) {
        CTIPayNewAPI.singleton().getApplicationContext().getSharedPreferences(spName, 0).edit()
                .remove(key)
                .apply();
    }

    public static SharedPreferences getSP(Context context, String spName) {
        return context.getSharedPreferences(spName, Context.MODE_PRIVATE);
    }

    public static void clear(String spName) {
        CTIPayNewAPI.singleton().getApplicationContext().getSharedPreferences(spName, 0)
                .edit()
                .clear()
                .apply();
    }
}