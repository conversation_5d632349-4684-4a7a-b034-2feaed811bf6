package com.centauri.oversea.comm;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import com.centauri.comm.CTILog;
import com.centauri.http.core.Response;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import org.apache.http.conn.ConnectTimeoutException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InvalidClassException;
import java.io.InvalidObjectException;
import java.io.NotActiveException;
import java.io.NotSerializableException;
import java.io.OptionalDataException;
import java.io.StreamCorruptedException;
import java.io.SyncFailedException;
import java.io.UTFDataFormatException;
import java.io.UnsupportedEncodingException;
import java.io.WriteAbortedException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.net.BindException;
import java.net.ConnectException;
import java.net.HttpRetryException;
import java.net.MalformedURLException;
import java.net.NoRouteToHostException;
import java.net.PortUnreachableException;
import java.net.ProtocolException;
import java.net.SocketTimeoutException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;
import java.nio.channels.ClosedChannelException;
import java.nio.channels.FileLockInterruptionException;
import java.nio.charset.MalformedInputException;
import java.nio.charset.UnmappableCharacterException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateNotYetValidException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.InvalidPropertiesFormatException;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipException;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.SSLHandshakeException;
import javax.net.ssl.SSLKeyException;
import javax.net.ssl.SSLPeerUnverifiedException;
import javax.net.ssl.SSLProtocolException;
import javax.security.cert.CertificateExpiredException;

/**
 * public method
 **/
public class CTITools {

    private static final String TAG = "APTools";


    /************************************* net tools *************************************/

    /**
     * Network not connected, pop-up prompt set
     * @param context
     */
    public static void setNetwork(final Context context) {
        //The pop-up prompts for networking
        MAlertDialog.Builder builder = new MAlertDialog.Builder(context);
        builder.setTitle("error")
                .setContent(CTICommMethod.getStringId(context, "unipay_error_network_not_connected"))
                .setDialogButton(CTICommMethod.getStringId(context, "unipay_tip_setting"), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        //Skip to the System Settings screen
                        Intent intent = new Intent(Settings.ACTION_SETTINGS);
                        context.startActivity(intent);
                        dialog.cancel();
                    }
                })
                .setCancelable(false)
                .create()
                .show();
    }

    /**
     * Network connection or not
     */
    public static boolean isNetworkAvailable(Context context) {
        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                return true;
            }
        } catch (Exception e) {
            CTILog.e(TAG, "isNetworkAvailable(): " + e.getMessage());
        }
        return false;
    }


    /**
     * @param context
     * @return 0 No network condition 1. FAP network 2.2G network 3.3G network 4. WIFI network
     */
    public static int getNetWorkType(Context context) {
        int mNetWorkType = -1;
        try {
            //Determine if there is a network
            if (isNetworkConnect(context)) {
                //The situation that has network subdivides again
                if (isNetworkWIFI(context)) {//wifi
                    mNetWorkType = 1000;
                } else if (isNetwork4G(context)) {//4g
                    mNetWorkType = 4;
                } else if (isNetwork3G(context)) {//3g
                    mNetWorkType = 3;
                } else if (isWAP(context)) {//wap
                    mNetWorkType = 1;
                } else {
                    mNetWorkType = 2;
                }
            }

        } catch (Exception e) {
            CTILog.e(TAG, "getNetWorkType(): " + e.getMessage());
        }
        return mNetWorkType;
    }

    /**
     * Determining whether the current network type is WAP, such as CMWAP, 3GWAP, etc
     *
     * @param context
     * @return
     */
    public static boolean isWAP(Context context) {
        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                String type = networkInfo.getTypeName();
                if ("MOBILE".equals(type)) {
                    String proxyHost = android.net.Proxy.getDefaultHost();
                    if (!TextUtils.isEmpty(proxyHost) && proxyHost.contains("wap") || proxyHost.contains("WAP")) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            CTILog.e(TAG, "isWAP(): " + e.getMessage());
        }
        return false;
    }


    //Determine if there is a network
    public static boolean isNetworkConnect(Context context) {
        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                return true;
            }
        } catch (Exception e) {
            CTILog.e(TAG, "isNetworkConnect(): " + e.getMessage());
        }

        return false;
    }

    //Determine whether there is a WiFi network
    public static boolean isNetworkWIFI(Context context) {
        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                String type = networkInfo.getTypeName();
                if (type.equalsIgnoreCase("WIFI")) {
                    return true;
                }
            }
        } catch (Exception e) {
            CTILog.e(TAG, "isNetworkWIFI(): " + e.getMessage());
        }

        return false;
    }

    //Determine whether 3G network
    public static boolean isNetwork3G(Context context) {
        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                String type = networkInfo.getTypeName();
                if (type.equalsIgnoreCase("MOBILE")) {
                    TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                    switch (telephonyManager.getNetworkType()) {
                        case TelephonyManager.NETWORK_TYPE_EVDO_A:
                            return true; // ~ 600-1400 kbps
                        case TelephonyManager.NETWORK_TYPE_HSDPA:
                            return true; // ~ 2-14 Mbps
                        case TelephonyManager.NETWORK_TYPE_HSPA:
                            return true; // ~ 700-1700 kbps
                        case TelephonyManager.NETWORK_TYPE_HSUPA:
                            return true; // ~ 1-23 Mbps
                        case TelephonyManager.NETWORK_TYPE_UMTS:
                            return true; // ~ 400-7000 kbps
                        default:
                            return false;
                    }
                }
            }
        } catch (Exception e) {
            CTILog.e(TAG, "isNetwork3G(): " + e.getMessage());
        }

        return false;
    }

    //Determine whether 4G network
    public static boolean isNetwork4G(Context context) {
        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                String type = networkInfo.getTypeName();
                if (type.equalsIgnoreCase("MOBILE")) {
                    TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                    switch (telephonyManager.getNetworkType()) {
                        case TelephonyManager.NETWORK_TYPE_LTE:  //4g
                            return true; // ~ 10+ Mbps
                        default:
                            return false;
                    }
                }
            }
        } catch (Exception e) {
            CTILog.e(TAG, "isNetwork4G(): " + e.getMessage());
        }

        return false;
    }


    /************************************* Universal tool method *************************************/

    /**
     * Multiple coding method
     * @param srcMsg What needs to be encoded
     * @param number of urlencode times
     **/
    public static String urlEncode(String srcMsg, int number) {
        String desMsg = "";

        if (!TextUtils.isEmpty(srcMsg)) {
            // If the number of encodes is less than 0, the original string is returned
            if (number <= 0) {
                return srcMsg;
            } else {// Otherwise encode the number of times specified
                for (int i = 0; i < number; i++) {
                    try {
                        desMsg = URLEncoder.encode(srcMsg, "utf-8");
                    } catch (Exception ex) {
                        CTILog.i("urlEncode", ex.toString());
                    }
                    srcMsg = desMsg;
                }
            }
        } else {
            CTILog.i("urlEncode", "The encoding content is empty");
        }

        return desMsg;
    }


    /**
     * Multiple decoding method
     * @param srcMsg What needs to be encoded
     * @param number of urlencode times
     **/
    public static String urlDecode(String srcMsg, int number) {
        String desMsg = "";

        if (!TextUtils.isEmpty(srcMsg)) {
            // If the number of encodes is less than 0, the original string is returned
            if (number <= 0) {
                return srcMsg;
            } else {// Otherwise encode the number of times specified
                try {
                    for (int i = 0; i < number; i++) {
                        desMsg = URLDecoder.decode(srcMsg, "utf-8");
                        srcMsg = desMsg;
                    }
                } catch (Exception e) {
                    CTILog.e(TAG, "urlDecode(): " + e.getMessage());
                }
            }
        } else {
            CTILog.w("", "The decoded content is empty");
        }

        return desMsg;
    }


    //url to map
    public static HashMap<String, String> url2Map(String url) {
        HashMap<String, String> desHashMap = new HashMap<String, String>();

        String params = url.split("\\?")[1];

        try {
            if (!TextUtils.isEmpty(params)) {
                String[] paramArray = params.split("&");

                for (int i = 0; i < paramArray.length; i++) {
                    String[] kv = paramArray[i].split("=");
                    if (!TextUtils.isEmpty(kv[0])) {
                        desHashMap.put(kv[0], kv[1]);
                    }
                }
            } else {
                CTILog.i("url2Map", "The parameter after the URL is null");
            }
        } catch (Exception e) {
            CTILog.w("url2Map", e.toString());
        }

        return desHashMap;
    }


    /**
     * String to Map
     * @param params origin String
     * @return Map Object
     */
    public static HashMap<String, String> str2Map(String params)
    {
        HashMap<String, String> desHashMap = new HashMap<String, String>();
        try
        {
            if(!TextUtils.isEmpty(params))
            {
                String[] paramArray = params.split("\\&");
                String key = "";
                String value = "";

                for(int i=0; i<paramArray.length; i++)
                {
                    //key = vlue 值有可能为空
                    try
                    {
                        key = paramArray[i].split("\\=")[0];
                        value = paramArray[i].replaceFirst(key+"=","");
                    }
                    catch (Exception e)
                    {
                    }

                    if(!TextUtils.isEmpty(key))
                    {
                        //servicename 需要解码
						/*if(key.equals("servicename") && !TextUtils.isEmpty(value))
						{
							value = URLDecoder.decode(value, "utf-8");
						}*/

                        desHashMap.put(key, value);
                    }
                }
            }
            else
            {
                CTILog.i("url2Map", "empty parameter after url");
            }
        }
        catch (Exception e)
        {
            CTILog.w("url2Map", e.toString());
        }

        return desHashMap;
    }

    //map转js
    public static String map2Js(Map<String,String> map){
        if(map == null || map.isEmpty()){
            return "";
        }

        JSONObject js = new JSONObject();
        try {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                js.put(entry.getKey(),entry.getValue());
            }
        }catch (JSONException e){
            CTILog.i(TAG,"map2Js fail: "+e.getMessage());
        }
        return js.toString();
    }

    //The HashMap turns into an argument that follows the URL, separated by an &
    public static String map2UrlParams(Map<String, String> hashMap) {
        StringBuffer paramBuffer = new StringBuffer();

        try {
            for (Map.Entry<String, String> mapEntry : hashMap.entrySet()) {
                if(!TextUtils.isEmpty(mapEntry.getValue())){
                    paramBuffer.append(mapEntry.getKey());
                    paramBuffer.append("=");
                    paramBuffer.append(mapEntry.getValue());
                    paramBuffer.append("&");
                }
            }

            if (!TextUtils.isEmpty(paramBuffer)) {
                paramBuffer.deleteCharAt(paramBuffer.length() - 1);
            }
        } catch (Exception e) {
            CTILog.e(TAG, "map2UrlParams(): " + e.getMessage());
        }
        return paramBuffer.toString();
    }


    //kv to map
    public static HashMap<String, String> kv2Map(String params) {
        HashMap<String, String> desHashMap = new HashMap<String, String>();
        try {
            if (!TextUtils.isEmpty(params)) {
                String[] paramArray = params.split("&");

                for (int i = 0; i < paramArray.length; i++) {
                    String[] kv = paramArray[i].split("=");
                    if (!TextUtils.isEmpty(kv[0])) {
                        desHashMap.put(kv[0], kv[1]);
                    }
                }
            } else {
                CTILog.i(TAG, "kv2Map(..): The parameter after the URL is null");
            }
        } catch (Exception e) {
            CTILog.e(TAG, "kv2Map(): " + e.getMessage());
        }
        return desHashMap;
    }

    public static String AESString(String encryptedText, String version) {
        try {
            String key;
            if ("v1".equals(version)) {
                key = "xRLo856Mp9Kd9C2mzJiX8F7IweYXM0Bh";
            } else {
                key = "6mznDE/6ezIfPs9DGVuKcTAH/G1JGD+B";
            }
            String iv = "1234567890123456";

            byte[] keyBytes = key.getBytes();
            byte[] ivBytes = iv.getBytes();

            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(ivBytes));

            byte[] decryptedBytes = cipher.doFinal(hexStringToByteArray(encryptedText));
            String decryptedText = new String(decryptedBytes);
            return decryptedText;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
    /**
     * Signs the string
     * @param data Signs the data
     * @param algorithm : MD5, SHA1
     * @return
     */
    public static String signString(String data, String algorithm) {
        try {
            // The encryption algorithm is MD5 or SHA1
            MessageDigest messageDigest = MessageDigest.getInstance(algorithm);
            messageDigest.update(data.getBytes());
            // The array of characters is returned as a string
            return byteArrayToHex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            CTILog.e(TAG, "signString(): " + e.getMessage());
        }
        return null;
    }


    public static String byteArrayToHex(byte[] byteArray) {
        // First we initialize a character array to hold each hexadecimal character
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'};
        // new an array of characters, which is used to form the resulting string. (Note: a byte is an eight-bit binary, i.e. a 2-bit hexadecimal character (2 to the eighth equals 16 to the second).)
        char[] resultCharArray = new char[byteArray.length * 2];

        // Convert the byte array into a character array by using bitwise operations (bitwise is more efficient)
        int index = 0;
        for (byte b : byteArray) {
            resultCharArray[index++] = hexDigits[b >> 4 & 0x0f];
            resultCharArray[index++] = hexDigits[b & 0x0f];
        }

        // The array of characters is returned as a string
        return new String(resultCharArray);
    }

    // Determine if this is a test environment
    public static boolean isTestEnv() {
        return !MConstants.ReleaseEnv.equals(GlobalData.singleton().env);
    }

    /************************************* net tools ******************************************/

    private static final Pattern IPV4_PATTERN =
            Pattern.compile(
                    "^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$");
    private static final Pattern IPV6_STD_PATTERN =
            Pattern.compile(
                    "^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$");
    private static final Pattern IPV6_HEX_COMPRESSED_PATTERN =
            Pattern.compile(
                    "^((?:[0-9A-Fa-f]{1,4}(?::[0-9A-Fa-f]{1,4})*)?)::((?:[0-9A-Fa-f]{1,4}(?::[0-9A-Fa-f]{1,4})*)?)$");

    public static boolean isIPv4Address(final String input) {
        return IPV4_PATTERN.matcher(input).matches();
    }

    public static boolean isIPv6StdAddress(final String input) {
        return IPV6_STD_PATTERN.matcher(input).matches();
    }

    public static boolean isIPv6HexCompressedAddress(final String input) {
        return IPV6_HEX_COMPRESSED_PATTERN.matcher(input).matches();
    }

    //Determine if it is IP address
    public static boolean isIPAddress(final String hostname) {
        return hostname != null && (
                CTITools.isIPv4Address(hostname)
                        || CTITools.isIPv6StdAddress(hostname)
                        || CTITools.isIPv6HexCompressedAddress(hostname));
    }


    //Is WiFi configured with proxies
    public static boolean isWifiProxy(Throwable t) {
        CTILog.w("APBaseHttpReq", "isWifiProxy https exception" + t.toString());
        String host = android.net.Proxy.getDefaultHost();
        //The current network type is WIFI, and the proxy host is not empty
        if (getNetWorkType(CTIPayNewAPI.singleton().getApplicationContext()) == 1000 && !TextUtils.isEmpty(host)) {
            String sThrowable = t.toString();
            //Exception is also the system's current time is incorrect
            // javax.net.ssl.SSLHandshakeException: java.security.cert.CertPathValidatorException: Trust anchor for certification path not found.
            //Caused by: java.security.cert.CertificateException: java.security.cert.CertPathValidatorException: Trust anchor for certification path not found.
            if (sThrowable.contains("Trust anchor for certification path not found")) {
                return true;
            }
        }

        return false;
    }

    //Whether the SSLv3 protocol caused the problem
    public static boolean isSSLV3Error(Throwable t) {
        CTILog.w("APBaseHttpReq", "isSSLV3Error https exception" + t.toString());
        String sThrowable = t.toString();

        //javax.net.ssl.SSLHandshakeException: javax.net.ssl.SSLProtocolException: SSL handshake aborted: ssl=0x1fe7ed8: Failure in SSL library, usually a protocol error
        //error:1407743E:SSL routines:SSL23_GET_SERVER_HELLO:tlsv1 alert inappropriate fallback (external/openssl/ssl/s23_clnt.c:661 0x402abcc3:0x00000000)
        if ((sThrowable.contains("SSL handshake aborted") && sThrowable.contains("usually a protocol error"))
                || sThrowable.contains("GET_SERVER_HELLO")) {
            return true;
        }
        return false;
    }

    //Is the current HTTPS request Exception due to an incorrect time
    public static boolean isTimeError(Throwable t) {
        CTILog.w("APBaseHttpReq", "isTimeError https exception" + t.toString());
        String sThrowable = t.toString();
        if (t instanceof CertificateExpiredException || t instanceof CertificateNotYetValidException) {
            return true;
        }

        if (TextUtils.isEmpty(sThrowable)) {
            return false;
        }
        //Exception is also the system's current time is incorrect
        //javax.net.ssl.SSLHandshakeException: com.android.org.bouncycastle.jce.exception.ExtCertPathValidatorException:
        //Could not validate certificate: current time: Mon Jan 12 12:50:58 GMT+08:00 1970, validation time: Fri Jan 01 08:00:00 GMT+08:00 2010
        if (sThrowable.contains("validation time") && sThrowable.contains("current time")) {
            return true;
        }

        //javax.net.ssl.SSLHandshakeException: com.android.org.bouncycastle.jce.exception.ExtCertPathValidatorException:
        //Could not validate certificate: Certificate not valid until Thu Oct 31 08:00:00 GMT+08:00 2013 (compared to Sun Aug 30 14:53:54 GMT+08:00 2009)
        if (sThrowable.contains("GMT") && sThrowable.contains("compared to")) {
            return true;
        }
        //If the certificate has not been verified, compare the time of the client with that of our server
        if (sThrowable.contains("Could not validate certificate")) {
            final long certTime = 1451577600; //20160101 GMT
            long curretTime = System.currentTimeMillis() / 1000; // Convert to seconds
            if (curretTime > 0 && certTime > curretTime) {
                return true;
            }
        }
        return false;
    }


    /**
     * Exceptions that occur during HTTP requests are converted to errorTypes that correspond to each of them. The errorTypes are converted primarily for reporting purposes
     *
     * @param e Exception occurred during a network request
     * @return The errorType corresponding to the passed Exception
     */
    public static int getErrorTypeFromException(final Exception e) {
        if (e == null) {
            // An empty Exception is passed to the outside, indicating that no Exception has occurred for this network connection
            // errorType
            return NetErrConstants.ERROR_HTTP_STATUS;
        }

        if (e instanceof CharConversionException) {
            return NetErrConstants.ERROR_IO_CharConversionException;
        } else if (e instanceof MalformedInputException) {
            return NetErrConstants.ERROR_IO_CharacterCodingException_MalformedInputException;
        } else if (e instanceof UnmappableCharacterException) {
            return NetErrConstants.ERROR_IO_CharacterCodingException_UnmappableCharacterException;
        } else if (e instanceof ClosedChannelException) {
            return NetErrConstants.ERROR_IO_ClosedChannelException;
        } else if (e instanceof EOFException) {
            return NetErrConstants.ERROR_IO_EOFException;
        } else if (e instanceof FileLockInterruptionException) {
            return NetErrConstants.ERROR_IO_FileLockInterruptionException;
        } else if (e instanceof FileNotFoundException) {
            return NetErrConstants.ERROR_IO_FileNotFoundException;
        } else if (e instanceof HttpRetryException) {
            return NetErrConstants.ERROR_IO_HttpRetryException;
        } else if (e instanceof ConnectTimeoutException) {
            return NetErrConstants.ERROR_IO_InterruptedIOException_ConnectTimeoutException;
        } else if (e instanceof SocketTimeoutException) {
            return NetErrConstants.ERROR_IO_InterruptedIOException_SocketTimeoutException;
        } else if (e instanceof InvalidPropertiesFormatException) {
            return NetErrConstants.ERROR_IO_InvalidPropertiesFormatException;
        } else if (e instanceof MalformedURLException) {
            return NetErrConstants.ERROR_IO_MalformedURLException;
        } else if (e instanceof InvalidClassException) {
            return NetErrConstants.ERROR_IO_ObjectStreamException_InvalidClassException;
        } else if (e instanceof InvalidObjectException) {
            return NetErrConstants.ERROR_IO_ObjectStreamException_InvalidObjectException;
        } else if (e instanceof NotActiveException) {
            return NetErrConstants.ERROR_IO_ObjectStreamException_NotActiveException;
        } else if (e instanceof NotSerializableException) {
            return NetErrConstants.ERROR_IO_ObjectStreamException_NotSerializableException;
        } else if (e instanceof OptionalDataException) {
            return NetErrConstants.ERROR_IO_ObjectStreamException_OptionalDataException;
        } else if (e instanceof StreamCorruptedException) {
            return NetErrConstants.ERROR_IO_ObjectStreamException_StreamCorruptedException;
        } else if (e instanceof WriteAbortedException) {
            return NetErrConstants.ERROR_IO_ObjectStreamException_WriteAbortedException;
        } else if (e instanceof ProtocolException) {
            return NetErrConstants.ERROR_IO_ProtocolException;
        } else if (e instanceof SSLHandshakeException) {
            Throwable t = e;
            while (t != null) {
                CTILog.d(TAG, "https exception" + t.toString());
                // The current phone has the wrong time
                if (isTimeError(t)) {
                    return NetErrConstants.ERROR_IO_SSLException_TIMEException;
                }

                // The WiFi proxy does not support HTTPS
                if (isWifiProxy(t)) {
                    return NetErrConstants.ERROR_IO_SSLException_WIFIPROXY;
                }

                if (isSSLV3Error(t)) {
                    return NetErrConstants.ERROR_IO_SSLV3ERROR;
                }

                t = t.getCause();
            }

            return NetErrConstants.ERROR_IO_SSLException_SSLHandshakeException;
        } else if (e instanceof SSLKeyException) {
            return NetErrConstants.ERROR_IO_SSLException_SSLKeyException;
        } else if (e instanceof SSLPeerUnverifiedException) {
            return NetErrConstants.ERROR_IO_SSLException_SSLPeerUnverifiedException;
        } else if (e instanceof SSLProtocolException) {
            return NetErrConstants.ERROR_IO_SSLException_SSLProtocolException;
        } else if (e instanceof BindException) {
            return NetErrConstants.ERROR_IO_SocketException_BindException;
        } else if (e instanceof ConnectException) {
            return NetErrConstants.ERROR_IO_SocketException_ConnectException;
        } else if (e instanceof NoRouteToHostException) {
            return NetErrConstants.ERROR_IO_SocketException_NoRouteToHostException;
        } else if (e instanceof PortUnreachableException) {
            return NetErrConstants.ERROR_IO_SocketException_PortUnreachableException;
        } else if (e instanceof SyncFailedException) {
            return NetErrConstants.ERROR_IO_SyncFailedException;
        } else if (e instanceof UTFDataFormatException) {
            return NetErrConstants.ERROR_IO_UTFDataFormatException;
        } else if (e instanceof UnknownHostException) {
            return NetErrConstants.ERROR_IO_UnknownHostException;
        } else if (e instanceof UnknownServiceException) {
            return NetErrConstants.ERROR_IO_UnknownServiceException;
        } else if (e instanceof UnsupportedEncodingException) {
            return NetErrConstants.ERROR_IO_UnsupportedEncodingException;
        } else if (e instanceof ZipException) {
            return NetErrConstants.ERROR_IO_ZipException;
        }

        String exceptionInfo = e.toString();

        if (exceptionInfo != null) {
            // recvfrom failed: ECONNRESET (Connection reset by peer)
            if (exceptionInfo.contains("Connection reset by peer")) {
                return NetErrConstants.ERROR_IO_ECONNRESET;
            }
        }

        return NetErrConstants.ERROR_UNKNOWN;
    }

    /**
     * report ResponseCode
     *
     * @return response code
     */
    public static int getResponseCodeForDataReport(final Response response) {
        int result;

        if (response.exception == null) {
            result = response.resultCode;
        } else {
            final Exception e = response.exception;
            if (e instanceof ConnectTimeoutException) {
                result = NetErrConstants.ERROR_NETWORK_CONTIMEOUT;
            } else if (e instanceof SocketTimeoutException) {
                result = NetErrConstants.ERROR_NETWORK_READTIMEOUT;
            } else if (e instanceof IOException) {
                result = NetErrConstants.ERROR_NETWORK_IOEXCEPTION;
            } else {
                result = NetErrConstants.ERROR_NETWORK_SYSTEM;
            }
        }

        return result;
    }


    /**
     * is debug
     * @param context Conext
     * @return if the APK is debugable
     */
    public static boolean isApkInDebug(Context context) {
        try {
            ApplicationInfo info = context.getApplicationInfo();
            return (info.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * @param context Application Context
     * @return The name of the current process, or unkown if not found
     */
   public static String getProcessName(Context context){
       String currentProcName = "unkown";
       int pid = android.os.Process.myPid();
       ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
       for (ActivityManager.RunningAppProcessInfo processInfo : manager.getRunningAppProcesses())
       {
           if (processInfo.pid == pid)
           {
               currentProcName = processInfo.processName;
               break;
           }
       }
       return currentProcName;
   }

    public static String getJsonValue(String jsonResource, String key){

        String value = "";
        if(!TextUtils.isEmpty(jsonResource)){
            try{
                JSONObject js = new JSONObject(jsonResource);
                value = js.optString(key);
            }catch (JSONException e){
                CTILog.d(TAG,"setJsResource exception: "+e.getMessage());
            }
        }
        return value;
    }

    public static Activity getCurrentActivity() {
//        try {
//            Class activityThreadClass = Class.forName("android.app.ActivityThread");
//            Object activityThread = activityThreadClass.getMethod("currentActivityThread").invoke(null);
//            Object activityClientRecord = activityThreadClass.getMethod("getActivityClientRecord").invoke(activityThread);
//            Object activity = activityClientRecord.getClass().getDeclaredField("activity").get(activityClientRecord);
//            return (Activity) activity;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        }
        Class activityThreadClass = null;
        try {
            activityThreadClass = Class.forName("android.app.ActivityThread");
            Object activityThread = activityThreadClass.getMethod("currentActivityThread").invoke(null);
            Field activitiesField = activityThreadClass.getDeclaredField("mActivities");
            activitiesField.setAccessible(true);
            Map activities = (Map) activitiesField.get(activityThread);
            for (Object activityRecord : activities.values()) {
                Class activityRecordClass = activityRecord.getClass();
                Field pausedField = activityRecordClass.getDeclaredField("paused");
                pausedField.setAccessible(true);
                if (!pausedField.getBoolean(activityRecord)) {
                    Field activityField = activityRecordClass.getDeclaredField("activity");
                    activityField.setAccessible(true);
                    Activity activity = (Activity) activityField.get(activityRecord);
                    return activity;
                }
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getCentuarimds(){
        StringBuffer tmp = new StringBuffer();

        tmp.append("m");
        tmp.append("i");
        tmp.append("d");
        tmp.append("a");
        tmp.append("s");
        return tmp.toString();
    }

    public static String getCentuariMds(){
        String[] strings = {"Ike", "ce", "elta", "lpha", "ierra"};
        String mds = CTITools.getCentauriString(strings);
        return mds;
    }

    public static String getCentauriBgl(){
        String[] strings = {"ravo", "niform", "olf", "ima", "ankee"};
        String bgl = CTITools.getCentauriString(strings);
        return bgl;
    }

    public static String getCentuariMDS(){
        return "";
    }

    public static String getCentuaribuy(){
        String[] strings = {"ravo", "niform", "ankee", "ot", "harlie", "scar", "ike"};
        String buy = CTITools.getCentauriString(strings);
        return buy;
    }

    public static String getCentauriString(String[] strings) {
        Context context = CTIPayNewAPI.singleton().getApplicationContext();
        if (context == null) {
            CTILog.e(TAG, "getCentauriString called, context null");
            return "";
        }
        StringBuilder builder = new StringBuilder();
        for (String string : strings) {
            try {
                String resourceId = CTICommMethod.getStringId(context, string);
                if (resourceId.length() > 0) {
                    builder.append(resourceId.charAt(0));
                }
            } catch (Exception e) {
                CTILog.e(TAG, "getCentauriString catch");
                return "";
            }
        }
        return builder.toString();
    }

    public static JSONArray getArrayByTypeFromMap(HashMap<String, String> map, String type) {
        if (map == null || map.isEmpty()) {
            return null;
        }

        JSONArray jsonArray = new JSONArray();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(type)) {
                jsonArray.put(entry.getKey());
            }
        }
        return jsonArray;
    }
}

