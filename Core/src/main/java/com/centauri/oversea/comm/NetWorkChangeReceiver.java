package com.centauri.oversea.comm;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.text.TextUtils;

import com.centauri.comm.CTILog;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2017/12/29.
 */

public class NetWorkChangeReceiver extends BroadcastReceiver {
    public static final String TAG = "NetWorkChangeReceiver";

    public static final String NETWORK_CHANGE_ACTION = "android.net.conn.CONNECTIVITY_CHANGE";
    private final long NETWORK_CHANGE_DURATION = 500;   //Network changes will be broadcast multiple times
    private long lastWifiConnectedTime = 0;

    @Override
    public void onReceive(Context context, Intent intent) {

        if(NETWORK_CHANGE_ACTION.equals(intent.getAction())){
            boolean disconnect = intent.getBooleanExtra(ConnectivityManager.EXTRA_NO_CONNECTIVITY, false);
            ConnectivityManager manager = (ConnectivityManager)context.getSystemService(
                    Context.CONNECTIVITY_SERVICE);
            @SuppressLint("MissingPermission")
            NetworkInfo info = manager.getActiveNetworkInfo();

            if (!disconnect) {
                if (info != null && info.getType() == ConnectivityManager.TYPE_WIFI) {
                    long currentTimeMill = System.currentTimeMillis();
                    if (currentTimeMill - lastWifiConnectedTime > NETWORK_CHANGE_DURATION) {
                        lastWifiConnectedTime = currentTimeMill;
                        pingReport();
                    }
                } else {
                    pingReport();
                }
            }
        }
    }


    public static void pingReport() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                String lost = "";
                String delay = "";

                try {
                    ArrayList<String> ipList = GlobalData.singleton().IPManager().getIPList();
                    CTIDataReportManager.instance().insertData("sdk.oversea.ping.start", "");

                    for (String ip : ipList) {
                        String exec = "ping -c 4 " + ip;
                        Process p = Runtime.getRuntime().exec(exec);
                        BufferedReader buf = new BufferedReader(new InputStreamReader(p.getInputStream()));
                        String extend = "";
                        String str = "";

                        while ((str = buf.readLine()) != null) {
                            if(str.contains("packet loss")) {
                                int i = str.indexOf("received");
                                int j = str.indexOf("%");
                                lost = str.substring(i + 10, j);

                                GlobalData.singleton().NetTimeout().setIpLossRate(ip, Integer.valueOf(lost));
                                extend += "lost_rate=" + lost;

                                str = buf.readLine();
                                if (str.contains("avg")) {
                                    i = str.indexOf("/", 20);
                                    j = str.indexOf(".", i);
                                    delay = str.substring(i + 1, j);
                                    GlobalData.singleton().NetTimeout().setRTT(ip, Integer.valueOf(delay));
                                    extend += "&rtt=" + delay;
                                }
                            }

                            if(!TextUtils.isEmpty(extend)) {
                                CTILog.d(TAG, Thread.currentThread().getName()+" ip=" + ip + "&" + extend);
                            }
                        }


                        buf.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }
}
