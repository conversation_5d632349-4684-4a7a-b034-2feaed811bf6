package com.centauri.oversea.comm;

import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newapi.params.InitParams;
import com.centauri.oversea.newnetwork.http.CTIIPManager;
import com.centauri.oversea.newnetwork.http.CTINetCfg;
import com.centauri.oversea.newnetwork.http.NetTimeoutHelper;

import java.util.HashMap;

/**
 * Data cache global classes
 * Created by zachzeng on 2017/10/20.
 */
public class GlobalData {
    public static final String TAG = "GlobalData";
    public static final String SDK_VERSION = "5.01.061"; // version number

    private int uiLevel = 0;                        // Mini-master UI view switch

    public String IDC = "";
    public String IDCInfo = "";
    public String offerID = "";
    public String payChannel = "";
    public String provideAppID = "";
    public String regionCode = "";
    public String currencyCode = "";
    public String openID = "";
    public String openKey = "";
    public String pf = "";
    public String pfKey = "";
    public String sessionID = "";
    public String sessionType = "";
    public String zoneID = "1";      // Account partition
    public String goodsZoneID = ""; // The game block
    public String userName = "";
    public Boolean inAppMessaging = false;

    public String env = "test";
    public String changeVid = "cpay_4.1.1"; //vid

    public String serverId = "";
    public String roleId = "";

    public String gw_version = "";
    public String preOrderId = "";
    public String unifiedProductId = "";

    public boolean ipMeasureSwitch = true;         // Hide whether IP speed is turned on
    public boolean isSendReport = true;            // Control whether or not to send the report data
    public static boolean isGoogleNew = true;      // Whether it is a new version of Google Play, through background control
    public static boolean useHighestGoogleApi = true;      // Whether it is a new version of Google Play, through background control
    private String sessionToken = "";        // The SDK generates unique values for each request
    private CTINetCfg mNetCfg = null;         // Network configuration class
    private CTIIPManager mIpManager = null;  // IP management class
    private NetTimeoutHelper mTimeoutHelper = null;  // Save the global network length access class

    private boolean useDomainFirst = true; // Set it to default UseDomain
    public long heartBeat = 0;                // When the heartbeat is reported, comm_config is specified in getIp

    private static String mKey = "";


    private static String mIV = "";

    private String currencyInGw = "";

    public static String subscribeSaveCache = "0";//save subscribe cache,1:open,0:close
    public static String subscribeClearCache = "0";//clear subscribe cache,1:open,0:close


    /**
     * The exterior can be released, but the timing of release is determined
     */
//    public void release(){
//        mNetCfg = null;
//        mIpManager = null;
//        mTimeoutHelper = null;
//    }
    private GlobalData() {
        if (null != CTIPayNewAPI.singleton().getApplicationContext()) {
            String useGoogleHighestApi = CTISPTools.getString(CTIPayNewAPI.singleton().getApplicationContext(), "use_highest_google_api");
            if (!TextUtils.isEmpty(useGoogleHighestApi) && TextUtils.equals("false", useGoogleHighestApi.toLowerCase())) {
                CTILog.i(TAG, "useHighestGoogleApi=false");
                useHighestGoogleApi = false;
            }
        }

    }

    private static class InstanceHolder {
        static GlobalData instance = new GlobalData();
    }

    public static GlobalData singleton() {
        return InstanceHolder.instance;
    }


    /**
     * 外部调用初始化
     */
    public void init(InitParams initParams) {
        // read base key

//        System.loadLibrary("unipayutils");
//
//        checkParam(CTIPayNewAPI.singleton().getApplicationContext());
        String temp = "";
        for (int i = 1; i < 17; i++) {
            temp += "" + i % 10;
        }

        CTILog.d("CalculateIV", temp);
        mIV = temp;

        mKey = "" + MConstants.MConstants0 + MConstants.MConstantsAlpha + MConstants.MConstantsBeta;

        temp = MConstants.MConstantsDone + MConstants.MConstantsEmma + MConstants.MConstantsFrank;

        mKey += MConstants.MConstantsCell;

        mKey += temp;

        mKey += MConstants.MConstantsGeo + MConstants.MConstantsHolo + MConstants.MConstantsI;

        mKey += MConstants.MConstantsJoker + MConstants.MConstantsKeith;

        mKey += MConstants.MConstantsLength + MConstants.MConstantsMoon;


        CTILog.d("CalculateIV", mKey);

        loadInitParams(initParams);

        if (mNetCfg == null) {
            mNetCfg = new CTINetCfg();
        }
        mNetCfg.init();

        if (mIpManager == null) {
            mIpManager = new CTIIPManager();
        }
        mIpManager.init(CTIPayNewAPI.singleton().getApplicationContext());

    }

    /**
     * Get network configuration information
     * @return
     */
    public CTINetCfg NetCfg() {
//        if(mNetCfg == null){
//            mNetCfg = new CTINetCfg();
//            mNetCfg.init();
//        }
        return mNetCfg;
    }


    public int getIPListLength() {
        if (null == mNetCfg) {
            return 0;
        } else if (null == mNetCfg.getIPList()) {
            return 0;
        }
        return mNetCfg.getIPList().length;
    }

    public String getHost() {
        if (null == mNetCfg) {
            return "";
        }
        return mNetCfg.getHost();
    }

    public String getReportDomain() {
        if (null == mNetCfg) {
            return "";
        }
        return mNetCfg.getReportDomain();
    }

    public String[] getIPList() {
        if (null == mNetCfg) {
            return new String[0];
        }
        return mNetCfg.getIPList();
    }

    /**
     * Get the IP management class
     * @return
     */
    public CTIIPManager IPManager() {
        if (mIpManager == null) {
            mIpManager = new CTIIPManager();
        }
        return mIpManager;
    }


    /**
     * Obtain network delay information
     * @return
     */
    public NetTimeoutHelper NetTimeout() {
        if (mTimeoutHelper == null) {
            mTimeoutHelper = new NetTimeoutHelper();
        }
        return mTimeoutHelper;
    }

    public String getGw_version() {
        if (null == gw_version) {
            return "";
        }
        return gw_version;
    }
    /**
     * Parse the initialization parameters
     * @param initParams
     */
    private void loadInitParams(InitParams initParams) {
        IDC = initParams.getIDC();
        offerID = initParams.getOfferID();
        provideAppID = initParams.getProvideAppID();
        regionCode = initParams.getRegionCode();
        currencyCode = initParams.getCurrencyCode();
        payChannel = initParams.getPayChannel();
        openID = initParams.getOpenID();
        zoneID = initParams.getZoneID();
        inAppMessaging = initParams.getInAppMessaging();

        InitParams.InitParamsExtra initExtra = initParams.getExtra();
        if (initExtra != null) {
            IDCInfo = initExtra.getIDCInfo();
            openKey = initExtra.getOpenKey();
            pf = initExtra.getPf();
            pfKey = initExtra.getPfKey();
            sessionID = initExtra.getSessionID();
            sessionType = initExtra.getSessionType();
            goodsZoneID = initExtra.getGoodsZoneID();

            if (!TextUtils.isEmpty(initExtra.getChannelExtras())) {
                String channelExtras = initExtra.getChannelExtras();
                HashMap<String, String> channelExtrasMaps = CTITools.str2Map(channelExtras);

                if (channelExtrasMaps.containsKey("serverId") && channelExtrasMaps.containsKey("roleId")) {
                    serverId = channelExtrasMaps.get("serverId");
                    roleId = channelExtrasMaps.get("roleId");
                    CTILog.i(TAG, "Init serverId = " + serverId + "; Init roleid = " + roleId);
                }

            }

        }

        //Support for DEV testing
        String iEnv = initParams.getEnv();
//        if (MConstants.ReleaseEnv.equals(iEnv)
//                || MConstants.DevEnv.equals(iEnv)) {
//            env = iEnv;
//        } else {
//            env = MConstants.TestEnv;
//        }
        env = iEnv;
    }

    /**
     * Refresh network request token
     * token identifies each network request
     */
    public void refreshNetToken() {
        sessionToken = GDPR.getUUID();
    }

    /**
     * Set the token externally
     * @param token
     */
    public void setNetToken(String token) {
        sessionToken = token;
    }

    public void setGw_version(String version) {
        gw_version = version;
    }

    public String getNetToken() {
        return sessionToken;
    }

    /**
     * baseKey
     * @return
     */
    public String getBaseKey() {
        return mKey;
    }

    /**
     * set ui switch
     * @param uiLevel
     */
    public void setMUILevel(@CTIPayAPI.MUILevel int uiLevel) {
        this.uiLevel = uiLevel;
    }

    /**
     * show loading
     * @return
     */
    public boolean showLoading() {
        return (uiLevel & 0x01) == 0;
    }

    /**
     * Whether to display the payment result page
     * @return
     */
    public boolean showPayResult() {
        return (uiLevel & 0x02) == 0;
    }

    /**
     * Whether to take the domain name first
     * @return
     */
    public boolean isUseDomainFirst() {
        return useDomainFirst;
    }

    public void setUseDomainFirst(boolean useDomainFirst) {
        this.useDomainFirst = useDomainFirst;
    }

//    private native String readBaseKey();
//
//    public static native void checkParam(Object obj);

    public static void setKey(String key) {
        mKey = key;
    }

    public static void setIV(String iv) {
        mIV = iv;
    }

    public static String getIV() {
        return mIV;
    }


    public String getCurrencyInGw() {
        return currencyInGw;
    }

    public void setCurrencyInGw(String currencyInGw) {
        this.currencyInGw = currencyInGw;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setIDCAndEnv() {
        IDC = CTISPTools.getString(CTIPayNewAPI.singleton().getApplicationContext(), "initIdc");
        env = CTISPTools.getString(CTIPayNewAPI.singleton().getApplicationContext(), "initEnv");
        if (mNetCfg == null) {
            mNetCfg = new CTINetCfg();
            mNetCfg.init();
        }

        if (mIpManager == null) {
            mIpManager = new CTIIPManager();
        }
    }
}
