package com.centauri.oversea.comm;

import android.content.Context;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.newapi.CTIPayNewAPI;

import java.util.ArrayList;

public class CTIDataReportManager {
    public static final String TAG = "APDataReportManager";

    public static final String SDK_OVERSEA_MAIN_SHOW = "sdk.oversea.main.show";	 // The first page is exposed
    public static final String SDK_OVERSEA_CHANNEL_SHOW = "sdk.oversea.channel.show"; // The channel mall page is exposed
    public static final String SDK_OVERSEA_MAIN_BACK = "sdk.oversea.main.back";	 // Home page returns
    public static final String SDK_OVERSEA_COUNTRY_SELECT = "sdk.oversea.country.select"; // The channel mall page is exposed
    public static final String SDK_OVERSEA_COUNTRY_SHOW = "sdk.oversea.country.show"; // The channel mall page is exposed
    public static final String NETWORK_REQUEST = "sdk.oversea.centauri.networkrequest";
    public static final String PHONE_DEVICE = "sdk.oversea.deviceinfo";

// -- Payment process --
    // Network request response
    public static final String SDK_NETWORK_CALL_RESPONSE = "sdk.oversea.network.call.response";

    // An error was reported in the Google Play SDK
    public static final String SDK_CALL_GW_SDK_INNER_ERROR = "sdk.oversea.call.gw.inner.error";
    public static final String SDK_CALL_GW20_SDK_CALL = "sdk.oversea.call.gw20.sdk.call";
    public static final String SDK_CALL_GW_SDK_CALL = "sdk.oversea.call.gw.sdk.call";
    public static final String SDK_CALL_GW20_SDK_RESPONSE = "sdk.oversea.call.gw20.sdk.response";
    public static final String SDK_CALL_GW_SDK_RESPONSE = "sdk.oversea.call.gw.sdk.response";
    public static final String SDK_CALL_GW_SDK_API_VERSION = "sdk.oversea.call.gw.api.version";

// call API
    public static final String SDK_CENTAURI_API_CALL = "sdk.centauri.api.call";
    public static final String SDK_CENTAURI_API_RESPONSE = "sdk.centauri.api.resp";
    public static final String SDK_OVERSEA_REPROVIDE_INFO = "sdk.oversea.reprovide.info";

// home page
    public static final String SDK_OVERSEA_ENTER = "sdk.oversea.enter";
    public static final String SDK_OVERSEA_EXIT = "sdk.oversea.exit";
    public static final String SDK_OVERSEA_CHANNEL_PAYRESULT = "sdk.oversea.channel.payresult";
    public static final String SDK_OVERSEA_BOKU_SHOW = "sdk.oversea.boku.show";
    public static final String SDK_OVERSEA_BOKU_BACK = "sdk.oversea.boku.back";
    public static final String SDK_OVERSEA_BOKU_CANCEL = "sdk.oversea.boku.cancel";

    //google play
    public static final String SDK_OVERSEA_GW_TIME = "sdk.oversea.gw.time";   //gw时长统计
    public static final String SDK_OVERSEA_GW_RESULT = "sdk.oversea.gw.result";
    public static final String SDK_OVERSEA_GW_REPROVIDE_TIME_CONSUME = "sdk.reprovide.oversea.gw.time.consume";

    public static final String SDK_OVERSEA_GW_CONSUME_RESULT = "sdk.oversea.gw.consume.result";

    // 2.0
    public static final String SDK_OVERSEA_GW_TIME_20 = "sdk.oversea.gw.20.time";   //gw时长统计
    public static final String SDK_OVERSEA_GW_RESULT_20 = "sdk.oversea.gw.20.result";
    public static final String SDK_OVERSEA_GW_REPROVIDE_TIME_CONSUME_20 = "sdk.reprovide.oversea.gw.time.20.consume";

    // Network probe
    public static final String SDK_OVERSEA_IPDETECT_CONNECT_TIME = "sdk.oversea.ip.detect.ip.time";
    public static final String SDK_OVERSEA_IPDETECT_DNS_RESULT = "sdk.oversea.ip.detect.dns.result";
    public static final String SDK_OVERSEA_IPDETECT_ERROR = "sdk.oversea.ip.detect.error";
    public static final String SDK_OVERSEA_SYSTEM_LANGUAGE = "sdk.oversea.sys.lang";

    // merge request network time statistics
    public static final String SDK_COMM_NET_TIME = "sdk.comm.net.time";

    // Report crash problem of eating chicken
    public static final String SDK_CRASH_RESTORE = "sdk.oversea.cash.restore";
    public static final String SDK_RESTORE = "sdk.oversea.restore";

    //fields
    public static final String SDK_FIELD_PRODUCTID = "productid";
    public static final String SDK_FIELD_BILLNO = "billno";

    // Use this to identify where the page has been loaded, because if you only look at the end of the page, the user may not wait for the load to complete, and then cancel
    public static final String SDK_WEBVIEW_LOAD = "sdk.webview.onfinish";
    public static final String SDK_WEBVIEW_END = "sdk.webview.end";
    public static final String SDK_WEBVIEW_ERROR = "sdk.webview.err";

    private ArrayList<ReportItem> reportData = null;    // Report content
    private static final int GROUP_SIZE = 12;          // Report in groups
    private int dataCount = 0;


    private CTIDataReportManager(){
        reportData = new ArrayList<ReportItem>(16);
        loadDataId(CTIPayNewAPI.singleton().getApplicationContext());

    }

    static class InstanceHolder{
        static CTIDataReportManager instance = new CTIDataReportManager();
    }

    //instance
    public static CTIDataReportManager instance(){
        return InstanceHolder.instance;
    }


    private void loadDataId(Context context) {
        if(context != null) {
            dataCount = CTISPTools.getInt(context, "dataCount");
        }
    }

    public void saveDataId(Context context) {
        if(context != null) {
            CTISPTools.putInt(context, "dataCount", dataCount);
        }
    }

    private int getDataId() {
        dataCount++;
        if (dataCount >= 30000) {
            dataCount = 0;
        }
        return dataCount;
    }


    /**
     * Add reported data
     * Synchronization lock to avoid multithread concurrent exceptions
     * @param type
     * @param extend
     */
    public synchronized void insertData(String type,String extend){
        CTILog.d(TAG, "format====" + type);
        CTILog.d(TAG, "extend====" + extend);
        if(!TextUtils.isEmpty(type)){
            ReportItem item = new ReportItem();
            item.format = type;
            if(!TextUtils.isEmpty(extend)){
                item.extend = extend;
            }

            reportData.add(item);
        }
    }

    /**
     * External access to reported data
     * @param LogRecord
     * @return
     */
    public int getLogRecord(ArrayList<String> LogRecord) {
        if(LogRecord == null
                || reportData == null
                || reportData.isEmpty()){
            return 0;
        }

        //Copy to the list to avoid multithreading exceptions
        ArrayList<ReportItem> cacheList = new ArrayList<ReportItem>(reportData.size());
        cacheList.addAll(reportData);
        reportData.clear();

        LogRecord.clear();
        int reportSize = cacheList.size();
        int groupCount = (reportSize%GROUP_SIZE == 0) ?
                reportSize/GROUP_SIZE : reportSize/GROUP_SIZE +1;

        GlobalData sGD = GlobalData.singleton(); //Cache to avoid each fetch
        StringBuilder sb = new StringBuilder();
        CTILog.d(this.getClass().getName(), "sGD.openID="+ sGD.openID);
        String CommData = new StringBuilder()
                .append("|3=").append(sGD.openID)
                .append("|7=0")
                .append("|13=").append(getDataId())
                .append("|24=").append(sGD.offerID)
                .append("|26=").append(sGD.pf)
                .append("|29=").append(sGD.getNetToken())
                .append("|31=androidoversea_v").append(GlobalData.SDK_VERSION)
                .append("|37=").append(sGD.sessionID)
                .append("|43=").append(sGD.sessionType)
                .append("&").toString();
        //Group report
        for(int i=0;i<groupCount;i++){
            int num = 0;
            for(int j=0;j<GROUP_SIZE;j++,num++){
                int index = i*GROUP_SIZE +j;
                if(index >= reportSize){
                    break;
                }

                ReportItem item = cacheList.get(index);
                if(item != null){
                    sb.append("record").append(j).append("=");
                    // Each submission increases the currency type
                    if (!TextUtils.isEmpty(item.extend)) {
                        sb.append("|8=").append(CTITools.urlEncode(item.extend+"&currency="+GlobalData.singleton().getCurrencyInGw(), 3));
                    } else {
                        sb.append("|8=").append(CTITools.urlEncode("currency="+GlobalData.singleton().getCurrencyInGw(), 3));
                    }
                    sb.append("|21=").append(item.format); //Format
                    sb.append("|38=").append(item.times);   //TokenTime

                    //Public data
                    sb.append(CommData);
                }
            }

            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }

            //The num is put here and spliced
            StringBuilder oneRecord = new StringBuilder()
                    .append("num=")
                    .append(num)
                    .append("&")
                    .append(sb.toString());
            CTILog.d(this.getClass().getName(), "report Content="+ sb.toString());
            LogRecord.add(oneRecord.toString());
            sb.setLength(0);
        }

        return groupCount;
    }


    //Report the entry
    static class ReportItem{
        public String format;
        public String times;
        public String extend;

        public ReportItem(){
            times = String.valueOf(System.currentTimeMillis());
        }
    }
}
