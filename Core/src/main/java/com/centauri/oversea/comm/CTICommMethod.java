package com.centauri.oversea.comm;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;


import com.centauri.comm.CTILog;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.util.List;

/**
 *
 */

public class CTICommMethod {
    public static final String TAG = "APCommMethod";

    /**
     * @param str           The format to be converted is similar to [100,30,200,50]
     * @param mpvalueList   Converts the data structure of the List
     * @param mpPrecentList
     */
    public static void transformStrToMpInfoList(String str, List<String> mpvalueList, List<String> mpPrecentList) {

        int start = str.indexOf("[");
        int end = str.indexOf("]");

        // Otherwise the format is wrong
        if (start != -1 && end != -1 && end > start) {

            String result = str.substring(start + 1, end);
            int olen = result.length();
            if (olen == 0) {
                // No marketing information
                mpvalueList.clear();
                mpPrecentList.clear();
            } else {
                String[] strings = result.split(",");
                int len = strings.length;
                // Must be a multiple of 2 and have a marketing campaign
                if (len > 0 && len % 2 == 0) {
                    mpvalueList.clear();
                    mpPrecentList.clear();
                    for (int i = 0; i < len / 2; i++) {
                        String string = strings[i * 2];
                        String pminfo = strings[i * 2 + 1];
                        mpvalueList.add(string);
                        mpPrecentList.add(pminfo);
                    }
                }
            }

        }

    }


    /**
     * @param list
     * @param mplist
     */
    public static void transformStrToList(String list, List<String> mplist) {
        int start = list.indexOf("[");
        int end = list.indexOf("]");
        mplist.clear();
        // Otherwise the format is wrong
        if (start != -1 && end != -1 && end > start) {
            String result = list.substring(start + 1, end);
            int olen = result.length();
            if (olen != 0) {
                String[] strings = result.split(",");
                int len = strings.length;
                for (int i = 0; i < len; i++) {
                    mplist.add(strings[i]);
                }
            }
        }
    }

    public static int getLayoutId(Context context, String layoutId) {
        return context.getResources().getIdentifier(layoutId, "layout", context.getPackageName());
    }


    public static String getStringId(Context context, String stringId) {
        int resId = context.getResources().getIdentifier(stringId, "string", context.getPackageName());
        return context.getResources().getString(resId);
    }


    public static int getDrawableId(Context context, String drawableId) {
        return context.getResources().getIdentifier(drawableId, "drawable", context.getPackageName());
    }


    public static Drawable getDrawable(Context context, String resId) {
        int drawableId = context.getResources().getIdentifier(resId, "drawable", context.getPackageName());
        return context.getResources().getDrawable(drawableId);
    }


    public static int getStyleId(Context context, String styleId) {
        return context.getResources().getIdentifier(styleId, "style", context.getPackageName());
    }


    public static int getDimenID(Context context, String dimenId) {
        return context.getResources().getIdentifier(dimenId, "dimen", context.getPackageName());
    }


    public static int getId(Context context, String Id) {
        return context.getResources().getIdentifier(Id, "id", context.getPackageName());
    }


    public static int getColorId(Context context, String colorId) {
        int id = context.getResources().getIdentifier(colorId, "color", context.getPackageName());
        return context.getResources().getColor(id);
    }


    //After plug-in, animation resources are integrated into the business and the Application Context is needed
    public static int getAnimId(Context context, String animId) {
        return context.getResources().getIdentifier(animId, "anim", context.getApplicationContext().getPackageName());
    }


    // By reflection
    public static int[] getStyleableIntArray(Context sContext, String name) {
        try {
            if (sContext == null)
                return null;
            Field field = Class.forName(sContext.getPackageName() + ".R$styleable").getDeclaredField(name);
            return (int[]) field.get(null);
        } catch (Throwable t) {
            CTILog.e(TAG, "getStyleableIntArray(): " + t.getMessage());
        }
        return null;
    }


    public static int getStyleableIntArrayIndex(Context sContext, String name) {
        try {
            if (sContext == null)
                return 0;
            Field field = Class.forName(sContext.getPackageName() + ".R$styleable").getDeclaredField(name);
            return (Integer) field.get(null);
        } catch (Throwable t) {
            CTILog.e(TAG, "getStyleableIntArrayIndex(): " + t.getMessage());
        }
        return 0;
    }

    //Reflection Creation Instance
    public static Object createObject(String className) {
        try {
            Class<?> channelName = Class.forName(className);
            return channelName.newInstance();
        } catch (Exception e) {
            CTILog.e(TAG, "createReflectObject(): reflect exception.");
        }

        return null;
    }

    public static void sendLocalBroadcast(Context context, String action, int resultCode, String resultMsg) {
        try {
            Class channelName = Class.forName("androidx.localbroadcastmanager.content.LocalBroadcastManager");
            Method m1 = channelName.getMethod("getInstance", Context.class);
            Object instance = (Object) m1.invoke(channelName, context);
            Intent intent = new Intent(action);
            intent.putExtra("resultCode", resultCode);
            intent.putExtra("resultMsg", resultMsg);
            Method m2 = channelName.getDeclaredMethod("sendBroadcast", Intent.class);
            m2.invoke(instance, intent);
        } catch (Exception e) {
            try {
                Class channelName = Class.forName("android.support.v4.content.LocalBroadcastManager");
                Method m1 = channelName.getMethod("getInstance", Context.class);
                Object instance = (Object) m1.invoke(channelName, context);
                Intent intent = new Intent(action);
                intent.putExtra("resultCode", resultCode);
                intent.putExtra("resultMsg", resultMsg);
                Method m2 = channelName.getDeclaredMethod("sendBroadcast", Intent.class);
                m2.invoke(instance, intent);
            } catch (Exception e1) {
                CTILog.e(TAG, "createReflectObject(): reflect exception." + e1.getMessage());

            }

        }
    }

    public static void startDebugActivity(Class s, Context context) {
        try {
            Method method = s.getDeclaredMethod("startActivity", Context.class);
            method.invoke(s.newInstance(), context);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

    }

    /**
     * iisShowErrDialog
     *
     * @param context context
     * @return bool
     */
    public static boolean isConfigShowErrDialog(Context context) {

        ApplicationInfo appInfo = null;
        boolean isShowErrDialog = true;
        try {
            appInfo = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
            isShowErrDialog = appInfo.metaData.getBoolean("isShowErrDialog", true);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        return isShowErrDialog;
    }

    /**
     * isQuerySecondSkuDetails
     *
     * @param context context
     * @return bool
     */
    public static boolean isConfigQuerySecondSkuDetails(Context context) {

        ApplicationInfo appInfo = null;
        boolean isQuerySecondSkuDetails = true;
        try {
            appInfo = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
            isQuerySecondSkuDetails = appInfo.metaData.getBoolean("isQuerySecondSkuDetails", true);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        return isQuerySecondSkuDetails;
    }

    /**
     * isErrCode2InnerCode
     *
     * @param context context
     * @return bool
     */
    public static boolean isErrCode2InnerCode(Context context) {

        ApplicationInfo appInfo = null;
        boolean isErrCode2InnerCode = true;
        try {
            appInfo = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
            isErrCode2InnerCode = appInfo.metaData.getBoolean("isErrCode2InnerCode", true);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return isErrCode2InnerCode;
    }


}
