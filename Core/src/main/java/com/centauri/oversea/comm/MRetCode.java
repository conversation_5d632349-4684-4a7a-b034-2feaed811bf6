package com.centauri.oversea.comm;

/**
 *  sdk ret code
 * <AUTHOR> 2019.1.8
 */
public class MRetCode {

    //SDK external error code
    public static final int OK = 0;
    public static final int ERR_OTHER = -1; //Other errors
    public static final int ERR_CANCEL = -2; //payment cancellation
    public static final int ERR_PARAMETER = -3; //Parameter error
    public static final int ERR_NET = -4; //Network abnormal
    public static final int ERR_ORDER_RISK = -5; //Risk control strategy during order placement
    public static final int ERR_NO_PROVIDE = 6; //provide errors

    //SDK internal error code
    public static final int ERR_LOG_INVALID = 1018; //Login status is invalid
    public static final int ERR_5017 = 5017; //Fake bill
    public static final int ERR_1138 = 1138; //Delivery risk control error code
    public static final int ERR_1145 = 1145; //Order risk control error code
    public static final int ERR_PROP_FORBID = 5020;
    public static final int ERR_ALREADY_PROVIDE = 6001;

    /**Google Play error code: -200x**/
    //Billing response codes
    public static final int ERR_GW_BILLING_USER_CANCEL = -2001; //gw payment cancellation
    public static final int ERR_GW_BILLING_SERVICE_UNAVAILABLE = -2002; //gw service does not support
    public static final int ERR_GW_BILLING_UNAVAILABLE_DEVICE = -2003; //gw device does not support
    public static final int ERR_GW_BILLING_UNAVAILABLE_ACCOUNT = -2004; //gw account does not support
    public static final int ERR_GW_BILLING_ITEM_UNAVAILABLE = -2005;
    public static final int ERR_GW_BILLING_DEVELOPER_ERROR = -2006;
    public static final int ERR_GW_BILLING_RESULT_ERROR = -2007;
    public static final int ERR_GW_BILLING_ITEM_ALREADY_OWNED = -2008;
    public static final int ERR_GW_BILLING_ITEM_NOT_OWNED = -2009;
    public static final int ERR_GW_BILLING_BUNDLE_NULL = -2010;
    // IAB Helper error codes
    public static final int ERR_GW_IABHELPER_BAD_RESPONSE = -2011;
    //Subscription
    public static final int ERR_GW_SUB_OWNED_SAME_USER = -2012; //gw subscription, within the validity period, the same openid is forbidden to purchase
    public static final int ERR_GW_SUB_OWNED_DIFF_USER = -2013; //gw subscription, within the validity period, different openid is forbidden to purchase
    //other
    public static final int ERR_GW_NO_PRODUCT_INFO = -2014; //No item information was found before placing the order

    public static final int ERR_GW_BILLING_SERVICE_TIMEOUT = -2015;
    public static final int ERR_GW_BILLING_SERVICE_DISCONNECTED = -2016;

    public static final int ERR_GW_SUB_UPGRADE_OLD_PURCHASE_TOKEN_NULL = -2017;//gw subscription upgrade, no information about the original token is queried

    public static final int ERR_GW_BILLING_PAY_PENDING = -2018;//Pending payment status

    public static final int ERR_GW_BILLING_PAY_NOTFOUNDBASEPLANID = -2019;//baseplanid or offerid not found

    public static final int ERR_GW_BILLING_PAY_NOTFOUNDPRODUCTID = -2020;//productid not found
    //UnKnow error
    public static final int ERR_GW_UNKNOW = -2000; //gw unknown error

    // The payment is successful and the delivery fails
    public static final int ERR_PAY_SUCC_POST_ERR = -3001;
    public static final int ERR_PAY_SUCC_POST_NET_ERR = -3002;
}
