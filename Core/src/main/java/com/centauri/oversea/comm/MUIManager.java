package com.centauri.oversea.comm;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.CTIPayManager;

/**
 * Payment view management class
 *
 * <AUTHOR>
 */
public class MUIManager {
    public static final String TAG = "MUIManager";

    private Context mContext = null;
    private MAlertDialog mTestEnvDialog = null; //sandbox dialog
    private MAlertDialog mErrorMsgDialog = null;
    private ProgressDialog mProgressDialog = null;  //progress

    public MUIManager(Context context) {
        mContext = context;
    }


    /**
     * Show loading progress bar
     * Cannot be canceled externally
     */
    public void showLoading() {
        if (!isShowLoading()) {
            return;
        }

        if (mProgressDialog != null && mProgressDialog.isShowing()) {
            return;
        }

        mProgressDialog = new CTIProgressDialog(mContext, false);
        if (mProgressDialog != null) {
            mProgressDialog.setMessage(CTICommMethod.getStringId(mContext, "unipay_waiting"));
            mProgressDialog.show();
            CTILog.i(TAG, "showLoading()");
        }
    }


    /**
     * Show loading progress bar
     * Cannot be canceled externally
     */
    public void showLoading(final MNotifier cancelNotifier) {
        if (!isShowLoading()) {
            return;
        }

        if (mProgressDialog != null && mProgressDialog.isShowing()) {
            return;
        }

        mProgressDialog = new CTIProgressDialog(mContext, true);
        if (mProgressDialog != null) {
            mProgressDialog.setMessage(CTICommMethod.getStringId(mContext, "unipay_waiting"));

            mProgressDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
                @Override
                public void onCancel(DialogInterface dialog) {
                    if (cancelNotifier != null) {
                        cancelNotifier.callback();
                    }
                    CTILog.e(TAG, "showWaitDialog() onCancel.");
                }
            });
            mProgressDialog.show();
            CTILog.i(TAG, "showWaitDialog()");
        }
    }


    /**
     * close dialog
     */
    public void dismissWaitDialog() {
        if (mProgressDialog != null && mProgressDialog.isShowing()) {
            mProgressDialog.dismiss();
        }
        mProgressDialog = null;
        CTILog.i(TAG, "dismissWaitDialog()");
    }


    /**
     * show sandbox dialog
     */
    public void showSandboxDialog(final MNotifier sureNotifier, final MNotifier cancelNotifier) {
        if (mTestEnvDialog != null && mTestEnvDialog.isShowing()) {
            return;
        }
        final Object obj = CTICommMethod.createObject("com.centauri.debugview.View.GoogleBillingActivity");

        MAlertDialog.Builder builder = new MAlertDialog.Builder(mContext)
                .setTitle(CTICommMethod.getStringId(mContext, "unipay_hints"))
                .setContent(CTICommMethod.getStringId(mContext, "unipay_no_charge_hints"))
                .setCancelable(false)
                .setDialogButton(CTICommMethod.getStringId(mContext, "unipay_sure"), null == obj ? null : CTICommMethod.getStringId(mContext, "unipay_debug"), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (which == DialogInterface.BUTTON_POSITIVE) {
                            dialog.dismiss();
                            if (sureNotifier != null) {
                                sureNotifier.callback();
                            }
                        } else {
//                            CTIPayManager.instance().clearPurchase(mContext);
                            CTICommMethod.startDebugActivity(obj.getClass(), mContext);
                        }

                    }
                });

        mTestEnvDialog = builder.create();
        if (mTestEnvDialog != null) {
            mTestEnvDialog.setCancelable(false);
            mTestEnvDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                @Override
                public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP) {
                        dialog.dismiss();
                        if (cancelNotifier != null) {
                            cancelNotifier.callback();
                        }
                    }
                    return false;
                }
            });

            mTestEnvDialog.show();
            CTILog.i(TAG, "showSandboxDialog()");
        }
    }


    /**
     * show error
     */
    public void showErrorMsg(String msg, final MNotifier notifier) {
        if (!isShowPayResult()) {
            if (notifier != null) {   //Do not display the view, directly call back the result
                notifier.callback();
            }
            return;
        }

        if (mErrorMsgDialog != null && mErrorMsgDialog.isShowing()) {
            return;
        }

        MAlertDialog.Builder builder = new MAlertDialog.Builder(mContext)
                .setTitle("error")
                .setContent(msg)
                .setCancelable(false)
                .setDialogButton(CTICommMethod.getStringId(mContext, "unipay_sure"),
                        new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                                if (notifier != null) {
                                    notifier.callback();
                                }
                            }
                        });

        mErrorMsgDialog = builder.create();
        if (mErrorMsgDialog != null) {
            mErrorMsgDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                @Override
                public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                    dialog.dismiss();
                    if (notifier != null) {
                        notifier.callback();
                    }
                    return false;
                }
            });

            mErrorMsgDialog.show();
            CTILog.i(TAG, "showErrorMsg()");
        }
    }


    /**
     * Show payment success page
     *
     * @param num
     */
    public void successToast(String num) {
        if (!isShowPayResult()) {
            return;
        }

        View myToast = LayoutInflater.from(mContext).inflate(
                CTICommMethod.getLayoutId(mContext, "unipay_abroad_tips_suc"), null);
        TextView unipay_id_succnum = CTIUICommMethod.findViewById(myToast, "unipay_id_succnum");
        TextView unipay_id_name = CTIUICommMethod.findViewById(myToast, "unipay_id_name");

        if ("0".equals(num) || TextUtils.isEmpty(num)) {
            unipay_id_succnum.setVisibility(View.INVISIBLE);
            unipay_id_name.setVisibility(View.INVISIBLE);
        } else {
            unipay_id_succnum.setText("x" + num);
        }

        Toast toast = Toast.makeText(mContext, "", Toast.LENGTH_LONG);
        toast.setGravity(Gravity.CENTER_VERTICAL | Gravity.FILL_HORIZONTAL, 0, 0);
        toast.setDuration(Toast.LENGTH_LONG);
        toast.setView(myToast);
        toast.show();
    }


    /**
     * clear
     */
    public void release() {
        if (mTestEnvDialog != null && mTestEnvDialog.isShowing()) {
            mTestEnvDialog.dismiss();
        }
        mTestEnvDialog = null;

        if (mErrorMsgDialog != null && mErrorMsgDialog.isShowing()) {
            mErrorMsgDialog.dismiss();
        }
        mErrorMsgDialog = null;

        dismissWaitDialog();
    }


    /**
     * Whether to display the payment result page
     */
    public boolean isShowPayResult() {
        return GlobalData.singleton().showPayResult();
    }

    /**
     * Whether to display the loading progress bar
     */
    public boolean isShowLoading() {
        return GlobalData.singleton().showLoading();
    }

    /**
     * callback
     */
    public interface MNotifier {
        void callback();
    }
}
