package com.centauri.oversea.newapi;

import static com.centauri.oversea.business.pay.ChannelHelper.DecodeByXor;
import static com.centauri.oversea.business.pay.ChannelHelper.EncodeByXor;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ServiceInfo;
//import androidx.core.content.ContextCompat;
//import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.TextUtils;


import com.centauri.comm.CTILog;
import com.centauri.comm.CTILogInfo;
import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.api.ICTINetCallBack;
import com.centauri.oversea.api.ICTICallBack;
import com.centauri.oversea.api.request.CTIGameRequest;
import com.centauri.oversea.business.CTIBaseIntroInfo;
import com.centauri.oversea.business.CTIPayManager;
import com.centauri.oversea.business.IGetProduct;
import com.centauri.oversea.business.pay.CTIResponse;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTISPTools;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GDPR;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MCycleTimer;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.comm.NetWorkChangeReceiver;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.centauri.oversea.newapi.params.InitParams;
import com.centauri.oversea.newapi.params.MallParams;
import com.centauri.oversea.newapi.params.NetParams;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.InfoCallback;
import com.centauri.oversea.newapi.response.NotifyCallback;
import com.centauri.oversea.newnetwork.http.CTIHttpsReport;
import com.centauri.oversea.newnetwork.http.NetworkManager;
import com.centauri.oversea.newnetwork.service.CTINetDetectManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class CTIPayNewAPI {
    public static final String TAG = "CTIPayNewAPI";
    public static final String BGL_ID = "900055685";
    public static final String BGL_SP_NAME = CTITools.getCentauriBgl() + "SdkInfos";
    public static final String R_CRASH_SP_NAME = "RCrashSDKInfo";//pubg bug upload key
    public static final String CRASHS_ID = "cae502e5c3";

    public static final String RE_PROVIDE_ACTION = "com.centauri.oversea.REPROVIDE_UPDATED";
    public static final String NET_DETECT_ACTION = "com.centauri.oversea.newnetwork.service.APNetDetectService";

    private Context applicationContext;
    private long lastClickTime = 0;

    private boolean hasInit = false;         //The init interface must be called first
    private NetWorkChangeReceiver networkChangeReceiver;

    private boolean isReprovideTimerOn = true;//Payment failed, whether to reissue regularly, the default is true
    public boolean logEnable = false;

    //日志组件实例
    private CTILogInfo logInfo = new CTILogInfo();

    private CTIPayNewAPI() {
    }

    static class InstanceHolder {
        static CTIPayNewAPI instance = new CTIPayNewAPI();
    }

    public static CTIPayNewAPI singleton() {
        return InstanceHolder.instance;
    }


    public void showCentauriUI(@CTIPayAPI.MUILevel int level) {
        GlobalData.singleton().setMUILevel(level);
    }

    public void setLogEnable(boolean logEnable) {
        if (CTIPayNewAPI.singleton().applicationContext != null) {
            CTILogInfo logInfo = CTILog.getLogInfo();
            logInfo.setLogEnable(logEnable);
            CTILog.init(logInfo);
        } else {
            this.logEnable = logEnable;
        }
    }

    public boolean isLogEnable() {
        CTILogInfo logInfo = CTILog.getLogInfo();
        return logInfo.isLogEnable();
    }

    public String getEnv() {
        return GlobalData.singleton().env;
    }

    public String getReleaseIDC() {
        return GlobalData.singleton().IDC;
    }

    public Context getApplicationContext() {
        return applicationContext;
    }

    public void setApplicationContext(Context context) {
        this.applicationContext = context;
    }

    /**
     * Initialize the interface, please call the excuse after logging in
     *
     * @param activity   incoming activity from outside
     * @param initParams initialization parameters
     */
    public void init(Activity activity, InitParams initParams) {
        hasInit = true;
        applicationContext = activity.getApplicationContext();
        // Initialize the log module
        initLogModule(applicationContext);
        //1. Read external configuration
        loadOutConfig();
        //2. Initialize internal data
        CTIPayManager.instance().init();
        GlobalData.singleton().init(initParams);
        //3. Check whether getKey/getIp is required, and enable network detection
        checkGetIPAndGetKey(initParams);
        //4. Asynchronous report
        initCfgAsync(activity);
        //5. Register the broadcast listener
        registerReceiver();

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=init"); //Count the number of init interface calls
        reportData("init");
    }


    /**
     * Payment interface, call this interface to initiate payment
     *
     * @param activity passed in from outside, released after payment is completed
     * @param request
     * @param callback
     */
    public void pay(Activity activity, BillingFlowParams request, final ICTICallBack callback) {
        checkFlagStart();

        if (isFastClick()) {
            CTILog.i(TAG, "fast click");
            return;
        }

        //Network not connected
        if (!CTITools.isNetworkAvailable(activity)) {
            CTITools.setNetwork(activity);
            CTIResponse responseInfo = new CTIResponse(
                    MRetCode.ERR_NET, "",
                    "Network not connected."
            );
            callback.CentauriPayCallBack(responseInfo);
            return;
        }

        //Encapsulate a layer of callback to check whether the payment is successful
        ICTICallBack wrapperCallback = new ICTICallBack() {
            @Override
            public void CentauriPayNeedLogin() {
                callback.CentauriPayNeedLogin();
            }

            @Override
            public void CentauriPayCallBack(CTIResponse response) {
                callback.CentauriPayCallBack(response);
                //Payment failed, start regular reissue
                if (response.getResultCode() != MRetCode.OK && isReprovideTimerOn) {
                    startTimerReProvide();
                }
                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE,
                        "name=pay&result=" + response.getResultCode() + "&msg=" + response.getResultMsg());
                reportData("pay");
            }
        };

        applicationContext = activity.getApplicationContext();
        MTimer.start(MTimer.GW_FIRST_SCREEN_SHOWDIALOG);
        CTIPayManager.instance().pay(activity, request, wrapperCallback);
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL,
                "name=pay&" + CTIDataReportManager.SDK_FIELD_PRODUCTID + "=" + request.getProductID()); //Count the number of calls to the pay interface
    }


    /**
     * Replenishment delivery interface, call this interface to follow the replenishment delivery process
     *
     * @param callback
     */
    public void reProvide(final ICTICallback callback) {
        checkFlagStart();
        CTIPayManager.instance().reProvide(new ICTICallback() {
            @Override
            public void callback(int retCode, String info) {
                callback.callback(retCode, info);
                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=reprovide&result=" + retCode + "&info=" + info);
                reportData("reprovide");
            }
        });
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=reprovide"); //Count the number of calls to the reProvide interface
    }


    /**
     * Get marketing information
     *
     * @param request
     * @param callback
     */
    public void net(NetParams request, final ICTINetCallBack callback) {
        checkFlagStart();

        String reqType = request.getMpReqType();
        if (NetParams.GET_SHORT_OPENID.equals(reqType)) { //Get short code
            NetworkManager.singleton().startSecInfo(reqType, request, new ICTINetCallBack() {
                @Override
                public void CentauriNetError(String reqType, int resultCode, String resultMsg) {
                    callback.CentauriNetError(reqType, resultCode, resultMsg);
                    reportData("net");
                }

                @Override
                public void CentauriNetStop(String reqType) {
                    callback.CentauriNetStop(reqType);
                    reportData("net");
                }

                @Override
                public void CentauriNetFinish(String reqType, String result) {
                    callback.CentauriNetFinish(reqType, result);
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=net&reqType=" + reqType + "&result=" + result);
                    reportData("net");
                }


            });
        } else {  //Pull marketing information
            NetworkManager.singleton().net(reqType, request, new ICTINetCallBack() {
                @Override
                public void CentauriNetError(String reqType, int resultCode, String resultMsg) {
                    callback.CentauriNetError(reqType, resultCode, resultMsg);
                    reportData("net");
                }

                @Override
                public void CentauriNetStop(String reqType) {
                    callback.CentauriNetStop(reqType);
                    reportData("net");
                }

                @Override
                public void CentauriNetFinish(String reqType, String result) {
                    callback.CentauriNetFinish(reqType, result);
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=net&result=" + result);
                    reportData("net");
                }


            });
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=net"); //统计调用接口次数
    }


    /**
     * enter mall
     *
     * @param activity
     * @param params
     * @param callback
     */
    public void mall(Activity activity, MallParams params, final ICTICallBack callback) {
        checkFlagStart();

        applicationContext = activity.getApplicationContext();
        String className = "com.centauri.oversea.mall.CTIMall";
        try {
            Class clazz = Class.forName(className);
            Method instance = clazz.getMethod("singleton");
            Object obj = instance.invoke(null);

            Method mall = clazz.getMethod("mall", Activity.class, MallParams.class, ICTICallBack.class);
            mall.invoke(obj, activity, params, new ICTICallBack() {
                @Override
                public void CentauriPayNeedLogin() {
                    callback.CentauriPayNeedLogin();
                }

                @Override
                public void CentauriPayCallBack(CTIResponse response) {
                    callback.CentauriPayCallBack(response);
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=mall&resultMsg=" + response.getResultMsg() + "&result=" + response.getResultCode());
                    reportData("mall");
                }
            });
        } catch (Exception e) {
            CTILog.e(TAG, "mall exception: " + e.getMessage());
            e.printStackTrace();
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=mall"); //Count the number of calls to the mall interface
    }

    /**
     * Query google play item information
     *
     * @param channel      channel value, currently only supports gwallet, os_stove
     * @param productIdMap The list of item ids to be queried
     * @param callback
     */
    public synchronized void getProductInfo(String channel, HashMap<String, String> productIdMap, final InfoCallback callback) {
        checkFlagStart();

        IGetProduct getProduct = CTIPayManager.instance().channelHelper().createProductInfo(channel);
        if (getProduct != null) {
            getProduct.getProductInfo(applicationContext, productIdMap, new InfoCallback() {
                @Override
                public void callback(String resp) {
                    callback.callback(resp);
                    JSONObject obj;
                    int ret = -1;
                    try {
                        obj = new JSONObject(resp);
                        ret = obj.getInt("ret");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=getproductinfo&resp=" + resp + "&result=" + ret);
                    reportData("getProductInfo");
                }
            });
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=getproductinfo"); //Count the number of calls to the getProductInfo interface
    }

    /**
     * Query google play item information - new
     *
     * @param channel      channel value, currently only supports gwallet, os_stove
     * @param productIdMap The list of item ids to be queried
     * @param callback
     */
    public synchronized void getProductInfo(String channel, HashMap<String, String> productIdMap, String resultData, final InfoCallback callback) {
        checkFlagStart();

        IGetProduct getProduct = CTIPayManager.instance().channelHelper().createProductInfo(channel);
        if (getProduct != null) {
            getProduct.getProductInfo(applicationContext, productIdMap, resultData, new InfoCallback() {
                @Override
                public void callback(String resp) {
                    callback.callback(resp);
                    JSONObject obj;
                    int ret = -1;
                    try {
                        obj = new JSONObject(resp);
                        ret = obj.getInt("retCode");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=getproductinfo&resp=" + resp + "&result=" + ret);
                    reportData("getProductInfo");
                }
            });
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=getproductinfo"); //Count the number of calls to the getProductInfo interface
    }

    /**
     * Query ToyPay item information
     *
     * @param channel      channel value, currently only supports toyPay
     * @param productIdMap The list of item ids to be queried
     * @param callback callback
     */
    public synchronized void getProductInfo(Activity activity,String channel, HashMap<String, String> productIdMap, final InfoCallback callback) {
        checkFlagStart();

        IGetProduct getProduct = CTIPayManager.instance().channelHelper().createProductInfo(channel);
        if (getProduct != null) {
            getProduct.getProductInfo(activity, productIdMap, new InfoCallback() {
                @Override
                public void callback(String resp) {
                    callback.callback(resp);
                    JSONObject obj;
                    int ret = -1;
                    try {
                        obj = new JSONObject(resp);
                        ret = obj.getInt("ret");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=getproductinfo&resp=" + resp + "&result=" + ret);
                    reportData("getProductInfo");
                }
            });
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=getproductinfo"); //Count the number of calls to the getProductInfo interface
    }


    /**
     * only for garena channel
     *
     * @param activity
     * @param gameRequest
     * @param callback
     */
    public void getProductInfo(Activity activity, CTIGameRequest gameRequest, InfoCallback callback) {
        checkFlagStart();

        IGetProduct getProduct = CTIPayManager.instance().channelHelper().createProductInfo(gameRequest.getPayChannel());
        if (getProduct != null) {
            if (!TextUtils.isEmpty(gameRequest.getPayChannel()) && gameRequest.getPayChannel().contains("garena")) {
                getProduct.getProductInfo(activity, gameRequest, callback);
            }
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "getproductinfo"); //统计调用getProductInfo接口次数
    }


    /**
     * Check discount price information, such as: gp entry price information
     *
     * @param channel
     * @param productIdMap
     * @param callback
     */
    public void getIntroPriceInfo(String channel, HashMap<String, String> productIdMap, final InfoCallback callback) {
        checkFlagStart();

        CTIBaseIntroInfo getProductIntro = CTIPayManager.instance().channelHelper().createIntroInfoChannel(channel);
        if (getProductIntro != null) {
            getProductIntro.getIntroInfo(applicationContext, channel, productIdMap, new InfoCallback() {
                @Override
                public void callback(String resp) {
                    callback.callback(resp);
                    JSONObject obj;
                    int ret = -1;
                    try {
                        obj = new JSONObject(resp);
                        ret = obj.getInt("ret");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_RESPONSE, "name=getIntroPriceInfo&resp=" + resp + "&result=" + ret);
                    reportData("getIntroPriceInfo");
                }
            });
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=getintropriceinfo"); //Count the number of calls to the getProductInfo interface
    }

    /**
     * Payment exit interface, paired with init interface to call
     * If you need to update the information, register for the broadcast
     */
    public void dispose() {
        hasInit = false;
        CTIPayManager.instance().release();

        if (networkChangeReceiver != null) {
            applicationContext.unregisterReceiver(networkChangeReceiver);
            networkChangeReceiver = null;
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_CENTAURI_API_CALL, "name=dispose"); //Count the number of calls to the getProductInfo interface
    }


    /****************************************Internal interface*********************************************/

    private void startTimerReProvide() {
        CTILog.d(TAG, "startTimerReProvide.");
        MCycleTimer cycleTimer = new MCycleTimer.Builder()
                .setCount(2)        //max count
                .setPeriod(30 * 1000) //30s
                .setUpdateNotifier(new MCycleTimer.CycleTimerUpdateNotifier() {
                    @Override
                    public void onUpdate() {
                        reProvide(new ICTICallback() {
                            @Override
                            public void callback(int resultCode, String resultMsg) {
                                CTILog.d(TAG, "startTimerReProvide callback,retCode: " + resultCode + ",info: " + resultMsg);
                                if (applicationContext != null) {

                                    CTICommMethod.sendLocalBroadcast(applicationContext, RE_PROVIDE_ACTION, resultCode, resultMsg);
//                                    Intent intent = new Intent(RE_PROVIDE_ACTION);
//                                    intent.putExtra("resultCode",resultCode);
//                                    intent.putExtra("resultMsg",resultMsg);
//
//                                    LocalBroadcastManager.getInstance(applicationContext)
//                                            .sendBroadcast(intent);
                                }
                            }
                        });
                    }
                })
                .build();
        cycleTimer.start();
    }

    // Start network detection
    private void startIPDetectService(InitParams params) {
        try {
            // First check if there is registered IPdetectService, if not, you do not need to detect
            PackageManager manager = getApplicationContext().getPackageManager();
            if (manager == null) {
                return;
            }
            PackageInfo packageInfo = manager
                    .getPackageInfo(getApplicationContext().getPackageName(), PackageManager.GET_SERVICES);
            if (packageInfo == null || packageInfo.services == null || packageInfo.services.length == 0) {
                // no need to detect
                CTILog.d(TAG, "no need to detect");
                return;
            } else {
                for (ServiceInfo serviceInfo : packageInfo.services) {
                    if (NET_DETECT_ACTION.equals(serviceInfo.name)) {
                        //have registered ,start service
                        CTILog.d(TAG, "service registered");
                        CTINetDetectManager.startService(getApplicationContext(), params);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //check param
    private void checkFlagStart() {
        if (!hasInit) {
            throw new IllegalArgumentException("You must call init() api first !!!");
        }
    }

    //When initializing, check whether get_ip or get_key is required
    private boolean checkGetIPAndGetKey(final InitParams request) {
        boolean needGetIP = GlobalData.singleton().IPManager().isIPOutdated();
        boolean needChangeKey = NetworkManager.singleton()
                .needChangeKey(request.getOfferID(), request.getOpenID());
        CTILog.i(TAG, "needGetIP: " + needGetIP + "; needChangeKey: " + needChangeKey);

        if (needGetIP || needChangeKey) {
            NetworkManager.singleton().initReq(request, new NotifyCallback() {
                @Override
                public void onFinish() {
                    CTILog.i(TAG, "Init get_ip or get_key finished.");

                    //Do it after get_ip, you need to get the field issued by get_ip
                    startIPDetectService(request);
                }
            });
        } else {
            startIPDetectService(request);
        }
        return needGetIP || needChangeKey;
    }


    //Is it a quick click
    private boolean isFastClick() {
        boolean res = false;
        long curTime = System.currentTimeMillis();
        if (curTime - lastClickTime < 800) {
            res = true;
        }
        lastClickTime = curTime;
        return res;
    }


    //Register ping broadcast
    private void registerReceiver() {
//        if(PackageManager.PERMISSION_GRANTED ==
//                ContextCompat.checkSelfPermission(applicationContext, Manifest.permission.ACCESS_NETWORK_STATE)) {
//            IntentFilter filter = new IntentFilter();
//            filter.addAction(NetWorkChangeReceiver.NETWORK_CHANGE_ACTION);
//            networkChangeReceiver = new NetWorkChangeReceiver();
//            applicationContext.registerReceiver(networkChangeReceiver, filter);
//        } else {
        NetWorkChangeReceiver.pingReport();
//        }
    }

    // Initialize the log module
    private void initLogModule(Context contenxt) {
        try {
            logInfo.setContext(contenxt);
            logInfo.setLogTag("CentauriPay");
            logInfo.setLogEnable(this.logEnable);
            logInfo.setAutoFlush(false);
            CTILog.init(logInfo);
        } catch (Exception e) {

        }
    }


    /**
     * Read external configuration
     */
    private void loadOutConfig() {
        if (applicationContext != null) {
            try {
                ApplicationInfo appInfo = applicationContext.getPackageManager()
                        .getApplicationInfo(applicationContext.getPackageName(), PackageManager.GET_META_DATA);
                if (appInfo != null && appInfo.metaData != null) {
                    //Whether to set the GDPR collection switch externally, the default collection
                    String myKey = "is" + CTITools.getCentuariMds() + "GdprOn";
                    GDPR.ifCollect = !appInfo.metaData.getBoolean(myKey, false);
                    //Whether to set the external payment failure timing reissue switch, the default is true
                    isReprovideTimerOn = appInfo.metaData.getBoolean("isReprovideTimerOn", true);
                    CTILog.d(TAG, "isCentuariGdprOn: " + !GDPR.ifCollect + ",isReprovideTimerOn: " + isReprovideTimerOn);
                }
            } catch (Exception e) {
                CTILog.i(TAG, "loadOutConfig() exception: " + e.getMessage());
            }
        }
    }

    /**
     * Initialize configuration information to avoid getting it every time
     * Asynchronously to avoid blocking the main thread
     */
    private void initCfgAsync(final Activity activity) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    initCfg(activity);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }


    private void initCfg(Activity activity) {
        CTISPTools.putString(applicationContext, BGL_SP_NAME, BGL_ID, GlobalData.SDK_VERSION);
        CTISPTools.putString(applicationContext, R_CRASH_SP_NAME, BGL_ID, GlobalData.SDK_VERSION);
        String cString = EncodeByXor("Cras","encodeKey");
        String hString = EncodeByXor("hSigh","encodeKey");
        String tString = EncodeByXor("tSdkInfos","encodeKey");
        CTISPTools.putString(applicationContext, DecodeByXor(cString + hString + tString,"encodeKey"), CRASHS_ID, GlobalData.SDK_VERSION);
        //Risk control report when logging in
        initReport(activity);
    }


    //Risk control report when logging in
    private void initReport(Context context) {

        //Map sorted by key from small to large, sorting is used to calculate sign
        Map<String, String> content = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String lhs, String rhs) {
                return lhs.compareTo(rhs);
            }
        });

        //Fixed content
        content.put("sys_id", "3_7");
        content.put("cmd", "ReportData");
        content.put("req_src", "1");
        content.put("uin_type", "game");
        content.put("scene", "login");
        content.put("device_os", "android");
        content.put("action", "login");

        //Get directly from the cache
        content.put("offer_id", GlobalData.singleton().offerID);
        content.put("uin", GlobalData.singleton().openID);
        content.put("sdk_version", GlobalData.SDK_VERSION);

        //GDPR data: device information
        if (GDPR.ifCollect) {
            content.put("device_guid", GDPR.getDeviceGuid(context));
            content.put("device_type", GDPR.getDeviceType());
            content.put("wifi_ssid", GDPR.getWifiSSID(context));
            content.put("device_name", GDPR.getDeviceName());
            content.put("network_type", String.valueOf(CTITools.getNetWorkType(context)));
            content.put("sys_version", GDPR.getSysVersion());
            content.put("manufacturer", GDPR.getDeviceManufacturer());
            content.put("device", GDPR.getDevice());
            content.put("showModel", GDPR.getDevice());
            String userIp = GDPR.getLocalIp();
            if (!TextUtils.isEmpty(userIp)) {
                content.put("user_ip", userIp);
            }
        }

        //Timestamp
        content.put("tran_time", String.valueOf(System.currentTimeMillis()));

        //content to string
        StringBuilder contentString = new StringBuilder(CTITools.map2UrlParams(content));
        StringBuilder reportData = new StringBuilder(contentString);

        //Perform 5 consecutive SHA1 signatures on content and get sign
        String sign = "";
        for (int i = 0; i < 5; i++) {
            sign = CTITools.signString(contentString.toString(), "SHA1");
            contentString.append(sign); //After each signing, add the signature to the original text
        }
        reportData.append("&").append("sign=").append(sign);

        new CTIHttpsReport().report(reportData.toString());
    }


    private void reportData(final String method) {

        NetworkManager.singleton().dataReport(new ICTIHttpCallback() {
            @Override
            public void onSuccess(CTIHttpAns ctiHttpAns) {
                // do nothing
                CTILog.d(method, "finalDataReport succ");
            }

            @Override
            public void onFailure(CTIHttpAns ctiHttpAns) {
                CTILog.e(method, "finalDataReport failed");
            }

            @Override
            public void onStop(CTIHttpAns ctiHttpAns) {
                CTILog.w(method, "finalDataReport stoped");
            }
        });
    }
}
