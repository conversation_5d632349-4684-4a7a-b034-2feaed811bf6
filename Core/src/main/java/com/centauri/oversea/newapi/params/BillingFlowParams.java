package com.centauri.oversea.newapi.params;


import android.text.TextUtils;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.HashMap;

/**
 * 支付请求基类
 *
 * <AUTHOR>
 */
public class BillingFlowParams {
    //	购买类型
    public static final String TYPE_GOODS = "bg";
    public static final String TYPE_GAME = "save";
    public static final String TYPE_MONTH = "month";
    public static final String TYPE_UNION_MONTH = "unimonth";


//    @StringDef({TYPE_GOODS,TYPE_GAME,TYPE_UNION_MONTH})
    @Retention(RetentionPolicy.CLASS)
    @interface Type{}


    /**
     * Type field
     * The distinction is the purchase of game currency, monthly subscription, and props
     */
    private String mType = "save";

    /**
     * Currency type
     */
    private String currencyType = "";

    /**
     * Country type
     */
    private String country = "";

    /**
     * Payment channel name
     */
    private String payChannel;

    /**
     * Item ID
     */
    private String productID;

    /**
     * sub offerToken
     */
    private String offerToken = "";

    /**
     * sub basePlanId
     */
    private String basePlanId = "";

    /**
     * sub gw_offerId
     */
    private String gw_offerId = "";

    /**
     * sub selectedOfferIndex
     */
    private int selectedOfferIndex = 0;
    /**
     * Monthly subscription
     * Business code
     */
    private String serviceCode = "";

    /**
     * Monthly subscription
     * The business name will be displayed on the purchase page
     */
    private String serviceName = "";

    /**
     * Monthly subscription
     * Whether to renew automatically, it will take effect when the quantity cannot be changed
     */
    private boolean isAutoPay = false;

    /**
     * Extension field object
     * Set when business needs
     */
    private BillingFlowParamsExtra extra = null;


    private BillingFlowParams(){}

    public String getType() {
        return mType;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public String getCountry() {
        return country;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public String getProductID() {
        return productID;
    }

    public String getOfferToken() { return offerToken; }

    public String getBasePlanId() { return basePlanId; }

    public String getGw_offerId() { return gw_offerId; }

    public int getSelectedOfferIndex() { return selectedOfferIndex; }

    public String getServiceCode() {
        return serviceCode;
    }

    public String getServiceName() {
        return serviceName;
    }

    public boolean isAutoPay() {
        return isAutoPay;
    }

    public BillingFlowParamsExtra getExtra(){
        if(extra == null){
            extra = new BillingFlowParamsExtra();
        }
        return extra;
    }


    //parm to map
    public HashMap<String,String> toMap() {
        HashMap<String,String> res = new HashMap<String, String>();

        if (!TextUtils.isEmpty(mType)) {
            res.put("type", mType);
        }
        if (!TextUtils.isEmpty(currencyType)) {
            res.put("currencyType", currencyType);
        }
        if (!TextUtils.isEmpty(country)) {
            res.put("country", country);
        }
        if (!TextUtils.isEmpty(payChannel)) {
            res.put("payChannel", payChannel);
        }
        if (!TextUtils.isEmpty(productID)) {
            res.put("productId", productID);
        }
        if (!TextUtils.isEmpty(offerToken)) {
            res.put("offerToken", offerToken);
        }
        if (!TextUtils.isEmpty(basePlanId)) {
            res.put("basePlanId", basePlanId);
        }
        if (!TextUtils.isEmpty(gw_offerId)) {
            res.put("gw_offerId", gw_offerId);
        }
        if (!TextUtils.isEmpty(String.valueOf(selectedOfferIndex))) {
            res.put("selectedOfferIndex", String.valueOf(selectedOfferIndex));
        }
        if (!TextUtils.isEmpty(serviceCode)) {
            res.put("serviceCode", serviceCode);
        }
        if (!TextUtils.isEmpty(serviceName)) {
            res.put("serviceName", serviceName);
        }
        if (!TextUtils.isEmpty(extra.channelExtras)) {
            res.put("channelExtras", extra.channelExtras);
        }
        if (!TextUtils.isEmpty(extra.drmInfo)) {
            res.put("drmInfo", extra.drmInfo);
        }
        if (!TextUtils.isEmpty(extra.goodsZoneId)) {
            res.put("goodsZoneId", extra.goodsZoneId);
        }

        return res;
    }

    /**********************************--Builder--***************************************/

    public static class Builder {
        private BillingFlowParams params = new BillingFlowParams();

        /**
         * Sets the purchase type.
         * @param type the type of the goods
         * @return The builder.
         */
        public Builder setType(@Type String type) {
            params.mType = type;
            return this;
        }

        /**
         * Sets the currencyType
         * @param currencyType the currencyType
         * @return The builder.
         */
        public Builder setCurrencyType(String currencyType) {
            params.currencyType = currencyType;
            return this;
        }

        /**
         * Sets the country
         * @param country the country
         * @return The builder.
         */
        public Builder setCountry(String country) {
            params.country = country;
            return this;
        }

        /**
         * Sets the payChannel
         * @param payChannel the payChannel
         * @return The builder.
         */
        public Builder setPayChannel(String payChannel) {
            params.payChannel = payChannel;
            return this;
        }

        /**
         * Sets the productID
         *
         * @param productID the productID
         * @return The builder.
         */
        public Builder setProductID(String productID) {
            params.productID = productID;
            return this;
        }

        /**
         * Sets the offerToken
         *
         * @param offerToken the offerToken
         * @return The builder.
         */
        public Builder setOfferToken(String offerToken) {
            params.offerToken = offerToken;
            return this;
        }

        public Builder setBasePlanId(String basePlanId) {
            params.basePlanId = basePlanId;
            return this;
        }

        public Builder setGwOfferId(String gw_offerId) {
            params.gw_offerId = gw_offerId;
            return this;
        }
        /**
         * Sets the selectedOfferIndex
         *
         * @param selectedOfferIndex the selectedOfferIndex
         * @return The builder.
         */
        public Builder setSelectedOfferIndex(int selectedOfferIndex) {
            params.selectedOfferIndex = selectedOfferIndex;
            return this;
        }
        /**
         * Monthly subscription service (isAutoPay=true), this interface needs to be set
         * @param serviceCode the serviceCode
         * @return The builder.
         */
        public Builder setServiceCode(String serviceCode) {
            params.serviceCode = serviceCode;
            return this;
        }

        /**
         * Monthly subscription service (isAutoPay=true), this interface needs to be set
         * @param serviceName the serviceName
         * @return The builder.
         */
        public Builder setServiceName(String serviceName) {
            params.serviceName = serviceName;
            return this;
        }

        /**
         * Is it a monthly subscription?
         * @param isAutoPay true: monthly subscription
         * @return The builder.
         */
        public Builder setIsAutoPay(boolean isAutoPay) {
            params.isAutoPay = isAutoPay;
            return this;
        }

        /**
         * Set the extended field object, set it when needed externally
         * @param extra
         * @return
         */
        public Builder setExtra(BillingFlowParamsExtra extra){
            params.extra = extra;
            return this;
        }

        /**
         * Returns the {@link BillingFlowParams}.
         * @return the {@link BillingFlowParams}.
         */
        public BillingFlowParams build() {
            return params;
        }
    }




    /*******************************--BillingFlowParamsExtra--*************************************/
    /**
     * Payment extension field object
     * Contains: default value fields, marketing information, transparent transmission fields, and externally transmitted extended information
     */
    public static class BillingFlowParamsExtra{
        /**
         * Application pass-through field
         * When the external business is passed in, after the payment is completed, the Mi Master SDK will transparently transmit the field
         */
        private String appExtends = ""; //request.reserv

        /**
         * Extended field, external business is transferred to the backstage of Mi Master
         */
        private String channelExtras = ""; //request.extras

        /**
         * Types of marketing activities
         */
        private String drmInfo = "";

        /**
         * GoodsZoneId
         * The cell may be switched after the game is initialized
         */
        private String goodsZoneId = "";

        /**
         * Set the channel transparent transmission field
         * @param channelExtras Channel transparent transmission field. Mainly used for channel access. The front-end SDK and the back-end channel of Master Mi need this transparent transmission field.
         */
        public void setChannelExtras(String channelExtras) {
            this.channelExtras = channelExtras;
        }

        /**
         * Set service transparent transmission field
         * @param appExtends transparent transmission field, the external business is passed in, after the payment is completed, the Mi Master SDK will return this field
         */
        public void setAppExtends(String appExtends) {
            this.appExtends = appExtends;
        }

        /**
         * Sets the drmInfo
         * @param drmInfo the drmInfo
         */
        public void setDrmInfo(String drmInfo) {
            this.drmInfo = drmInfo;
        }

        /**
         * Set GoodsZoneId
         * @param goodsZoneId
         */
        public void setGoodsZoneId(String goodsZoneId){
            this.goodsZoneId = goodsZoneId;
        }

        public String getChannelExtras() {
            return channelExtras;
        }

        public String getAppExtends() {
            return appExtends;
        }

        public String getDrmInfo() {
            return drmInfo;
        }

        public String getGoodsZoneId(){
            return goodsZoneId;
        }
    }

}
