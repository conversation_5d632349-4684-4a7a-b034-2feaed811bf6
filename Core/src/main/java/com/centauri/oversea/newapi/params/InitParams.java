package com.centauri.oversea.newapi.params;
import android.text.TextUtils;
import com.centauri.comm.CTILog;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/1/18.
 */

public class InitParams {
    public static final String TAG = "InitParams";

    public static final String IDC_LOCAL = "local";
    public static final String IDC_HONGKONG = "hongkong";
    public static final String IDC_CANADA = "canada";

    private String idc;
    private String env;
    private String offerID;

    private String provideAppID;
    private String regionCode;
    private String currencyCode;
    private String payChannel;
    private String openID;
    private String zoneID = "1"; //Account zone ID
    private Boolean inAppMessaging = false;

    private InitParamsExtra extra = null; //Extended parameters: default fields

    private InitParams(){}

    public InitParamsExtra getExtra(){
        if(extra == null){
            extra = new InitParamsExtra();
        }
        return extra;
    }

    public String getIDC(){
        return idc;
    }

    public String getEnv(){
        return env;
    }

    public String getOfferID(){
        return offerID;
    }

    public String getProvideAppID(){
        return provideAppID;
    }

    public String getRegionCode(){
        return regionCode;
    }

    public String getCurrencyCode(){
        return currencyCode;
    }

    public String getPayChannel(){
        return payChannel;
    }

    public String getOpenID(){
        return openID;
    }

    public String getZoneID(){
        return zoneID;
    }

    public Boolean getInAppMessaging() { return inAppMessaging; }

    //check param
    private boolean checkParams(){
//        if (TextUtils.isEmpty(idc)) {// Parameter error: idc cannot be empty
//            CTILog.e(TAG, "IDC is empty,please set it !!");
//            return false;
//        }

        if (TextUtils.isEmpty(offerID)) {// Parameter error: offerID cannot be empty
            CTILog.e(TAG, "offerID is empty,please set it !!");
            return false;
        }

        if (TextUtils.isEmpty(openID)) {// Parameter error: openID cannot be empty
            CTILog.e(TAG, "openID is empty,please set it !!");
            return false;
        }

        if (TextUtils.isEmpty(zoneID)) {// Parameter error: zoneID cannot be empty
            CTILog.e(TAG, "zoneID is empty,please set it !!");
            return false;
        }
        return true;
    }


    /**********************************--Builder--***************************************/

    public static class Builder{
        private InitParams mParams = new InitParams();


        /**
         * Sett environment
         * @param env
         */
        public Builder setEnv(String env){
            mParams.env = env;
            return this;
        }

        /**
         * set idc
         * @param IDC
         */
        public Builder setIDC(String IDC){
            mParams.idc = IDC;
            return this;
        }

        /**
         * set offerID
         * @param offerID
         */
        public Builder setOfferID(String offerID){
            mParams.offerID = offerID;
            return this;
        }

        public Builder setProvideAppID(String provideAppID){
            mParams.provideAppID = provideAppID;
            return this;
        }

        public Builder setRegionCode(String regionCode){
            mParams.regionCode = regionCode;
            return this;
        }

        public Builder setCurrencyCode(String currencyCode){
            mParams.currencyCode = currencyCode;
            return this;
        }

        public Builder setPayChannel(String payChannel) {
            mParams.payChannel = payChannel;
            return this;
        }
        /**
         * set openID
         * @param openID
         */
        public Builder setOpenID(String openID){
            mParams.openID = openID;
            return this;
        }

        /**
         * set account zone ID
         * @param zoneID
         */
        public Builder setZoneID(String zoneID){
            mParams.zoneID = zoneID;
            return this;
        }

        /**
         * set google inAppMessaging
         * @param inAppMessaging
         */
        public Builder setInAppMessage(Boolean inAppMessaging){
            mParams.inAppMessaging = inAppMessaging;
            return this;
        }
        /**
         * Set extension field
         * The extended fields have default values, set under special services
         * @param extra
         */
        public Builder setExtra(InitParamsExtra extra){
            mParams.extra = extra;
            return this;
        }

        public InitParams build(){
            if(!mParams.checkParams()){
                CTILog.i(TAG, "params are missed,please check init params");
//                throw new IllegalArgumentException("params are missed,please check init params.");
            }
            return mParams;
        }
    }



    /*******************************--InitParamsExtra--*************************************/
    /**
     * Initialize the extension field
     * Contains: fields with default values and fields that are not commonly used
     */
    public static class InitParamsExtra {
        private String pf = "huyu_m-2001-android-2001";
        private String pfKey = "pfKey";
        private String sessionID = "hy_gameid";
        private String sessionType = "st_dummy";
        private String openKey = "openkey";//The user key after the user logs in, corresponding to the userKey parameter of the lower version
        private String goodsZoneID = ""; //Game zone id
        private String idcInfo;
        private String channelExtras;

        public String getChannelExtras() {
            return channelExtras;
        }

        public void setChannelExtras(String channelExtras) {
            this.channelExtras = channelExtras;
        }

        /**
         * Set pf
         * The default value is "huyu_m-2001-android-2001"
         * @param pf
         */
        public void setPF(String pf){
            this.pf = pf;
        }

        /**
         * Set pfKey
         * The default value is: "pfKey"
         * @param pfKey
         */
        public void setPFKey(String pfKey){
            this.pfKey = pfKey;
        }

        /**
         * Set sessionID
         * The default value is "hy_gameid"
         * @param sessionID
         */
        public void setSessionID(String sessionID){
            this.sessionID = sessionID;
        }

        /**
         * Set sessionType
         * The default value is: "st_dummy"
         * @param sessionType
         */
        public void setSessionType(String sessionType){
            this.sessionType = sessionType;
        }

        /**
         * Set up the game community
         * @param goodsZoneID
         */
        public void setGoodsZoneID(String goodsZoneID){
            this.goodsZoneID = goodsZoneID;
        }

        /**
         * Set idc information
         * The interface setting is called for special business logic, and general business does not need to be set
         * @param idcInfo
         */
        public void setIDCInfo(String idcInfo){
            this.idcInfo = idcInfo;
        }

        /**
         * The user key after the user logs in, corresponding to the userKey parameter of the lower version
         * @param openKey
         */
        public void setOpenKey(String openKey) {
            this.openKey = openKey;
        }

        public String getOpenKey() {
            return openKey;
        }

        public String getPf(){
            return pf;
        }

        public String getPfKey(){
            return pfKey;
        }

        public String getSessionID(){
            return sessionID;
        }

        public String getSessionType(){
            return sessionType;
        }

        public String getGoodsZoneID(){
            return goodsZoneID;
        }

        public String getIDCInfo(){
            return idcInfo;
        }
    }
}
