package com.centauri.oversea.business.h5.webview;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.widget.Toast;

import com.centauri.comm.CTILog;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.MConstants;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * intercept specify url and replace
 */
public class MUrlIntercept implements IIntercept {

    private final String TAG = "MUrlIntercept";

    private String env = "dev";
    private ArrayList<InterceptItem> interceptItems;

    public void setInterceptItems(ArrayList<InterceptItem> interceptItems){
        this.interceptItems = interceptItems;
    }

    //pass aidl params from outside
    public void setJsResource(String jsResource){
        if(!TextUtils.isEmpty(jsResource)){
            try{
                JSONObject js = new JSONObject(jsResource);
                env = js.optString("env");
            }catch (JSONException e){
                CTILog.d(TAG,"setJsResource exception: "+e.getMessage());
            }
        }
    }

    @Override
    public int level() {
        return REPLACE_URL;
    }

    @Override
    public WebResourceResponse queryCache(String url) {
        return null;
    }


    private void showErrMessage(Context context){
        String tips = CTICommMethod.getStringId(context, "unipay_error_not_installed");
        Toast.makeText(context, tips,Toast.LENGTH_LONG).show();
    }

    /**
     * Handle custom URLs
     * @param view WebView
     * @param url The current URL
     * @return true means that the processing succeeded and no further processing is required, and false means to continue processing
     */
    private boolean handleCustomUrl(WebView view, InterceptItem matchedItem,String url){
        if(matchedItem != null){
            String getParams = "";
            int index = url.lastIndexOf('?');
            if(index != -1){
                getParams = url.substring(index);
            }

            String destUrl = matchedItem.redirectUrl.replace("{env}",switchEnv());
            //integer url
            String integerUrl = destUrl + getParams;

            CTILog.d(TAG,"handleCustomUrl matched url: "+url);
            CTILog.d(TAG,"handleCustomUrl integerUrl url: "+integerUrl);
            view.loadUrl(integerUrl);
            return true;
        }
        return false;
    }
    /**
     * Processing custom schemes
     * @param view WebView
     * @param url The current URL
     * @return true means that the processing succeeded and no further processing is required, and false means to continue processing
     */
    private boolean handleCustomScheme(WebView view, InterceptItem matchedItem,String url){
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            view.getContext().startActivity( intent );
        }catch (Exception e){
            showErrMessage(view.getContext());
        }

        return true;
    }
    /**
     * The Intent handles the custom Intent
     * @param view WebView
     * @param url The current URL
     * @return true means that the processing succeeded and no further processing is required, and false means to continue processing
     */
    private boolean handleIntent(WebView view,InterceptItem matchedItem,String url){
        Intent intent;
        try {
            intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME);
        } catch (URISyntaxException e) {
            Log.e(TAG, "URISyntaxException: " + e.getLocalizedMessage());
            showErrMessage(view.getContext());
            return true;
        }

        intent.setComponent(null);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
            intent.setSelector(null);
        }

        //intent://dangdang://product//pid=23248697#Intent;scheme=dangdang://product//pid=23248697;package=com.dangdang.buy2;end
        //该Intent无法被设备上的应用程序处理
        if (view.getContext().getPackageManager().resolveActivity(intent, 0) == null) {
            showErrMessage(view.getContext());
            return true;
        }

        try {
            view.getContext().startActivity( intent );
        } catch (Exception e) {
            Log.e(TAG, "ActivityNotFoundException: " + e.getLocalizedMessage());
            showErrMessage(view.getContext());
            return true;
        }

        return true;
    }

    /**
     *
     * @param view WebView
     * @param url The current URL
     * @return true means that the processing succeeded and no further processing is required, and false means to continue processing
     */
    private boolean handleCommScheme(WebView view, String url){

        if (url.startsWith("sms:") || url.startsWith("smsto:") || url
                .startsWith("mms:") || url.startsWith("mmsto:")) {

            try {
                CTILog.d(TAG,"handleCommScheme url:"+ url);
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                view.getContext().startActivity(intent);
            }catch (Exception e){
                e.printStackTrace();
            }
            return true; // If we return true, onPageStarted, onPageFinished won't be called.
        }else if(url.startsWith("tel:")){
            try {
                CTILog.d(TAG,"handleCommScheme url:"+ url);
                Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse(url));
                view.getContext().startActivity(intent);
            }catch (Exception e){
                e.printStackTrace();
            }
            return true; // If we return true, onPageStarted, onPageFinished won't be called.
        }
        /*******************************************************
         * Added in support for mailto:
         *******************************************************/
        else if (url.startsWith("mailto:")) {
            CTILog.d(TAG,"handleCommScheme url:"+ url);
            try{
                Intent intent = new Intent(Intent.ACTION_SENDTO, Uri.parse(url));
                view.getContext().startActivity(intent);
            }catch (Exception e){
                e.printStackTrace();
            }
            return true;
        }
        return  false;
    }

    /**
     * Add the Referer for a specific request
     * @param view WebView
     * @param url The current URL
     * @return true means that the processing succeeded and no further processing is required, and false means to continue processing
     */
    private boolean handleReferrer(WebView view,InterceptItem matchedItem, String url){
        Map headers = new HashMap();
        try{

            String getParams = "";
            int index = url.lastIndexOf('?');
            if(index != -1){
                getParams = url.substring(index);
            }

            String destUrl = matchedItem.redirectUrl.replace("{env}",switchEnv());
            //integer url
            String integerUrl = destUrl + getParams;
            CTILog.d(TAG," handleReferrer matched url: "+url);
            CTILog.d(TAG,"handleReferrer integerUrl url: "+integerUrl);
            headers.put("Referer", integerUrl);//The request header of the third-party payment platform is generally fixed by the other party
        }catch (Exception e){
            e.printStackTrace();
        }
        view.loadUrl(url, headers);
        return  true;
    }

    @Override
    public String handleUrl(WebView view, String url) {
        if(!TextUtils.isEmpty(url)
                && interceptItems != null && !interceptItems.isEmpty()){

            InterceptItem matchedItem = null;
            for(InterceptItem item : interceptItems){
                if(Pattern.matches(item.pattern,url)){
                    matchedItem = item;
                    break;
                }
            }

            if(matchedItem != null){
                if (matchedItem.type.equals("scheme") ){
                    handleCustomScheme(view,matchedItem,url);
                }else if (matchedItem.type.equals("intent")){
                    handleIntent(view,matchedItem,url);
                }else if (matchedItem.type.equals("referrer")){
                    handleReferrer(view,matchedItem,url);
                }
                else{
                    if(!handleCustomUrl(view,matchedItem,url)){
                        view.loadUrl(url);
                    }
                }
            }else{
                if (!handleCommScheme(view,url)){
                    view.loadUrl(url);
                }
            }
        }else{
            if (!handleCommScheme(view,url)) {
                view.loadUrl(url);
            }
        }
        return url;
    }

    /**
     * switch env
     * h5 accepted there env:dev/sandbox/release
     */
    private String switchEnv(){
        if(MConstants.ReleaseEnv.equals(env)){
            return "hk";
        }else if(MConstants.DevEnv.equals(env)){
            return env;
        }else{
            return "sandbox";
        }
    }
}
