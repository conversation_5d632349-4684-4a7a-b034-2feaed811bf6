package com.centauri.oversea.business.pay;

import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;

import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.TestConfig;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.comm.CTILog;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.data.CTIPayInfo;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.centauri.oversea.newnetwork.http.NetworkManager;
import com.centauri.oversea.newnetwork.model.CTIOverSeaCommAns;

import org.json.JSONException;
import org.json.JSONObject;


/**
 * MVP model
 * Data storage, reporting, network requests
 * Created by zachzeng on 2018/1/11.
 */

public class CTIPayModel {
    public static final String TAG = "APPayModel";

    // Data may be obtained from the channel side at the time of ordering
    private String payAmt;      // Amount at time of payment
    private String payCurrency; // The currency of payment    此字段的值为支付前拉到的币种
    private String channelExtra;
    private String gw_version = "";
    private String gw_pricingPhases = "";
    private String basePlanId = "";

    private String productType;
    private boolean hasGoodsList = true;

    // This part of the data can be sent from the background
    private String num;
    private String mBillNo;
    private String mCurrency;   // 此字段的值为下单返回的币种
    private String offer_name;
    private String product_name;
    private String currency_amt;
    private JSONObject mInfo;

    private String mPayInfo = "";                     // Order and return payment information
    private CTIPayReceipt mProvideReceipt = null;    // invoice upon shipment

    private String verifyRes = "";               // After verifying the ticket, CGI returns the result that Netmarble uses
    private String provideMes = "";            // Delivery return information
    private String provideSdkRet = "";          // Shipping back to SDK_RET
    private boolean isErrorConsume = false;   // Cancellation of note in case of error 5017, 1138

    private XCallback mCallback = null; // A callback that interacts with the Presenter
    private BillingFlowParams mRequest = null;


    //order
    public void order(Object orderInfo) {
        if (mRequest != null) {
            String amt = "";
            String serviceCode = "";
            String num = hasGoodsList ? "" : "1";

            if (BillingFlowParams.TYPE_UNION_MONTH.equals(mRequest.getType())) {
                serviceCode = mRequest.getServiceCode();
            }

            if (orderInfo == null) {
                payCurrency = "";
                payAmt = "";
            } else {
                CTIPayInfo _orderInfo = (CTIPayInfo) orderInfo;
                amt = _orderInfo.amount;
                payAmt = _orderInfo.ext;
                payCurrency = _orderInfo.currency;
                channelExtra = _orderInfo.channelExtra;
                gw_version = _orderInfo.gw_version;
                gw_pricingPhases = _orderInfo.gw_pricingPhases;
                basePlanId = _orderInfo.basePlanId;
            }
            NetworkManager.singleton().getOrder(
                    mRequest.getPayChannel(),
                    gw_version,
                    basePlanId,
                    gw_pricingPhases,
                    mRequest.getCurrencyType(),
                    payCurrency,
                    payAmt,
                    serviceCode,
                    num,
                    amt,
                    channelExtra,
                    mRequest,
                    mOrderObserver
            );
        }
    }

    //provide
    public void provide(Object receipt) {
        if (mRequest != null) {
            setProvideReceipt((CTIPayReceipt) receipt);

            NetworkManager.singleton().provide(
                    false,
                    gw_version,
                    "",
                    mProvideReceipt.second_currency_type,
                    mRequest.getPayChannel(),
                    num,
                    mBillNo,
                    mProvideReceipt.receipt,
                    null,
                    mProvideReceipt.receipt_sig,
                    mRequest,
                    mProvideReceipt,
                    mProvideObserver);
        }
    }
    //GW5.1 provide
    public void newProvide(Object receipt) {
        if (mRequest != null) {
            setProvideReceipt((CTIPayReceipt) receipt);

            NetworkManager.singleton().newProvide(
                    false,
                    mProvideReceipt.second_currency_type,
                    mProvideReceipt.basePlanId,
                    "",
                    mProvideReceipt.gw_version,
                    mRequest.getPayChannel(),
                    num,
                    mBillNo,
                    mProvideReceipt.receipt,
                    null,
                    mProvideReceipt.receipt_sig,
                    mRequest,
                    mProvideReceipt,
                    mProvideObserver);
        }
    }

    public void setCallback(XCallback callback) {
        mCallback = callback;
    }

    public void setRequest(BillingFlowParams request) {
        mRequest = request;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public void setHasGoodsList(boolean flag) {
        hasGoodsList = flag;
    }

    public BillingFlowParams getRequest() {
        if (mRequest == null) {
            mRequest = new BillingFlowParams.Builder().build();
        }
        return mRequest;
    }

    public String getPayInfo() {
        return mPayInfo;
    }

    public JSONObject getMInfo() {
        return mInfo;
    }

    public String getCurrencyAmt() {
        return currency_amt;
    }

    public String getCurrency() {
        return mCurrency;
    }

    public String getOfferName() {
        return offer_name;
    }

    public String getProductName() {
        return product_name;
    }

    public int getNum() {
        try {
            return Integer.parseInt(num);
        } catch (Exception e) {
            CTILog.e(TAG, "getNum(): " + e.getMessage());
        }
        return 0;
    }

    public String getBillNo() {
        return mBillNo;
    }

    public void setBillNo(String billNo) {
        mBillNo = billNo;
    }

    public boolean isErrorConsume() {
        return isErrorConsume;
    }

    public CTIPayReceipt getProvideReceipt() {
        if (mProvideReceipt == null) {
            mProvideReceipt = new CTIPayReceipt();
        }
        return mProvideReceipt;
    }

    public void setProvideReceipt(CTIPayReceipt receipt) {
        mProvideReceipt = receipt;
    }

    public String getProvideRetMes() {
        return provideMes;
    }

    public String getProvideSdkRet(){ return provideSdkRet; }

    public String getChannelId() {
        if (mRequest != null) {
            return mRequest.getPayChannel();
        }
        return "";
    }

    public String getVerifyRes() {
        return verifyRes;
    }


    public void release() {
        mInfo = null;
        mProvideReceipt = null;
        mCallback = null;
        mRequest = null;
    }

    /*********************************net callback******************************************/

    //order callback
    private ICTIHttpCallback mOrderObserver = new ICTIHttpCallback() {

        @Override
        public void onStop(CTIHttpAns ans) {
            notifyInner(MConstants.MSG_PAYCHANNEL_CANCEL,ans.getMsgErrorCode(), "Order network cancel");
            CTILog.i(TAG, "getOrder onStop.");
        }

        @Override
        public void onSuccess(CTIHttpAns ans) {
            int retCode = ans.getResultCode();

            if (TestConfig.retCodeOrder != 0) {
                retCode = TestConfig.retCodeOrder;
                CTILog.i(TAG, "TestConfig");
            }

            CTILog.i(TAG, "getOrder onSuccess: " + retCode + "_" + ans.getResultMessage());

            switch (retCode) {
                case MRetCode.OK:
                    CTIOverSeaCommAns oversea = (CTIOverSeaCommAns) ans;
                    mBillNo = oversea.getBillno();
                    mInfo = oversea.getInfo();
                    currency_amt = oversea.getAmount();
                    mCurrency = oversea.getCurrentType();
                    offer_name = oversea.getOfferName();
                    product_name = oversea.getProductName();
                    num = oversea.getNum();

                    notifyInner(MConstants.MSG_PAYCHANNEL_GET_ORDER_SUCC,ans.getMsgErrorCode(), null);

                    //Saves the information before placing the order for easy callbacks to the application
                    try {
                        JSONObject json = new JSONObject();
                        json.put("offerid", GlobalData.singleton().offerID);
                        json.put("productid", getRequest().getProductID());
                        json.put("productname", product_name);
                        json.put("price", payAmt);
                        json.put("paychannel", getChannelId());
                        json.put("currency", payCurrency);
                        mPayInfo = json.toString();
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                    break;
                case MRetCode.ERR_LOG_INVALID:
                    notifyOuterLoginErr();
                    break;
                case MRetCode.ERR_1145: //order risk
//                case MRetCode.ERR_1138:
                    notifyOuterRiskErr(retCode,ans.getMsgErrorCode(),ans.getResultMessage());
                    break;
                case MRetCode.ERR_1138:
                    try {
                        JSONObject js = new JSONObject(ans.getResultData());
                        JSONObject provideJs = new JSONObject(js.optString("order"));
                        notifyRiskField(MConstants.MSG_PAYCHANNEL_GET_ORDER_ERROR, ans.getMsgErrorCode(), MRetCode.ERR_1138, provideJs.optString("copywrite_format"), provideJs.optString("copywrite_params_list"), ans.getResultMessage());
                    } catch (JSONException e) {
                        notifyRiskField(MConstants.MSG_PAYCHANNEL_GET_ORDER_ERROR, ans.getMsgErrorCode(), MRetCode.ERR_1138, "", "", ans.getResultMessage());
                    }
                    break;
                default: {
                    notifyInner(MConstants.MSG_PAYCHANNEL_GET_ORDER_ERROR, ans.getMsgErrorCode(),ans.getResultMessage());
                    break;
                }
            }
        }


        @Override
        public void onFailure(CTIHttpAns ans) {
            //The underlying network module is not multilingual and is prompted by the SDK
            notifyInner(MConstants.MSG_PAYCHANNEL_GET_ORDER_ERROR,MRetCode.ERR_NET,ans.getMsgErrorCode(),"Network Exception");
            CTILog.i(TAG, "getOrder onFailure: " + ans.getResultCode() + "_" + ans.getResultMessage());
        }
    };


    //provide callback
    private ICTIHttpCallback mProvideObserver = new ICTIHttpCallback() {

        @Override
        public void onStop(CTIHttpAns ans) {
            notifyInner(MConstants.MSG_PAYCHANNEL_CANCEL, ans.getMsgErrorCode(),"Provide network cancel");
            CTILog.i(TAG, "provide onStop.");
        }

        @Override
        public void onSuccess(CTIHttpAns ans) {
            int retCode = ans.getResultCode();
            provideMes = ans.getResultMessage();
            verifyRes = (((CTIOverSeaCommAns) ans).getVerifyRes());
            provideSdkRet = (((CTIOverSeaCommAns) ans).getProvideSdkRet());


            if (retCode == MRetCode.ERR_5017) {//Whether the error is 5017, 1138
                isErrorConsume = true;
            }

            if (TestConfig.retCodeProvide != 0) {  //Easy to test the added code
                retCode = TestConfig.retCodeProvide;
                provideMes = "test error msg:" + retCode;
                CTILog.i(TAG, "TestConfig");
            }

            switch (retCode) {
                case MRetCode.OK:
                    notifyInner(MConstants.MSG_PAYCHANNEL_PROVIDE_SUCC,ans.getMsgErrorCode(), null);
                    break;
                case MRetCode.ERR_LOG_INVALID:
                    notifyOuterLoginErr();
                    break;
                case MRetCode.ERR_1138:
                    try {
                        JSONObject js = new JSONObject(ans.getResultData());
                        JSONObject provideJs = new JSONObject(js.optString("provide"));
                        notifyRiskField(MConstants.MSG_PAYCHANNEL_PROVIDE_ERROR, ans.getMsgErrorCode(), MRetCode.ERR_1138, provideJs.optString("copywrite_format"), provideJs.optString("copywrite_params_list"), ans.getResultMessage());
                    } catch (JSONException e) {
                        notifyRiskField(MConstants.MSG_PAYCHANNEL_PROVIDE_ERROR, ans.getMsgErrorCode(), MRetCode.ERR_1138, "", "", ans.getResultMessage());
                    }
                    break;
                default:
                    notifyInner(MConstants.MSG_PAYCHANNEL_PROVIDE_ERROR, MRetCode.ERR_PAY_SUCC_POST_ERR, ans.getMsgErrorCode(),provideMes);
                    break;
            }
            CTILog.i(TAG, "provide onSuccess: " + retCode + "_" + provideMes);
        }

        @Override
        public void onFailure(CTIHttpAns ans) {
            //The underlying network module is not multilingual and is prompted by the SDK
            notifyInner(MConstants.MSG_PAYCHANNEL_PROVIDE_ERROR,MRetCode.ERR_PAY_SUCC_POST_NET_ERR,ans.getMsgErrorCode(),"Network Exception");
            CTILog.i(TAG, "provide onFailure: " + ans.getResultCode() + "_" + ans.getResultMessage());
        }
    };


    //Insert report data
    public void report(String name,String result) {

        StringBuilder sb = new StringBuilder();
        sb.append("name=").append(name)
                .append("&result=").append(result)
                .append("&payChannel=").append(getChannelId());

        if(!TextUtils.isEmpty(productType))
        {
            sb.append("&productType=").append(productType);
        }

        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_CHANNEL_PAYRESULT,
                sb.toString());
    }

    //Encapsulate a layer, add null-checking logic
    private void notifyInner(int what, String msgErrCode ,Object obj) {
        if (mCallback != null) {
            Message message = new Message();
            message.what = what;
            message.obj = obj;
            Bundle bundle = new Bundle();
            bundle.putString("msgErrCode",msgErrCode);
            message.setData(bundle);
            mCallback.notifyInner(message);
        }
    }

    //Encapsulate a layer, add null-checking logic
    private void notifyInner(int what,int arg1,String msgErrCode, Object obj) {
        if (mCallback != null) {
            Message message = new Message();
            message.what = what;
            message.arg1 = arg1;
            message.obj = obj;
            Bundle bundle = new Bundle();
            bundle.putString("msgErrCode",msgErrCode);
            message.setData(bundle);
            mCallback.notifyInner(message);
        }
    }

    //Encapsulate a layer, add risk field
    private void notifyRiskField(int what, String msgErrCode, int retCode, String copywrite_format, String copywrite_params_list, Object obj) {
        if (mCallback != null) {
            Message message = new Message();
            message.what = what;
            message.obj = obj;
            Bundle bundle = new Bundle();
            bundle.putString("msgErrCode",msgErrCode);
            bundle.putString("retCode", String.valueOf(retCode));
            bundle.putString("copywrite_format",copywrite_format);
            bundle.putString("copywrite_params_list",copywrite_params_list);
            message.setData(bundle);
            mCallback.notifyInner(message);
        }
    }

    //Login invalidation
    private void notifyOuterLoginErr() {
        if (mCallback != null) {
            mCallback.notifyOuterLoginErr();
        }
    }

    //Risk control strategy
    private void notifyOuterRiskErr(int errCode,String msgErrCode,String errMsg){
        if(mCallback != null){
            mCallback.notifyOuterRiskErr(errCode,msgErrCode,errMsg);
        }
    }
}
