/*****************************************************************************
 * @ copyright Copyright (C), 1998-2021, Centauri Tech. Co., Ltd.
 * @ file     : IGetProduct.java
 * @ brief    :
 * @ author   : dongbingliu
 * @ version  : 1.0.0
 * @ date     : 2021/7/8
 *****************************************************************************/
package com.centauri.oversea.business;

import android.app.Activity;
import android.content.Context;

import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.oversea.api.request.CTIBaseRequest;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.InfoCallback;
import com.centauri.oversea.api.request.CTIGameRequest;

import org.json.JSONArray;

import java.util.HashMap;
import java.util.List;


public interface IGetProduct {
    /**
     * GooglePlay Channel
     * @param context context
     * @param productIdMap hashmap productList
     * @param callback callback
     */
    void getProductInfo(Context context, HashMap<String, String> productIdMap, InfoCallback callback) ;

    /**
     * GooglePlay Channel
     * @param context context
     * @param productIdMap hashmap productList
     * @param callback callback
     */
    void getProductInfo(Context context, HashMap<String, String> productIdMap, String resultData, InfoCallback callback) ;

    /**
     * ToyPay channel need activity (context) , add Overload method
     * @param activity activity
     * @param productIdMap hashmap productList
     * @param callback callback
     */
    void getProductInfo(Activity activity, HashMap<String, String> productIdMap, InfoCallback callback) ;

    /**
     * Garena Channel access to item information interface
     * @param activity activity
     * @param ctiGameRequest baseRequest
     * @param infoCallback callback
     */
    void getProductInfo(Activity activity, CTIGameRequest ctiGameRequest, InfoCallback infoCallback)  ;


    void clearPurchase(Context context);
}
