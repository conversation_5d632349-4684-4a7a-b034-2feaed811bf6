package com.centauri.oversea.business;

import android.content.Context;

import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.InfoCallback;
import com.centauri.oversea.newnetwork.http.NetworkManager;
import com.centauri.oversea.newnetwork.model.CTIIntroPriceAns;

import java.util.HashMap;
import java.util.List;

public abstract class CTIBaseIntroInfo {

    protected final static int RET_OK = 0;
    protected final static int RET_ERR = -1;

    protected String channel = "";                // channel name
    protected HashMap<String, String> productIdMap = null;   // Query the item list

    // Check discount information
    public abstract void getIntroInfo(Context context, String channel, HashMap<String, String> productIdMap, InfoCallback callback) ;

    // Check the background item information
    protected void queryServerInfo(final ICTICallback callback){

        if(productIdMap == null || productIdMap.isEmpty()){
            callback.callback(RET_ERR,"queryServerInfo productId is null !!!");
            return;
        }

        NetworkManager.singleton().introPriceReq(channel, productIdMap, new ICTIHttpCallback() {
            @Override
            public void onSuccess(CTIHttpAns ctiHttpAns) {
                if(callback != null){
                    callback.callback(RET_OK,((CTIIntroPriceAns)ctiHttpAns).getJsIntroInfo());
                }
            }

            @Override
            public void onFailure(CTIHttpAns ctiHttpAns) {
                if(callback != null){
                    callback.callback(RET_ERR,"");
                }
            }

            @Override
            public void onStop(CTIHttpAns ctiHttpAns) {
                if(callback != null){
                    callback.callback(RET_ERR,"");
                }
            }
        });
    }
}
