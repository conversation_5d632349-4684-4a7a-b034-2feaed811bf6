package com.centauri.oversea.business.h5.webview;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.http.SslCertificate;
import android.net.http.SslError;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.webkit.CookieManager;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.centauri.comm.CTILog;
import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.CTISPTools;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.newnetwork.http.NetworkManager;

import java.util.HashMap;
import java.util.Map;

/**
 * Custom WebView
 */
public class MWebView extends WebView{
    public static final String TAG = "MWebView";

    private String APP_CACHE_PATH;      //AppCache Pate
    private boolean isIntercept = false;//if intercept
    private IIntercept intercept = null;
    private WebChromeClientListener _wccListener = null;
    private WebViewClientListener _wvcListener = null;
    private MUrlIntercept mUrlIntercept = null;
    private Handler _mainHandler = new Handler(Looper.getMainLooper());
    private static final String SP_CACHE_SAVE_TIME = "cachetime";
    private boolean isFirstLoad = true;
    private int isCache = 0;
    private String payChannel = "";

    public MWebView(Context context){
        super(context);
        init(context);
    }

    public MWebView(Context context, AttributeSet attrs){
        super(context, attrs);
        init(context);
    }

    private void init(Context context){

        isFirstLoad=true;
        WebSettings webSettings = getSettings();
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setJavaScriptEnabled(true);        // Allow JS code
        webSettings.setTextZoom(100);                // Disable text scaling
        //webSettings.setSupportMultipleWindows(true);    // Support pop-up downloads
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);

        //Disable zooming
        webSettings.setDisplayZoomControls(false);
        webSettings.setBuiltInZoomControls(false);

        //setting App Cache
        APP_CACHE_PATH = context.getFilesDir().getAbsolutePath()+"/MWebCache";
//        webSettings.setAppCacheEnabled(true);
//        webSettings.setAppCacheMaxSize(8 * 1024 * 1024);
//        webSettings.setAppCachePath(APP_CACHE_PATH);
        //enable Dom storage Cache
        webSettings.setDomStorageEnabled(true);


        //setting cache mode
        // In Wesing's feedback, the load_default setting is not in effect, so it is necessary to manually determine whether to read the cache
        if(isUseCache(getContext())){
            isCache = 1;
            webSettings.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK); // This is the default load cache
        }else {
            isCache = 0;
            webSettings.setCacheMode(WebSettings.LOAD_DEFAULT); // The default is to load the cache first, but the actual verification does not work
            setCacheTime(getContext());
        }

        webSettings.setAllowFileAccess(true);           // Allow the WebView to use the File protocol
        webSettings.setSavePassword(false);            // Do not save the password
        webSettings.setLoadsImagesAutomatically(true); // Automatically load image ls

        setWebViewClient(mWebViewClient);
        setWebChromeClient(mWebChromeClient);

        // Enable mixed mode loading above 5.0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            CookieManager.getInstance().setAcceptThirdPartyCookies(this,true);
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        if(CTITools.isApkInDebug(getContext())){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                CTILog.d(TAG,"!!!WebContentsDebuggingEnabled == true  in debug mode");
                WebView.setWebContentsDebuggingEnabled(true);
            }
        }

        //Remove insecure interfaces
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                removeJavascriptInterface("searchBoxJavaBridge_");
            }
            removeJavascriptInterface("accessibility");
            removeJavascriptInterface("accessibilityTraversal");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setCacheTime(Context context){

        int now = (int)(System.currentTimeMillis()/1000);
        CTISPTools.putInt(context, SP_CACHE_SAVE_TIME, now);

        CTILog.d(TAG,"setCacheTime:" + now);
    }

    //Caching is not used by default and is only used after the last time an online page was used for no more than an interval
    private boolean isUseCache(Context context){

        int lastTime = CTISPTools.getInt(context, SP_CACHE_SAVE_TIME);

        CTILog.d(TAG,"isUseCache CacheTime lastTime:" + lastTime);

        if(lastTime == 0){
            return false;
        }else {
            int defaultTime = 60 * 60; //1 hour interval
//            defaultTime = 20; //test
            int now = (int)(System.currentTimeMillis()/1000);
            int inter= now - lastTime ;
            if (inter <= defaultTime){
                return true;
            }
            return false;
        }
    }

    @Override
    public void loadUrl(final String url) {

        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                MWebView.super.loadUrl(url);
            }
        });
    }

    @Override
    public void loadUrl(final String url, final Map<String, String> additionalHttpHeaders) {

        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                MWebView.super.loadUrl(url,additionalHttpHeaders);
            }
        });
    }

    @Override
    public void reload() {
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                MWebView.super.reload();
            }
        });
    }

    /**
     * External Settings whether to use local preloaded caching
     * @param isIntercept
     */
    public void setInterceptFlag(boolean isIntercept){
        this.isIntercept = isIntercept;
    }

    //Set up the cache
    public void setIntercept(IIntercept intercept){
        this.isIntercept = true;
        this.intercept = intercept;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    //Main thread execution
    private void runOnMainThread(Runnable runnable){
        if(Looper.getMainLooper() == Looper.myLooper()){
            runnable.run();
        }else{
            _mainHandler.post(runnable);
        }
    }


    public void setWebChromeClientListener(WebChromeClientListener listener){
        this._wccListener = listener;
    }


    public void setWebViewClientListener(WebViewClientListener listener){
        this._wvcListener = listener;
    }

    /********************************inner class*************************************************/

    private WebChromeClient mWebChromeClient = new WebChromeClient(){
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);

            //load finish

            if(isFirstLoad){
                if(newProgress == 100){
                    MTimer.stop(MTimer.SDK_WEBVIEW_PAGE_LOAD);
                    MTimer.stop(MTimer.SDK_WEBVIEW_LOAD);
                    StringBuilder sb = new StringBuilder();
                    sb.append("time=");
                    sb.append(MTimer.duration(MTimer.SDK_WEBVIEW_LOAD));
                    sb.append("&");
                    sb.append("processtime=");
                    sb.append(MTimer.duration(MTimer.SDK_WEBVIEW_PROCESS_LOAD));
                    sb.append("&");
                    sb.append("pagetime=");
                    sb.append(MTimer.duration(MTimer.SDK_WEBVIEW_PAGE_LOAD));
                    sb.append("&");
                    sb.append("isvisable=");
                    sb.append(MTimer.duration(MTimer.SDK_WEBVIEW_DOM_LOAD));
                    sb.append("&");
                    sb.append("isCache=");
                    sb.append(isCache);
                    CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_WEBVIEW_END, sb.toString());

                    webviewReport();

                    isFirstLoad=false;
                }
            }

            if(_wccListener != null){
                _wccListener.onProgressChanged(newProgress);
            }
        }

        /**
         * Interact with js
         * @Param Message protocol standard：oversea://jsbridge?action=response&status=success
         */
        @Override
        public boolean onJsAlert(WebView view, String url, String message,
                                 android.webkit.JsResult result) {
            if(_wccListener != null){
                _wccListener.onJsAlert(url,message,result);
            }

            result.cancel();
            return true;
        }
    };

    private WebViewClient mWebViewClient = new WebViewClient(){

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            CTILog.d(TAG,"onPageStarted url: "+url);
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onPageCommitVisible(WebView view, String url) {
            super.onPageCommitVisible(view, url);
            CTILog.d(TAG,"onPageCommitVisible url: "+url);

            if(isFirstLoad){
                MTimer.stop(MTimer.SDK_WEBVIEW_DOM_LOAD);
            }
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            CTILog.d(TAG,"onPageFinished url: "+url);
            super.onPageFinished(view, url);

            if(isFirstLoad){
                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_WEBVIEW_LOAD, "");

                webviewReport();
            }
        }

//        public boolean shouldOverrideUrlLoading(WebView view,
//                                                WebResourceRequest request){

        public boolean shouldOverrideUrlLoading(WebView view, String url){

//        String url = "";
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//                url = request.getUrl().toString();
//            } else {
//                url = request.toString();
//            }

            CTILog.i(TAG,"shouldOverrideUrlLoading url: "+url);
            CTILog.i(TAG,"payChannel = : "+payChannel);

            if (("centauri://sdk.os_mp.result.success".equals(url) || "centauri://sdk.os_mp.result.cancel".equals(url))
                    && ("os_mp".equals(payChannel) || "os_midaspay".equals(payChannel))) {
                int resultCode = "centauri://sdk.os_mp.result.success".equals(url) ? 0 : 1;
                _wccListener.onMpDestory(resultCode);
                return true;
            }
            // Fix a bug where MX 4.0.1 listens for server redirection
// prevent public account browser blank page;
// Calling JSBridge below 3.0 will result in a About :blank and the system browser will be called
//            if (TextUtils.isEmpty(url) || "about:blank;".equals(url) || "about:blank".equals(url)) {
//                CTILog.e(TAG,new StringBuilder("shouldOverrideUrlLoading fail , url=[").append(url).append("].").toString());
//                return true;
//            }
            //replace url
            if(isIntercept && intercept != null){
                if(IIntercept.REPLACE_URL == intercept.level()){
                    intercept.handleUrl(view,url);
                    return true;
                }
            }
            if (url.contains("referer=")) {
                String referer = url.split("referer=")[1].split("&")[0];
                Map<String, String> headers = new HashMap<>();
                headers.put("Referer", referer);
                view.loadUrl(url, headers);
            }

            view.loadUrl(url);
            return true;
        }

        //A callback is given when a load error occurs, where you can do error handling, such as requesting another load or a 404 error page
        public void onReceivedError(WebView view, int errorCode,String description, String failingUrl){
            CTILog.e(TAG,"onReceivedError description: "+description);
            super.onReceivedError(view, errorCode, description, failingUrl);

            if(isFirstLoad){
                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_WEBVIEW_ERROR, "");
                webviewReport();
            }

            if(_wvcListener != null){
                _wvcListener.onRequestError(description);
            }
        }

        //This function is called back when an HTTPS error is received, where error handling can be done
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error){

            //super.onReceivedSslError(view,handler,error);
            SslCertificate cert = error.getCertificate();
            String url = view.getUrl();
            CTILog.e(TAG, "onReceivedSslError:" + error.getPrimaryError() + ", cert=" + (cert == null ? "null" : cert.toString())+ ", pageUrl=" + url);

            super.onReceivedSslError(view, handler, error);

            //TODO  we could not support this feature now.There is a bug with this feature.when the ssl error fired.the
            // "request error,https ssl error" shows
//            if (APTools.isApkInDebug(view.getContext())) {
//                handler.proceed();
//                Toast.makeText(view.getContext(),"!!!webview debug mode,ignore the ssl eeror",Toast.LENGTH_LONG).show();
//            } else {
//                handler.cancel();
//            }
            //super.onReceivedSslError(view, handler, error);
            if(_wvcListener != null){
                _wvcListener.onRequestError("request error,https ssl error.");
            }
        }

        /**
         * Each time a resource is requested, it is called back and forth through this function
         * Determine whether to intercept or not
         */
        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, final WebResourceRequest request) {
            Log.d(TAG,"shouldInterceptRequest: "+request.getUrl());

            WebResourceResponse response = null;
            if(isIntercept && intercept != null){
                switch (intercept.level()){
                    //use local cache
                    case IIntercept.QUERY_CACHE:
                        response = intercept.queryCache(request.getUrl().toString());
                        break;
                }
            }

            return response != null ? response :
                    super.shouldInterceptRequest(view, request);
        }
    };

    public interface WebChromeClientListener{
        void onJsAlert(String url, String message,
                       android.webkit.JsResult result);
        void onProgressChanged(int progress);
        void onMpDestory(int retCode);
    }

    public interface WebViewClientListener{
        void onRequestError(String errMsg);
        void onPageFinished(WebView view, String url);
        void onPageStarted(WebView view, String url);
    }

    private void webviewReport(){
        NetworkManager.singleton().dataReport(new ICTIHttpCallback() {
            @Override
            public void onSuccess(CTIHttpAns ctiHttpAns) {

            }

            @Override
            public void onFailure(CTIHttpAns ctiHttpAns) {

            }

            @Override
            public void onStop(CTIHttpAns ctiHttpAns) {

            }
        });
    }
}
