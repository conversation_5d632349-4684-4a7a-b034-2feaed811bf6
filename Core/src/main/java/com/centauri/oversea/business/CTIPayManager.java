package com.centauri.oversea.business;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.SparseArray;
import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.TestConfig;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.business.h5.CTIWebActivity;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GDPR;
import com.centauri.oversea.comm.IabBroadcastReceiver;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.RestoreItem;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.api.ICTICallBack;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.centauri.oversea.business.pay.CTIResponse;
import com.centauri.oversea.business.pay.CTIProxyActivity;
import com.centauri.oversea.business.pay.CTIOrder;
import com.centauri.oversea.business.pay.ChannelHelper;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.comm.CTILog;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newnetwork.http.NetworkManager;

import org.json.JSONObject;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;

/**
 * Created by zachzeng on 2018/1/10.
 */

public class CTIPayManager {
    public static final String TAG = "APPayManager";
    public static final String ORDER_KEY = "order_key";

    private ChannelHelper mChannelHelper = null;    // Responsible for channel creation and management
    private SparseArray<CTIOrder> mOrders = null;    // Payment order data

    private boolean isBRRegister = false;     // Is the broadcast registered
    private LinkedList<RestoreItem> restores;   // The FIFO holds the channel that needs to be reissued, the channel that needs to be broadcast
    private ArrayList<String> promoRestoreKeys;
    private ICTICallback mRestoreCallBack = null;
    private ICTICallback seriesCallBack = null;
    private IabBroadcastReceiver mIabBroadcastReceiver;


    private CTIPayManager() {}

    static class InstanceHolder{
        static final CTIPayManager instance = new CTIPayManager();
    }

    public static CTIPayManager instance() {
        return InstanceHolder.instance;
    }


    /**
     * Internal data initialization
     * Avoid NULL after being released
     */
    public void init(){
        if(mChannelHelper == null) {
            mChannelHelper = new ChannelHelper();
        }

        if(mOrders == null) {
            mOrders = new SparseArray<CTIOrder>();
        }

        if(seriesCallBack == null) {
            seriesCallBack = new ICTICallback() {
                @Override
                public void callback(int retCode, String info) {
                    if (mRestoreCallBack != null) {
                        mRestoreCallBack.callback(retCode, info);
                    }

                    if (restores != null && !restores.isEmpty()) {
                        RestoreItem item = null;
                        try {
                            item = restores.poll();
                            if (item != null) {
                                CTILog.i(TAG, item.channel + " start reProvide.");
                                item.restore.restore(CTIPayNewAPI.singleton().getApplicationContext(), item.channel, this);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
//                            CTILog.i(TAG, item.channel + e.getMessage().toString());
                        }

                    } else {  //provide success
                        restores = null;
                        if (!isBRRegister) { //Determine if you need to register a broadcast
                            registerBR();
                        }
                    }
                }
            };
        }
    }

    /******************************************支付***********************************************/

    /**
     * pay
     * @param activity
     * @param request
     * @param callBack
     */
    public void pay(Activity activity, BillingFlowParams request, ICTICallBack callBack){
        int key = generateOrderKey();
        mOrders.put(key,new CTIOrder(request, callBack));

        if(mChannelHelper.isH5Channel(request.getPayChannel())){ //h5 pay
            Intent intent = new Intent(activity, CTIWebActivity.class);
            Bundle bundle = new Bundle();

            bundle.putBoolean("LogEnable", CTIPayNewAPI.singleton().isLogEnable());//log
            bundle.putString("PayChannel",request.getPayChannel());
            bundle.putInt("OrderKey",key);                      //Pass the order key and call back for external use

            if ("h5_mall".equals(request.getPayChannel())) {
                bundle.putString("debugUrl", TestConfig.mWebViewUrl);
                //支付参数
                HashMap<String,String> payParams = request.toMap();
                payParams.put("offerId",GlobalData.singleton().offerID);
                payParams.put("openId",GlobalData.singleton().openID);
                payParams.put("zoneId",GlobalData.singleton().zoneID);
                payParams.put("env",GlobalData.singleton().env);
                payParams.put("idc",GlobalData.singleton().IDC);
                bundle.putString("PayParams", CTITools.map2Js(payParams));
                intent.putExtras(bundle);
                activity.startActivity(intent);
            } else {
                BillingFlowParams.BillingFlowParamsExtra payExtra = request.getExtra();
                if (!TextUtils.isEmpty(payExtra.getChannelExtras())) {
                    String channelExtras = payExtra.getChannelExtras();
                    HashMap<String, String> channelExtrasMaps = CTITools.str2Map(channelExtras);
                    if (channelExtrasMaps.containsKey("pay_info")) {
                        try {
                            String jsonString = channelExtrasMaps.get("pay_info");
                            JSONObject jsonObject = new JSONObject(jsonString);
                            String packageName = CTIPayNewAPI.singleton().getApplicationContext().getPackageName();
                            String packageNameValidation = jsonObject.getJSONObject("data").getJSONObject("package_name_validation").getString("name");
                            CTILog.i(TAG, "packageName = "+ packageName + " packageNameValidation = " + packageNameValidation);
                            String[] packageNameArray = packageNameValidation.split(";");
                            boolean contains = Arrays.asList(packageNameArray).contains(packageName);
                            if (!contains) {
                                CTIResponse responseInfo = new CTIResponse(
                                        MRetCode.ERR_OTHER, "9003",
                                        "Package name verification failed.");
                                callBack.CentauriPayCallBack(responseInfo);
                                return;
                            }
//                            if (!packageName.equals(packageNameValidation)) {
//                                CTIResponse responseInfo = new CTIResponse(
//                                        MRetCode.ERR_OTHER, "9003",
//                                        "Package name verification failed.");
//                                callBack.CentauriPayCallBack(responseInfo);
//                                return;
//                            }
                            String redirect_url = jsonObject.getJSONObject("data").getJSONObject("encrypt_redirect_url").getString("redirect_url");
                            CTILog.i(TAG, "redirect_url = " + redirect_url);
                            String version = jsonObject.getJSONObject("data").getJSONObject("encrypt_redirect_url").getString("version");
                            CTILog.i(TAG, "version = " + version);
                            String PayStr = CTITools.AESString(redirect_url,version);
                            PayStr += "&is_sdk=1";
                            CTILog.i(TAG, "PayStr = " + PayStr);
                            bundle.putString("debugUrl", PayStr);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                //支付参数
                HashMap<String,String> payParams = request.toMap();
                payParams.put("idc",GlobalData.singleton().IDC);
                bundle.putString("PayParams", CTITools.map2Js(payParams));
                intent.putExtras(bundle);
                activity.startActivity(intent);
            }
        }else{
            MTimer.start(MTimer.SDK_PROCESS_ACTIVITY);
            Intent intent = new Intent(activity, CTIProxyActivity.class);
            intent.putExtra(ORDER_KEY,key);
            activity.startActivity(intent);
        }
    }


    //Get the order for the key
    public CTIOrder getOrder(int key){
        if(mOrders != null) {
            return mOrders.get(key);
        }

        CTILog.e(TAG,"getOrder(): order is null !!!");
        return null;
    }


    /**
     * Payments external callback 1: Logon invalidated
     * @param key
     */
    public void callBackLoginError(int key){
        CTILog.i(TAG,"callBackLoginError()");

        CTIOrder order = getOrder(key);
        if(order != null){
            order.callBack.CentauriPayNeedLogin();
            updateOrders(key);
        }

        reportData(-1,"CentauriPayNeedLogin");
    }


    /**
     * Payment callback 2: Payment failed
     * @param key
     * @param response
     */
    public void callBackError(int key, CTIResponse response){
        CTILog.i(TAG,"callBackError()");

        CTIOrder order = getOrder(key);
        if(order != null) {
            //When calling back to the business, add the pass-through field that was passed in when the external payment was made.
            response.setAppExtends(order.request.getExtra().getAppExtends());
            order.callBack.CentauriPayCallBack(response);
            updateOrders(key);
        }

        reportData(response.getResultCode(),response.getResultMsg());
    }


    /**
     * Payment callback 3: Payment successful
     * @param key
     * @param response
     */
    public void callBackSuccess(int key, CTIResponse response){
        CTILog.i(TAG,"callBackSuccess()");

        CTIOrder order = getOrder(key);
        CTILog.i(TAG,"order: " + order);
        if(order != null) {
            //When calling back to the business, add the pass-through field that was passed in when the external payment was made.
            response.setAppExtends(order.request.getExtra().getAppExtends());
            order.callBack.CentauriPayCallBack(response);
            updateOrders(key);
        }

        reportData(response.getResultCode(),response.getResultMsg());
    }


    /******************************************补发***********************************************/

    /**
     * re provide
     * @param callback
     */
    public void reProvide(ICTICallback callback){
        ChannelHelper channelHelper = channelHelper();  //Fetch via interface to avoid null
        ArrayList<String> restoreChannelList = channelHelper.restoreChannelList();
        if(restoreChannelList == null || restoreChannelList.isEmpty()){
            if(callback != null){
                callback.callback(MRetCode.OK,"don't need reProvide.");
            }
            return;
        }

        if(restores == null){
            restores = new LinkedList<RestoreItem>();
        }

        if(promoRestoreKeys == null){
            promoRestoreKeys = new ArrayList<String>();
        }

        for(String channelKey : restoreChannelList){
            CTIBaseRestore restore = channelHelper.createRestoreChannel(channelKey);
            if(restore != null){
                restores.offer(new RestoreItem(channelKey,restore));
                CTILog.i(TAG,"Need restore channel: "+channelKey);

                //The channel through which a broadcast needs to be reissued when it is received
                if(channelHelper.isPromoCodeChannel(channelKey)
                        && !promoRestoreKeys.contains(channelKey)){
                    promoRestoreKeys.add(channelKey);
                    CTILog.i(TAG,"BroadcastReceiver restore channel: "+channelKey);
                }
            }
        }

        if(!restores.isEmpty()) {
            mRestoreCallBack = callback;
            triggerRestore();
        }else if(callback != null){
            callback.callback(MRetCode.OK,"don't need reProvide.");
        }
    }


    //Trigger serial reissue
    private void triggerRestore(){
        if(restores != null && !restores.isEmpty()) {
            RestoreItem item = restores.poll();
            if(item != null) {
                CTILog.i(TAG, item.channel + " start reProvide.");
                item.restore.restore(CTIPayNewAPI.singleton().getApplicationContext(), item.channel, seriesCallBack);
            }
        }
    }


    //Sign up for a Google Play exchange code broadcast
    @SuppressLint("WrongConstant")
    private void registerBR(){
        isBRRegister = true;
        mIabBroadcastReceiver = new IabBroadcastReceiver(new IabBroadcastReceiver.IabBroadcastListener() {
            @Override
            public void receivedBroadcast() {
                CTILog.i(TAG, "Receiver received broadcast.");

                //Broadcasting is not processed in payment
                if(isAllPayFinished()
                        && promoRestoreKeys != null
                        && !promoRestoreKeys.isEmpty()){

                        if(restores == null){
                            restores = new LinkedList<>();
                        }

                        for(String channelKey : promoRestoreKeys){
                            RestoreItem item = new RestoreItem(channelKey, channelHelper().createRestoreChannel(channelKey));
                            restores.offer(item);
                        }

                        //Begin serial reissue
                        triggerRestore();
                }
            }
        });

        CTILog.i(TAG, "Register Receiver.");
        IntentFilter broadcastFilter = new IntentFilter(IabBroadcastReceiver.ACTION);
        if (Build.VERSION.SDK_INT >= 33) {
            CTILog.i(TAG, "Register Receiver.33+");
            CTIPayNewAPI.singleton().getApplicationContext().registerReceiver(mIabBroadcastReceiver, broadcastFilter, 0x4);
        } else {
            CTIPayNewAPI.singleton().getApplicationContext().registerReceiver(mIabBroadcastReceiver, broadcastFilter);
        }
    }

    public void clearPurchase(Context context) {
        IGetProduct getProduct = CTIPayManager.instance().channelHelper().createProductInfo("gwallet");
        getProduct.clearPurchase(context);
    }


    /****************************************Internal interfaces, data****************************************/

    public ChannelHelper channelHelper(){
        if(mChannelHelper == null){
            mChannelHelper = new ChannelHelper();
        }
        return  mChannelHelper;
    }

    public void release(){
        if(restores != null){
            restores.clear();
            restores = null;
        }

        if(promoRestoreKeys != null){
            promoRestoreKeys.clear();
            promoRestoreKeys = null;
        }

        mRestoreCallBack = null;
        seriesCallBack = null;
        mChannelHelper = null;

        if (mIabBroadcastReceiver != null) {
            CTIPayNewAPI.singleton().getApplicationContext()
                    .unregisterReceiver(mIabBroadcastReceiver);
            isBRRegister = false;
            mIabBroadcastReceiver = null;
            CTILog.i(TAG, "UnRegister GooglePlay BroadcastReceiver.");
        }
    }

    //Generate payment order key
    private int generateOrderKey(){
        if(mOrders != null){
            return mOrders.size()+1;
        }
        return 0;
    }

    //Are all payment orders completed
    private boolean isAllPayFinished(){
        if(mOrders != null){
            return mOrders.size() == 0;
        }
        return true;
    }

    //After the payment is completed, remove the payment order from the order data
    private void updateOrders(int key){
        if(mOrders != null) {
            mOrders.delete(key);
        }
    }

    //Report of payment result
    private void reportData(int code,String msg) {
        CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_OVERSEA_EXIT,
                "name=exitPay"
                +"&retCode="+code
                +"&retMsg="+msg);

        CTIDataReportManager.instance().insertData(CTIDataReportManager.PHONE_DEVICE,
                GDPR.getDeviceInfo(CTIPayAPI.singleton().getApplicationContext()));

        NetworkManager.singleton().dataReport(new ICTIHttpCallback() {
            @Override
            public void onStop(CTIHttpAns ans) {
                CTILog.e(TAG,"reportData() onStop.");
            }

            @Override
            public void onSuccess(CTIHttpAns ans) {
                CTIDataReportManager.instance().saveDataId(CTIPayNewAPI.singleton().getApplicationContext());
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                CTILog.e(TAG,"reportData() onFailure.");
            }
        });
    }


}
