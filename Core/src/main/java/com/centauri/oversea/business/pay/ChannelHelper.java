package com.centauri.oversea.business.pay;

import android.text.TextUtils;
import android.util.Log;

import com.centauri.comm.CTILog;
import com.centauri.oversea.business.CTIBaseIntroInfo;
import com.centauri.oversea.business.CTIBaseRestore;
import com.centauri.oversea.business.IGetProduct;
import com.centauri.oversea.comm.GlobalData;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


/**
 * Channel naming
 * Pay channels: prefix + name + APPay channels, such as: com. Centauri. Oversea. Business. Payhub. Gwallet. APPay
 * Replacement channels: prefix + + APRestore channel name, such as: com. Centauri. Oversea. Business. Payhub. Gwallet. APRestore
 * Query item information channels: prefix + name + APProductInfo channels, such as: com. Centauri. Oversea. Business. Payhub. Gwallet. APProductInfo
 * Created by zachzeng on 2018/1/10.
 */
public class ChannelHelper {
    private static final String TAG = "ChannelHelper";
    private static final String CHANNEL_NAME_PREFIXE = "com.centauri.oversea.business.payhub.";
    private static final String GOOGLE_PLAY = "gwallet";    //google play

    private static final int FLAG_NEED_RESTORE = 0x01;  // Whether the channel should be reissued
    private static final int FLAG_PROMO_CODE = 0x02;  // Does the channel support exchange codes
    private static final int FLAG_H5 = 0x04;          // Whether it is an H5 channel

    private static final HashMap<String, Integer> mChannels = new HashMap<String, Integer>();

    public ChannelHelper() {
        mChannels.put("gwallet", FLAG_NEED_RESTORE | FLAG_PROMO_CODE);
        mChannels.put("h5_mall", FLAG_H5);
        mChannels.put("os_mp", FLAG_H5);
        mChannels.put("os_midaspay", FLAG_H5);
        mChannels.put("os_tstore", FLAG_NEED_RESTORE);
        mChannels.put("os_amazon", FLAG_NEED_RESTORE);
        mChannels.put("os_garena", FLAG_NEED_RESTORE);
        mChannels.put("os_toy", FLAG_NEED_RESTORE);
//        mChannels.put("os_netmarble_promocode", FLAG_NEED_RESTORE);
        mChannels.put("os_netmarble_googleplay", FLAG_NEED_RESTORE);
        mChannels.put("os_samsung", FLAG_NEED_RESTORE);
        mChannels.put("os_onestore", FLAG_NEED_RESTORE);
        mChannels.put("os_xiaomi", FLAG_NEED_RESTORE);

        String hString = EncodeByXor("os_hu","encodeKey");
        String wString = EncodeByXor("awei","encodeKey");
        mChannels.put( DecodeByXor(hString + wString,"encodeKey"), FLAG_NEED_RESTORE);
    }


    //Need to reissue channel list
    public ArrayList<String> restoreChannelList() {
        ArrayList<String> restoreList = new ArrayList<String>();

        if (mChannels != null && !mChannels.isEmpty()) {
            for (Map.Entry<String, Integer> item : mChannels.entrySet()) {
                if ((item.getValue() & FLAG_NEED_RESTORE) != 0) {
                    restoreList.add(item.getKey());
                }
            }
        }

        return restoreList;
    }

    //is h5 channel
    public boolean isH5Channel(String channelId) {
        return mChannels.containsKey(channelId)
                && (mChannels.get(channelId) & FLAG_H5) != 0;
    }

    //Whether the channel is an exchange code
    public boolean isPromoCodeChannel(String channelId) {
        return mChannels.containsKey(channelId)
                && (mChannels.get(channelId) & FLAG_PROMO_CODE) != 0;
    }


    //Generate a reissue Channel instance
    public CTIBaseRestore createRestoreChannel(String channelId) {
        if (TextUtils.isEmpty(channelId)) {
            CTILog.e(TAG, "createRestoreChannel(): channelId is empty.");
            return null;
        }

        Object instance = createReflectObject(getChannelFullName(channelId, ".CTIRestore"));
        return instance == null ? null : (CTIBaseRestore) instance;
    }


    //Generate a payment Channel instance
    public CTIPayBaseChannel createPayChannel(String channelId) {
        if (TextUtils.isEmpty(channelId)) {
            CTILog.e(TAG, "createPayChannel(): channelId is empty.");
            return null;
        }

        Object instance = createReflectObject(getChannelFullName(channelId, ".CTIPay"));
        return instance == null ? null : (CTIPayBaseChannel) instance;
    }


    //Generate an item information query Channel instance
    public IGetProduct createProductInfo(String channelId) {
        if (TextUtils.isEmpty(channelId)) {
            CTILog.e(TAG, "createProductInfo(): channelId is empty.");
            return null;
        }

        Object instance = createReflectObject(getChannelFullName(channelId, ".CTIProductInfo"));
        return instance == null ? null : (IGetProduct) instance;
    }

    //Generate item discount information to query a Channel instance
    public CTIBaseIntroInfo createIntroInfoChannel(String channelId) {
        if (TextUtils.isEmpty(channelId)) {
            CTILog.e(TAG, "createProductInfo(): channelId is empty.");
            return null;
        }

        Object instance = createReflectObject(getChannelFullName(channelId, ".CTIIntroInfo"));
        return instance == null ? null : (CTIBaseIntroInfo) instance;
    }

    //Reflection Creation Instance
    private Object createReflectObject(String className) {
        try {
            CTILog.d(TAG, "createReflectObject = " + className);
            Class<?> channelName = Class.forName(className);
            return channelName.newInstance();
        } catch (Exception e) {
            CTILog.e(TAG, "createReflectObject(): reflect exception.");
        }

        return null;
    }

    //Generate channel pathname: prefix + channel name + suffix
    private static String getChannelFullName(String key, String suffix) {
        StringBuilder sb = new StringBuilder(CHANNEL_NAME_PREFIXE);

        //google play
        if (GOOGLE_PLAY.equals(key)) {
            sb.append(key);

            //Google Play Billing Library
            if (GlobalData.isGoogleNew) {
                CTILog.d("createChannel", "google new");
                sb.append(".New");
            } else {
                CTILog.d("createChannel", "google old");
            }
        } else {
            String tmp = key.replace('_', '.');
            sb.append(tmp.replace("os.", ""));
        }

        sb.append(suffix);
        return sb.toString();
    }

    /**
     * 异或加密
     * @param rawStr 原文
     * @param key 密钥
     * @return 密文
     */
    public static String EncodeByXor(String rawStr, String key) {
        // 通过字符串拿到密钥
        int number = 1;
        for (int i = 0; i < key.length(); i++) {
            number *= key.charAt(i);
        }

        // 转成字节数组
        byte[] rawData = rawStr.getBytes(StandardCharsets.UTF_8);
        byte[] encodeData = Xor(rawData, number);

        // 把字节数组转成某种格式的字符串，方便传输（格式可以自定义，好解析就行）
        StringBuilder encodeStr = new StringBuilder();
        for (byte b : encodeData) {
            encodeStr.append(b).append("x");
        }

        return encodeStr.toString();
    }

    /**
     * 异或解密
     * @param encodeStr 密文
     * @param key 密钥
     * @return 原文
     */
    public static String DecodeByXor(String encodeStr, String key) {
        // 通过字符串拿到密钥
        int number = 1;
        for (int i = 0; i < key.length(); i++) {
            number *= key.charAt(i);
        }

        // 解析EncodeByXor方法中的字节数组格式，找到加密后的字节数组
        String[] strings = encodeStr.substring(0,encodeStr.length()-1).split("x");
        byte[] rawData = new byte[strings.length];
        for (int i = 0; i < strings.length; i++) {
            rawData[i] = Byte.parseByte(strings[i]);
        }

        // 异或一下
        byte[] encodeData = Xor(rawData, number);

        // 重新编码成原始字符串
        return new String(encodeData, StandardCharsets.UTF_8);
    }

    private static byte[] Xor(byte[] rawData, int number) {
        byte[] encodeData = new byte[rawData.length];
        for (int i = 0; i < rawData.length; i++) //遍历字符数组
        {
            encodeData[i] = (byte) (rawData[i] ^ number); //对每个数组元素进行异或运算
        }
        return encodeData;
    }
}
