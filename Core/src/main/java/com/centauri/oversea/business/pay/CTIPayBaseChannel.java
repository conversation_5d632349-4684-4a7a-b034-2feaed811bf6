package com.centauri.oversea.business.pay;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Message;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.comm.CTIBase64;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.comm.MRetCode;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.CTIPayReceipt;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * MVP presenter
 * Created by zachzeng on 2018/1/11.
 */
public class CTIPayBaseChannel {
    private final String TAG = this.getClass().getSimpleName();

    public static final int REQUEST_CODE = 1001;
    public static final int RET_OK = 0;
    public static final String MSG_KEY = "msg_key";
    public static final String ORDER_KEY = "order_key";
    public static final String MSG_STR_OBJ = "msg_key";

    protected CTIPayBaseView mView = null;  //view
    protected CTIPayModel mModel = null;         //model
    protected boolean dropMessage = false;   // Identifies whether to discard the received message


    public void init(CTIPayBaseView view) {
        mView = view;
        mModel = new CTIPayModel();
        mModel.setProductType(getProductType());
        mModel.setHasGoodsList(hasGoodsList());
        mModel.setRequest(mView.getOrder().request);

        mModel.setCallback(new XCallback() {
            @Override
            public void notifyInner(Message msg) {
                if (UIHandler != null) {
                    UIHandler.sendMessage(msg);
                }
            }

            @Override
            public void notifyOuterLoginErr() {
                callBackLoginError();
            }

            @Override
            public void notifyOuterRiskErr(int retCode,  String msgErrCode, String message) {
                if (MRetCode.ERR_1145 == retCode ) {   //Ordering risk control
                    callBackError(new CTIResponse(MRetCode.ERR_ORDER_RISK,msgErrCode, message));
                }
            }
        });
    }


    //Start payment and check if it is sandbox environment
    public void startPayCheckEnv() {
        if (CTITools.isTestEnv()) {
            mView.showSandboxDialog();
        } else {
            startPay();
        }
    }


    //Begin to pay
    public void startPay() {
        dropMessage = false;
        UIHandler.sendEmptyMessage(MConstants.MSG_PAYCHANNEL_INIT);
    }


    //return orderKey
    public int getOrderKey() {
        return mView.getOrderKey();
    }

    public class HandlerImpl {
        //Receives the message
        public void sendMessage(Message msg) {
            handleMessage(msg);
        }

        public void sendEmptyMessage(int what) {
            Message msg = new Message();
            msg.what = what;
            handleMessage(msg);
        }

        // Processing all messages uniformly
        public boolean handleMessage(Message msg) {
            CTILog.d(TAG, "Thread id:" + Thread.currentThread().getName());
            if (dropMessage || mView == null || mModel == null) {
                callBackError(new CTIResponse(
                        MRetCode.ERR_OTHER,
                        "","handleMessage error"));
                return true;
            }
            String msgErrCode = msg.getData().getString("msgErrCode","");
            switch (msg.what) {
                case MConstants.MSG_PAYCHANNEL_INIT:
                    MTimer.start(MTimer.SDK_PROCESS_SHOWLOADING);
                    showLoading();
                    MTimer.stop(MTimer.SDK_PROCESS_SHOWLOADING);
                    init();
                    break;
                case MConstants.MSG_PAYCHANNEL_INIT_ERROR:
                    mModel.report("initErr", (String) msg.obj);
                    CTILog.i(TAG, "initErr");
                    handleCommError(msg);
                    break;
                case MConstants.MSG_PAYCHANNEL_INIT_SUCC:
                    mModel.report("initSucc", "");
                    CTILog.i(TAG, "initSuccess");
                    prePay();
                    break;
                case MConstants.MSG_PAYCHANNEL_PREPAY_ERROR:             //prepay
                    mModel.report("prepayErr", (String) msg.obj);
                    CTILog.i(TAG, "prepayErr");
                    handleCommError(msg);
                    break;
                case MConstants.MSG_PAYCHANNEL_PREPAY_SUCC:
                    mModel.report("prepaySucc", "");
                    CTILog.i(TAG, "prepaySuccess");
                    if (needOrder()) {
                        mModel.order(msg.obj);
                    } else {
                        pay(mView.getActivity(), null);
                    }
                    break;
                case MConstants.MSG_PAYCHANNEL_RECOVERY:
                    pay(mView.getActivity(), (JSONObject) msg.obj);
                    break;
                case MConstants.MSG_PAYCHANNEL_GET_ORDER_ERROR:          //order
                    mModel.report("orderErr", (String) msg.obj);
                    CTILog.i(TAG, "orderErr");
                    handleCommError(msg);
                    break;
                case MConstants.MSG_PAYCHANNEL_GET_ORDER_SUCC:
                    mModel.report("orderSucc", "");
                    CTILog.i(TAG, "orderSuccess");
                    pay(mView.getActivity(), mModel.getMInfo());
                    break;
                case MConstants.MSG_PAYCHANNEL_PAY_ERROR:                //pay
                    mModel.report("payErr", (String) msg.obj);
                    CTILog.i(TAG, "payErr");
                    handleCommError(msg);
                    break;
                case MConstants.MSG_PAYCHANNEL_PAY_TO_BE_PAID:
                    mModel.report("payToBePaid", (String) msg.obj);
                    CTILog.i(TAG, "payToBePaid");
                    handleCommError(msg);
                    break;
                case MConstants.MSG_PAYCHANNEL_PAY_SUCC:
                    mModel.report("paySucc", "");
                    CTILog.i(TAG, "paySuccess");
                    if (isSdkProvide()) {
                        if (msg.arg1 == 100) {
                            callBackSuccess(1);//The fourth mode of subscription upgrades
                        } else {
                            showLoading();
                            if (GlobalData.singleton().getGw_version().equals("5")) {
                                mModel.newProvide(msg.obj);
                            } else {
                                mModel.provide(msg.obj);
                            }
                        }
                    } else {
                        callBackSuccess(msg.arg1 == 0 ? mModel.getNum() : msg.arg1);
                    }
                    break;
                case MConstants.MSG_PAYCHANNEL_PROVIDE_ERROR:           //provide
                    if (mModel.isErrorConsume()) {
                        mModel.report("provideErr", "msg=errorConsume&billno=" + mModel.getBillNo() + "&receipt=" + mModel.getProvideReceipt().receipt);
                        postPay();
                    } else {
                        mModel.report("provideErr", "msg=" + (String) msg.obj + "&billno=" + mModel.getBillNo() + "&receipt=" + mModel.getProvideReceipt().receipt);
                        CTILog.i(TAG, "provideErr");
                        onSaveReceipt(mModel.getProvideReceipt());
                        handleCommError(msg);
                    }
                    break;
                case MConstants.MSG_PAYCHANNEL_PROVIDE_SUCC:
                    mModel.report("provideSucc", "");
                    CTILog.i(TAG, "provideSuccess");
                    postPay();
                    break;
                case MConstants.MSG_PAYCHANNEL_POSTPAY_ERROR:         //postPay
                    mModel.report("postpayErr", (String) msg.obj);
                    CTILog.i(TAG, "postPayErr");
                    callBackSuccess(msg.arg1 == 0 ?
                            mModel.getNum() : msg.arg1);
                    break;
                case MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC:
                    if (mModel.isErrorConsume()) {
                        mModel.report("errorConsume", "");
                        CTILog.i(TAG, "errorConsume");
                        boolean showErrDialog = true;

                        showErrDialog = CTICommMethod.isConfigShowErrDialog(mView.getActivity());
                        CTILog.d(TAG, "showErrDialog is " + showErrDialog);

                        if (showErrDialog) {
                            callBackErrorWithUI(mModel.getProvideRetMes(), new CTIResponse(
                                    MRetCode.ERR_OTHER,
                                    msgErrCode,mModel.getProvideRetMes()));
                        } else {
                            try {
                                callBackError(new CTIResponse(Integer.parseInt(mModel.getProvideSdkRet()),
                                        msgErrCode,mModel.getProvideRetMes()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                callBackError(new CTIResponse(MRetCode.ERR_OTHER,
                                        msgErrCode,mModel.getProvideRetMes()));
                            }
                        }
                    } else {
                        mModel.report("postpaySucc", "");
                        CTILog.i(TAG, "postPaySuccess");
                        callBackSuccess(msg.arg1 == 0 ? mModel.getNum() : msg.arg1);
                    }
                    break;
                case MConstants.MSG_PAYCHANNEL_CANCEL:               //cancel
                    CTILog.i(TAG, "payCancel");
                    mModel.report("cancel", (String) msg.obj);
                    callBackError(new CTIResponse(
                            msg.arg1 != 0 ? msg.arg1 : MRetCode.ERR_CANCEL,  msgErrCode,(String) msg.obj));
                    break;
                case MConstants.MSG_PAYCHANNEL_PAY_UNKNOWN:          //unKnow error
                    mModel.report("unknown", "");
                    CTILog.i(TAG, "payUnknown");
                    callBackError(new CTIResponse(
                            MRetCode.ERR_OTHER,
                            msgErrCode,"unknow result,check later"));
                    break;
                case MConstants.MSG_COMM_LOGIN_ERROR:
                    mModel.report("loginInvalid", "");
                    CTILog.i(TAG, "login expired");
                    callBackLoginError();
                    break;
            }
            return true;
        }
    }

    public final HandlerImpl UIHandler = new HandlerImpl();


    //release
    public void release() {
        if (mModel != null) {
            mModel.release();
            mModel = null;
        }
        mView = null;
    }


    /***********************************ApBaseActivity is called back to the business*****************************/
    //response
    private void wrapperResponse(CTIResponse response) {
        if (response != null && !TextUtils.isEmpty(response.getExtra())) {
            try {
                JSONObject js = addChannelExtra(new JSONObject(response.getExtra()));
                response.setExtra(js);
            } catch (JSONException e) {
                CTILog.e(TAG, "wrapperResponse exception: " + e.getMessage());
            }
        }
    }

    private void callBackLoginError() {
        if (mView != null) {
            mView.callBackLoginError();
        }
        dispose();
    }

    private void callBackError(CTIResponse response) {
        wrapperResponse(response);
        if (mView != null) {
            mView.callBackError(response);
        }
        dispose();
    }

    private void callBackErrorWithUI(String msg, CTIResponse response) {
        wrapperResponse(response);
        if (mView != null) {
            mView.showErrorMsg(msg, response);
        }
        dispose();
    }

    private void callBackSuccess(int num) {
        CTIResponse response = new CTIResponse(MRetCode.OK);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("channel", mModel.getChannelId());
            jsonObject.put("num", num);
            jsonObject.put("billNo", mModel.getBillNo());

            //1、SDK order information, Base64 encryption
            String payInfo = mModel.getPayInfo();
            if (!TextUtils.isEmpty(payInfo)) {
                jsonObject.put("info", CTIBase64.encode(payInfo.getBytes()));
            }

            //edit by zach.don't return google play receipt
//            String provideReceipt = mModel.getProvideReceipt().toString();
//            if(!TextUtils.isEmpty(provideReceipt)) {
//                jsonObject.put("receipt", provideReceipt);
//            }

            //2、Channel extensions are included with Extra
            response.setExtra(addChannelExtra(jsonObject));
        } catch (JSONException e) {
            e.printStackTrace();
        }

        //Whether to display the payment success result page
        response.needShowSuccess = needShowSucc();
        mView.callBackSuccess(response);
        dispose();
    }


    /**********************************The subclass selects the override method*****************************************/

    protected void init() {
        UIHandler.sendEmptyMessage(MConstants.MSG_PAYCHANNEL_INIT_SUCC);
    }

    public void prePay() {
        UIHandler.sendEmptyMessage(MConstants.MSG_PAYCHANNEL_PREPAY_SUCC);
    }

    public void pay(Activity act, JSONObject obj) {
    }

    public void postPay() {
        UIHandler.sendEmptyMessage(MConstants.MSG_PAYCHANNEL_POSTPAY_SUCC);
    }

    // The channel adds additional content to the callback extras
    protected JSONObject addChannelExtra(JSONObject in) {
        return in;
    }

    // Get the item type
    protected String getProductType() {
        return "";
    }

    // The channel does not have a list of items, currently only mol_pin does not
    protected boolean hasGoodsList() {
        return true;
    }

    // Do you need to place an order? There is no need to place an order for some specific channels, but only need rice master to pull up and pay
    protected boolean needOrder() {
        return true;
    }

    // Save the notes. Some specific channels need to save the notes locally
    protected void onSaveReceipt(CTIPayReceipt receipt) {
    }

    // Whether you need to display a success page after a successful purchase
    protected boolean needShowSucc() {
        return true;
    }

    // Whether to ship through SDK
    protected boolean isSdkProvide() {
        return false;
    }

    // Google Play needs to retrieve the data returned by the payment
    public boolean handleActivityResult(int requestCode, int resultCode, Intent data) {
        return false;
    }

    //Clearing up channel resources
    protected void dispose() {
        CTILog.i(TAG, "dispose()");
        if (mView != null) {
            mView.dismissWaitDialog();
        }
        dropMessage = true;
    }


    /*************************************Internal methods**********************************************/

    //show loading
    private void showLoading() {
        if (mView != null) {
            mView.showWaitDialog();
        }
    }


    //Processing error messages uniformly
    private void handleCommError(Message msg) {
        String obj = (String) msg.obj;
        int arg1 = msg.arg1 == 0 ? MRetCode.ERR_OTHER : msg.arg1;//Msg. arg1 is not set. Default is 0

        // Check the manifest to see if you need to display the popup
        boolean showErrDialog = true;

        if (mView != null){
            showErrDialog = CTICommMethod.isConfigShowErrDialog(mView.getActivity());
            CTILog.d(TAG, "showErrDialog is " + showErrDialog);
        }

        String msgErrCode = msg.getData().getString("msgErrCode","");
        String retCode = msg.getData().getString("retCode","");
        String copywriteFormat = msg.getData().getString("copywrite_format","");
        String copywriteParamsList = msg.getData().getString("copywrite_params_list","");

        if (TextUtils.isEmpty(obj)) {
            String showMes = CTICommMethod.getStringId(mView.getActivity(), "unipay_pay_error_tip");
            callBackError(new CTIResponse(arg1,  msgErrCode,showMes));
        } else if (showErrDialog) {
            if (retCode.equals(String.valueOf(MRetCode.ERR_1138))) {
                callBackErrorWithUI(obj, new CTIResponse(arg1, msgErrCode, copywriteFormat, copywriteParamsList, obj));
            } else {
                callBackErrorWithUI(obj, new CTIResponse(arg1, msgErrCode, obj));
            }
        } else {
            // 增加风控返回值(中了风控时，将风控变量透传给业务)
            if (retCode.equals(String.valueOf(MRetCode.ERR_1138))) {
                callBackError(new CTIResponse(arg1, msgErrCode, copywriteFormat, copywriteParamsList, obj));
            } else {
                callBackError(new CTIResponse(arg1, msgErrCode, obj));
            }
        }
    }
}
