package com.centauri.oversea.business.h5;

import android.app.Activity;
import android.os.Build;
import android.os.Bundle;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JsResult;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.centauri.oversea.business.h5.url.IH5;
import com.centauri.oversea.business.h5.url.UrlFactory;
import com.centauri.oversea.business.h5.webview.MUrlIntercept;
import com.centauri.oversea.business.h5.webview.MWebView;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.comm.CTILog;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.comm.MUIManager;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newapi.params.InitParams;

/**
 * WebView the child process
 * <AUTHOR>
 */
public class CTIWebActivity extends Activity{
    public static final String TAG = "CTIWebActivity";

    private int orderKey = 0;  // main process order key

    private IH5 h5 = null;
    private AIDLHandler aidlHandler = null;
    private MUrlIntercept mUrlIntercept = null;

    private FrameLayout wvContainer = null;            // the WebView container
    private RelativeLayout refreshLayout = null;       // Request failed, refresh layout
    private TextView refreshContent = null;            // Request failed, display content
    private MWebView mWebView = null;
    private MUIManager uiManager = null;               // Centauri general UI management
    private String mDebugUrl = "";                     //Debug Url for webview
    private String payChannel = "";
    private ProgressBar mProgressBar = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CTILog.d(TAG, "onCreate()");
        Bundle bundle = getIntent().getExtras();
        if(bundle != null) {
            payChannel = bundle.getString("PayChannel");
        }

        setContentView(CTICommMethod.getLayoutId(this, "unipay_abroad_layout_h5"));
        refreshLayout = findViewById(CTICommMethod.getId(this, "unipay_id_h5_refresh_layout"));
        refreshContent = findViewById(CTICommMethod.getId(this, "unipay_id_h5_err_content"));
        wvContainer = findViewById(CTICommMethod.getId(this, "unipay_id_h5_webview_container"));
        mProgressBar = findViewById(CTICommMethod.getId(this, "progressBar"));

        if (!"os_mp".equals(payChannel) && !"os_midaspay".equals(payChannel)) {
            // In Android P, two WebView processes access the same directory crash
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                try {
                    CTILog.i(TAG, "setDataDirectorySuffix_buy");
                    WebView.setDataDirectorySuffix(CTITools.getCentuarimds() + "buy" + CTITools.getProcessName(this));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else if ("os_mp".equals(payChannel) || "os_midaspay".equals(payChannel)) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                try {
                    CTILog.i(TAG, "setDataDirectorySuffix_mp");
                    WebView.setDataDirectorySuffix(CTITools.getCentuarimds() + "mp" + CTITools.getProcessName(this));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        MTimer.start(MTimer.SDK_WEBVIEW_LOAD);
        MTimer.start(MTimer.SDK_WEBVIEW_DOM_LOAD);
        MTimer.start(MTimer.SDK_WEBVIEW_PROCESS_LOAD);
        mWebView = new MWebView(this);   //fixbug ApplicationContext can not support UI selection components。
        mWebView.setLayoutParams(new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        wvContainer.addView(mWebView);

        //add intercept
        mUrlIntercept = new MUrlIntercept();
        mWebView.setIntercept(mUrlIntercept);
        mWebView.setPayChannel(payChannel);

        getAidlParams();
        setListeners();
        uiManager = new MUIManager(this);

        //Create AIDL communication
        aidlHandler = new AIDLHandler(this);
        aidlHandler.setAIDLListener(aidlListener);
        aidlHandler.bindService();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if(mWebView.canGoBack()){
                mWebView.goBack();
            }else{
                callbackAndDestroy();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        CTILog.i(TAG,"onDestroy()");
//        uiManager.dismissWaitDialog();
        aidlHandler.release();

        if(mWebView != null){
            ViewGroup parent = (ViewGroup)mWebView.getParent();
            if(parent != null){
                parent.removeView(mWebView);

                mWebView.clearHistory();
                mWebView.clearCache(true);
                mWebView.clearFormData();
                mWebView.clearSslPreferences();
                mWebView.clearView();
                mWebView.destroy();
                mWebView = null;
            }
        }
        Process.killProcess(Process.myPid());

        super.onDestroy();
    }

    //AIDL Connected Listener
    private AIDLHandler.AIDLListener aidlListener = new AIDLHandler.AIDLListener() {
        @Override
        public void OnServiceConnected() {
            //set up ip
            if(h5.ifSetLocalCacheIP()) {
                h5.setLocalCacheIP(aidlHandler.queryCacheIP(h5.getHost()));
            }

            //loading，cancel callback，finish activity
//            uiManager.showLoading(new MUIManager.MNotifier() {
//                @Override
//                public void callback() {
//                    CTILog.d(TAG,"cancel show loading...");
//                    callbackAndDestroy();
//                }
//            });

            MTimer.stop(MTimer.SDK_WEBVIEW_PROCESS_LOAD);
            MTimer.start(MTimer.SDK_WEBVIEW_PAGE_LOAD);

            if (!TextUtils.isEmpty(getDebugModeUrl(mDebugUrl))){
                CTILog.d(TAG,"debug loadUrl url: "+mDebugUrl);
                mWebView.loadUrl(mDebugUrl);
            }else{
                CTILog.d(TAG,"release loadUrl url: "+h5.getUrl(getApplicationContext()));
                mWebView.loadUrl(h5.getUrl(getApplicationContext()));

                //test demo
//                mWebView.loadUrl("http://www.asfawdfadfsdfdfs.com");
            }

        }
    };

    //get aidl params
    private void getAidlParams(){
        Bundle bundle = getIntent().getExtras();
        if(bundle != null){
            boolean logEnable = bundle.getBoolean("LogEnable");
            orderKey = bundle.getInt("OrderKey");
            String jsPayParams = bundle.getString("PayParams");//The pay parameter passed by the main process in JSON format

//            webProcessInit(jsPayParams);

            mDebugUrl = bundle.getString("debugUrl");
            CTIPayNewAPI.singleton().setLogEnable(logEnable);

            h5 = UrlFactory.create(payChannel);
            if(h5 != null){
                //set aidl params for H5
                h5.setJsResource(jsPayParams);
                h5.setPayUrl(mDebugUrl);
            }

            //set aidl params for Intercept
            mUrlIntercept.setJsResource(jsPayParams);
        }
    }

    private void setListeners(){
        //WebViewClient Listener
        mWebView.setWebViewClientListener(new MWebView.WebViewClientListener() {
            @Override
            public void onRequestError(String errMsg) {
//                uiManager.dismissWaitDialog();
                wvContainer.setVisibility(View.INVISIBLE);
                refreshLayout.setVisibility(View.VISIBLE);
                mProgressBar.setVisibility(View.GONE);
                if(!TextUtils.isEmpty(errMsg)){
                    refreshContent.setText(errMsg);
                }
            }

            @Override
            public void onPageFinished(WebView view, String url){
                CTILog.d(TAG, "setListeners onPageFinished");
            }

            @Override
            public void onPageStarted(WebView view, String url) {

            }
        });

        //WebChromeClient Listener
        mWebView.setWebChromeClientListener(new MWebView.WebChromeClientListener() {

            @Override
            public void onJsAlert(String url, String message, JsResult result) {
                h5.onJsAlert(message);
                //update intercept rules
                mUrlIntercept.setInterceptItems(h5.getInterceptItems());
            }

            @Override
            public void onProgressChanged(int progress) {
                Log.d(TAG,"progress: "+progress);
                if(progress == 100){
                    mProgressBar.setVisibility(View.GONE);
                }else {
                    mProgressBar.setVisibility(View.VISIBLE);
                    mProgressBar.setProgress(progress);
                }
            }

            @Override
            public void onMpDestory(int retCode) {
                Log.d(TAG,"retCode: "+retCode);
                callbackAndMpDestroy(retCode);
            }
        });

        //Close onClick Listener
        findViewById(CTICommMethod.getId(this,"unipay_id_h5_close")).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                callbackAndDestroy();
            }
        });

        //RefreshLayout onClick Listener
        refreshLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                wvContainer.setVisibility(View.VISIBLE);
                refreshLayout.setVisibility(View.INVISIBLE);

                if (!TextUtils.isEmpty(getDebugModeUrl(mDebugUrl))){
                    CTILog.d(TAG,"loadUrl url: "+mDebugUrl);
                    mWebView.loadUrl(mDebugUrl);
                }else {
                    CTILog.d(TAG,"loadUrl url: "+h5.getUrl(getApplicationContext()));
                    mWebView.loadUrl(h5.getUrl(getApplicationContext()));
                }
            }
        });
    }

    //Calling back to the main process, the notification ends
    private void callbackAndDestroy(){
        if(h5 != null) {
            aidlHandler.onResponse(orderKey, h5.getRetCode(),h5.getRetMsg());
        }else{
            aidlHandler.onResponse(orderKey, -1,"");
        }
        this.finish();
    }

    //Calling back to the main process, the notification ends
    private void callbackAndMpDestroy(Integer retCode){
        if(h5 != null) {
            aidlHandler.onResponse(orderKey, retCode,h5.getRetMsg());
        }else{
            aidlHandler.onResponse(orderKey, -1,"");
        }
        this.finish();
    }
    /**
     * check the debug situation
     * @param debugUrl
     * @return  if in debug mode ,url is not empty  else url is empty
     */
    private String  getDebugModeUrl(String debugUrl){

        String url =  "";
        CTILog.d(TAG,"!!!webview debug mode  isApkInDebug：" + CTITools.isApkInDebug(getApplicationContext()));
        CTILog.d(TAG,"!!!webview debug mode  debugUrl：" + debugUrl);

        /**
         * support the custom url for debugging。
         */
        if(CTITools.isApkInDebug(getApplicationContext())
                && !TextUtils.isEmpty(debugUrl)){
            url =debugUrl;
            CTILog.d(TAG,"!!!webview debug mode  custom url is: "+ url);
        }else {
            url = "";
        }
        return url;
    }

    public void webProcessInit(String jsonSrc){
        String env = CTITools.getJsonValue(jsonSrc, "env");
        String idc = CTITools.getJsonValue(jsonSrc, "idc");
        String offerId = CTITools.getJsonValue(jsonSrc, "offerId");
        String openId = CTITools.getJsonValue(jsonSrc, "openId");
        String zoneId = CTITools.getJsonValue(jsonSrc, "zoneId");


        CTIPayNewAPI.singleton().setApplicationContext(this);
        InitParams initParams = new InitParams.Builder()
                .setEnv(env)
                .setIDC(idc)
                .setOfferID(offerId)
                .setOpenID(openId)
                .setZoneID(zoneId)
                .build();
        GlobalData.singleton().init(initParams);
    }

}
