package com.centauri.oversea.data;

import java.util.ArrayList;
import java.util.List;

// 此类是为发货 req 提供参数的一个类，不单单有票据信息
public class CTIPayReceipt {
    public List<String> sku;
    public String receipt;
    public String receipt_sig;
    public String orderId;
    public String productId;
    public String payChannel;

    // 新增票据以外参数
    public String first_currency_type;  //支付前币种
    public String second_currency_type; //支付后币种
    public String second_amt;           //支付后金额

    // GW5.1新增参数
    public String basePlanId;
    public String gw_version;
    public String gw_pricingPhases;
    public String gw_reprovide_version;


    public CTIPayReceipt() {
        sku = new ArrayList<>();
        receipt = "";
        receipt_sig = "";
        orderId = "";
        productId = "";
        first_currency_type = "";
        second_currency_type = "";
        second_amt = "";
        basePlanId = "";
        gw_version = "";
        gw_pricingPhases = "";
        gw_reprovide_version = "";
    }

    @Override
    public String toString() {
        return receipt;
    }
}
