package com.centauri.oversea.newnetwork.http;

import android.content.Context;
import android.text.TextUtils;

import com.centauri.http.core.Request;
import com.centauri.http.core.Response;
import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.CTIHttpRequest;
import com.centauri.http.centaurihttp.CTINetworkManager;
import com.centauri.http.centaurihttp.ICTIDataReportNotifier;
import com.centauri.http.centaurihttp.ICTICommonInfoGetter;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.api.ICTINetCallBack;
import com.centauri.oversea.api.request.CTIProductRequest;
import com.centauri.oversea.comm.CTIDataReportManager;
import com.centauri.comm.CTILog;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.centauri.oversea.newapi.params.InitParams;
import com.centauri.oversea.newapi.params.NetParams;
import com.centauri.oversea.newapi.response.GetProductCallback;
import com.centauri.oversea.newapi.response.NotifyCallback;
import com.centauri.oversea.newnetwork.model.CTIDataReportAns;
import com.centauri.oversea.newnetwork.model.CTIDataReportReq;
import com.centauri.oversea.newnetwork.model.CTIDetectAns;
import com.centauri.oversea.newnetwork.model.CTIDetectRequest;
import com.centauri.oversea.newnetwork.model.CTIEndGetIPInterceptor;
import com.centauri.oversea.newnetwork.model.CTIEndGetKeyInterceptor;
import com.centauri.oversea.newnetwork.model.CTIFrontGetIPInterceptor;
import com.centauri.oversea.newnetwork.model.CTIHttpsIPDirectHandler;
import com.centauri.oversea.newnetwork.model.CTIIntroPriceAns;
import com.centauri.oversea.newnetwork.model.CTIIntroPriceReq;
import com.centauri.oversea.newnetwork.model.CTIMpAns;
import com.centauri.oversea.newnetwork.model.CTIMpReq;
import com.centauri.oversea.newnetwork.model.CTIOverSeaCommAns;
import com.centauri.oversea.newnetwork.model.CTIOverSeaCommReq;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * Created by zachzeng on 2017/12/27.
 * Connect new network module
 */

public class NetworkManager {
    public static final String TAG = "NetworkManager";

    public static final String CMD_GET_KEY = "get_key";
    public static final String CMD_GET_IP_LIST = "get_ip";
    public static final String CMD_INFO = "info";
    public static final String CMD_ORDER = "order";
    public static final String CMD_PROVIDE = "provide";
    public static final String CMD_TAG = "overseas_cmd";
    private CTINetworkManager networkManager;


    private NetworkManager() {
        initNewNetworkModule();
    }

    static class InstanceHolder {
        static NetworkManager instance = new NetworkManager();
    }

    //Single case
    public static NetworkManager singleton() {
        return InstanceHolder.instance;
    }


    //Initialize the new network module
    private void initNewNetworkModule() {
        NewNetLog log = new NewNetLog();
        log.setLogEnable(false); //Do not print the internal non-error log of the network module

        networkManager = new CTINetworkManager(log);
        networkManager.setBaseKey(GlobalData.singleton().getBaseKey());
        networkManager.setIV(GlobalData.singleton().getIV());
        networkManager.setContext(CTIPayNewAPI.singleton().getApplicationContext());
        networkManager.addFistInterceptor(new CTIFrontGetIPInterceptor());//Execute getIp before each network request
        networkManager.addLastInterceptor(new CTIEndGetIPInterceptor());//Process the get_ip result to avoid processing in each network request
        networkManager.addLastInterceptor(new CTIEndGetKeyInterceptor(networkManager));//Process the get_key result

        networkManager.setCentauriCommonInfoGetter(new ICTICommonInfoGetter() {

            @Override
            public String getHttpHostHeaderDomain(CTIHttpRequest request) {
                String host = "";

                if(request instanceof CTIDataReportReq
                        || request instanceof CTIDetectRequest){
                    host = request.getHost();
                }else{
                    host = GlobalData.singleton().getHost();
                }

                CTILog.d(TAG,"getHttpHostHeaderDomain host: "+host);
                return host;
            }

            @Override
            public String getSdkVersion() {
                return GlobalData.SDK_VERSION;
            }
        });

        networkManager.setDataReportNotifier(new ICTIDataReportNotifier() {
            @Override
            public void onNetworkSuccess(Request request, Response response) {
                if (request == null || response == null) {
                    return;
                }

                NetWorker.sendReportData(request, 0, 200, "", response);
            }

            @Override
            public void onNetworkFailure(Request request, Response response) {
                if (request == null || response == null) {
                    return;
                }

                String exception = "";
                if (response.exception != null) {
                    exception = response.exception.toString();
                }

                NetWorker.sendReportData(
                        request,
                        CTITools.getErrorTypeFromException(response.exception),
                        CTITools.getResponseCodeForDataReport(response),
                        exception,
                        response);
            }
        });

        //Set up ip connection under https
        networkManager.addHttpHandler(new CTIHttpsIPDirectHandler());
        networkManager.setup();
    }


    //save key
    public void saveKeyInfo(String offerId, String openId, String secretKey, String cryptoKey, String keyTime) {
        String sdkVersion = GlobalData.SDK_VERSION;
        Context context = CTIPayNewAPI.singleton().getApplicationContext();

        networkManager.setSecretKeyToRam(openId, offerId, secretKey);
        networkManager.setCryptKeyToRam(openId, offerId, cryptoKey);
        networkManager.setKeyTimeToRam(openId, offerId, keyTime);
        networkManager.saveSecretKeyToDisk(context, openId, offerId, secretKey, sdkVersion);
        networkManager.saveCryptKeyToDisk(context, openId, offerId, cryptoKey, sdkVersion);
        networkManager.saveKeyTimeToDisk(context, openId, offerId, keyTime, sdkVersion);
        CTILog.i(TAG,"save key success.");
    }

    //Get payment encryption key
    public String getCryToKey(String offerId,String openId){
        return  networkManager.getCryptKeyFromRam(openId, offerId);
    }


    public String readKeyTime(String offerId, String openId) {
        return networkManager.getKeyTimeFromRam(openId, offerId);
    }


    //need to change key
    public boolean needChangeKey(String offerId,String openId){
        ICTICommonInfoGetter commonInfoGetter = networkManager.getCentauriCommonInfoGetter();
        if(commonInfoGetter == null){
            CTILog.e(TAG,"needChangeKey: commonInfoGetter = null");
            return false;
        }

        String sdkVersion =  commonInfoGetter.getSdkVersion();
        return networkManager.needChangeKey(CTIPayNewAPI.singleton().getApplicationContext(),openId,offerId,sdkVersion);
    }


    /*************************************** net connect ********************************************/

    public void introPriceReq(String channel, HashMap<String, String> pMap, ICTIHttpCallback callback){

        Collection<String> valueCollection = pMap.keySet();
        List<String> plist = new ArrayList(valueCollection);

        CTIIntroPriceReq req = new CTIIntroPriceReq()
                .setChannel(channel)
                .setQueryProductList(plist)
                .setUp();

        networkManager.executeRequestAsync(req,new CTIIntroPriceAns(callback));
    }

    /**
     * During initialization, determine whether to obtain get_ip and get_key information
     * @param request
     * @param callBack
     */
    public void initReq(InitParams request, final NotifyCallback callBack){
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setRequest(NetworkReqParams.switchParams(request))
                .setUp();

        MTimer.start(String.valueOf(req.hashCode()));   //Hash value is used as key value to avoid parallel overwriting
        ICTIHttpCallback ICTIHttpCallback = new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ans) {
                reportTime(ans,"Succ");
                if(callBack !=  null){
                    callBack.onFinish();
                }
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                reportTime(ans,"Fail");
                if(callBack !=  null){
                    callBack.onFinish();
                }
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                reportTime(ans,"Stop");
                if(callBack !=  null){
                    callBack.onFinish();
                }
            }
        };

        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(ICTIHttpCallback));
    }

    public void getProductReq(HashMap<String, String> sku, final GetProductCallback callBack){
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setSku(sku)
                .setProduct();

        MTimer.start(String.valueOf(req.hashCode()));   //Hash value is used as key value to avoid paralleCl overwriting
        ICTIHttpCallback ICTIHttpCallback = new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ans) {
                reportTime(ans,"Succ");
                if(callBack !=  null){
                    callBack.onSuccess(ans);
                }
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                reportTime(ans,"Fail");
                if(callBack !=  null){
                    callBack.onFailure(ans);
                }
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                reportTime(ans,"Stop");
                if(callBack !=  null){
                    callBack.onStop(ans);
                }
            }
        };

        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(ICTIHttpCallback));
    }

    public void getProductReq(List<String> unifiedSkuLists, final GetProductCallback callBack){
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setUnifiedProductIds(unifiedSkuLists)
                .setProduct();

        MTimer.start(String.valueOf(req.hashCode()));   //Hash value is used as key value to avoid paralleCl overwriting
        ICTIHttpCallback ICTIHttpCallback = new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ans) {
                reportTime(ans,"Succ");
                if(callBack !=  null){
                    callBack.onSuccess(ans);
                }
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                reportTime(ans,"Fail");
                if(callBack !=  null){
                    callBack.onFailure(ans);
                }
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                reportTime(ans,"Stop");
                if(callBack !=  null){
                    callBack.onStop(ans);
                }
            }
        };

        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(ICTIHttpCallback));
    }

    public void getPromotionReq(List<String> unifiedSkuLists, final GetProductCallback callBack){
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setUnifiedProductIds(unifiedSkuLists)
                .setPromotion();

        MTimer.start(String.valueOf(req.hashCode()));   //Hash value is used as key value to avoid paralleCl overwriting
        ICTIHttpCallback ICTIHttpCallback = new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ans) {
                reportTime(ans,"Succ");
                if(callBack !=  null){
                    callBack.onSuccess(ans);
                }
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                reportTime(ans,"Fail");
                if(callBack !=  null){
                    callBack.onFailure(ans);
                }
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                reportTime(ans,"Stop");
                if(callBack !=  null){
                    callBack.onStop(ans);
                }
            }
        };

        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(ICTIHttpCallback));
    }
    /**
     * Marketing activity 1: Get short code
     * @param reqType "get_short_openid"
     * @param request
     * @param callBack
     */
    public void startSecInfo(final String reqType, NetParams request, final ICTINetCallBack callBack) {
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setCmd(NetworkManager.CMD_INFO)
                .setRequest(NetworkReqParams.switchParams(request))
                .setInfoType(reqType).setUp();

        MTimer.start(String.valueOf(req.hashCode()));   //Hash value is used as key value to avoid parallel overwriting
        networkManager.executeRequestAsync(req, new CTIOverSeaCommAns(new ICTIHttpCallback() {
            @Override
            public void onSuccess(CTIHttpAns ans) {
                reportTime(ans,"Succ");

                if (ans.getResultCode() == 0
                        && (ans instanceof CTIOverSeaCommAns)) {
                    JSONObject ret = new JSONObject();
                    try {
                        ret.put("ret", 0);
                        ret.put("err_code", "0");
                        ret.put("msg", new JSONObject(((CTIOverSeaCommAns) ans).getInfoMsg()));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                    if(callBack != null) {
                        callBack.CentauriNetFinish(reqType, ret.toString());
                    }
                } else {
                    if(callBack != null) {
                        callBack.CentauriNetError(reqType, ans.getResultCode(), ans.getResultMessage());
                    }
                }
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                reportTime(ans,"Fail");
                if(callBack != null) {
                    callBack.CentauriNetError(reqType, ans.getResultCode(), ans.getResultMessage());
                }
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                reportTime(ans,"Stop");
                if(callBack != null) {
                    callBack.CentauriNetStop(reqType);
                }
            }
        }));
    }

    /**
     * Marketing activity 2: Pull marketing information
     * @param reqType "mp_info"
     * @param request
     * @param callBack
     */
    public void net(final String reqType, NetParams request, final ICTINetCallBack callBack) {
        CTIMpReq req = new CTIMpReq()
                .setRequest(NetworkReqParams.switchParams(request))
                .setHttpRequestKey(reqType)
                .setUp();

        MTimer.start(String.valueOf(req.hashCode()));   //Hash value is used as key value to avoid parallel overwriting
        networkManager.executeRequestAsync(req, new CTIMpAns(new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ans) {
                reportTime(ans,"netSucc");
                if (ans.getResultCode() == 0
                        && (ans instanceof CTIMpAns)) {
                    JSONObject ret = new JSONObject();
                    try {
                        ret.put("ret", 0);
                        ret.put("err_code", "0");
                        ret.put("msg", new JSONObject(((CTIMpAns) ans).getMpJson()));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                    if(callBack != null) {
                        callBack.CentauriNetFinish(reqType, ret.toString());
                    }
                } else {
                    if(callBack != null) {
                        callBack.CentauriNetError(reqType, ans.getResultCode(), ans.getResultMessage());
                    }
                }
//                if(ans instanceof CTIMpAns) {
//                    CTIMpAns mpAns = (CTIMpAns) ans;
//                    if(callBack != null) {
//                        callBack.CentauriNetFinish(((CTIHttpRequestBase) ans.getCentauriHttpRequest()).getHttpRequestKey(), mpAns.getMpJson());
//                    }
//                }else{
//                    if(callBack != null) {
//                        callBack.CentauriNetFinish(reqType, "");
//                    }
//                }
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                reportTime(ans,"netFail");
                if(callBack != null) {
                    callBack.CentauriNetError(((CTIHttpRequestBase) ans.getCentauriHttpRequest()).getHttpRequestKey(),
                            ans.getResultCode(), ans.getResultMessage());
                }
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                reportTime(ans,"netStop");
                if(callBack != null) {
                    callBack.CentauriNetStop(((CTIHttpRequestBase) ans.getCentauriHttpRequest()).getHttpRequestKey());
                }
            }
        }));
    }

    //order network
    public void getOrder(
            String pay_method,
            String gw_version,
            String basePlanId,
            String gw_pricingPhases,
            String currency_type,
            String payCurrencyType,
            String payAmt,
            String service_code,
            String buy_quantity,
            String amt,
            String channelExtra,
            BillingFlowParams request,
            final ICTIHttpCallback callBack) {
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setCmd(NetworkManager.CMD_ORDER)
                .setServiceCode(service_code)
                .setCurrencyType(currency_type)
                .setPayCurrencyType(payCurrencyType)
                .setAmt(amt)
                .setPayMethod(pay_method)
                .setChannelExtra(channelExtra)
                .setPayAmount(payAmt)
                .setBuyQuantity(buy_quantity)
                .setIsReProvide(false)
                .setRequest(NetworkReqParams.switchParams(request))
                .setGwVersion(gw_version)
                .setGwpricingPhases(gw_pricingPhases)
                .setBasePlanId(basePlanId)
                .setOneStop("pay")
                .setUp();
        MTimer.start(String.valueOf(req.hashCode()));
        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Succ");
                if(callBack != null) {
                    callBack.onSuccess(ctiHttpAns);
                }
            }

            @Override
            public void onFailure(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Fail");
                if(callBack != null) {
                    callBack.onFailure(ctiHttpAns);
                }
            }

            @Override
            public void onStop(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Stop");
                if(callBack != null) {
                    callBack.onStop(ctiHttpAns);
                }
            }
        }));
    }

    //provide network
    public void provide(boolean isReProvide,
                        String gw_version,
                        String gw_reprovide_version,
                        String currency_type,
                        String pay_method,
                        String num,
                        String billno,
                        String receipt,
                        String receipt_openid,
                        String receipt_sign,
                        BillingFlowParams request,
                        CTIPayReceipt provideParams,
                        final ICTIHttpCallback callBack) {
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setCmd(NetworkManager.CMD_PROVIDE)
                .setCurrencyType(currency_type)
                .setPayMethod(pay_method)
                .setBillNO(billno)
                .setReceipt(receipt)
                .setReceiptOpenID(receipt_openid)
                .setReceiptSign(receipt_sign)
                .setIsReProvide(isReProvide)
                .setGwReporideVersion(gw_reprovide_version)
                .setNum(num)
                .setRequest(NetworkReqParams.switchParams(request))
                .setProvideParams(provideParams)
                .setGwVersion(gw_version)
                .setOneStop("provide")
                .setUp();

        MTimer.start(String.valueOf(req.hashCode()));
        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Succ");
                if(callBack != null) {
                    callBack.onSuccess(ctiHttpAns);
                }
            }

            @Override
            public void onFailure(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Fail");
                if(callBack != null) {
                    callBack.onFailure(ctiHttpAns);
                }
            }

            @Override
            public void onStop(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Stop");
                if(callBack != null) {
                    callBack.onStop(ctiHttpAns);
                }
            }
        }));
    }

    //Gw5.1 provide network
    public void newProvide(boolean isReProvide,
                        String currency_type,
                        String basePlanId,
                        String gw_pricingPhases,
                        String gw_version,
                        String pay_method,
                        String num,
                        String billno,
                        String receipt,
                        String receipt_openid,
                        String receipt_sign,
                        BillingFlowParams request,
                        CTIPayReceipt provideParams,
                        final ICTIHttpCallback callBack) {
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setCmd(NetworkManager.CMD_PROVIDE)
                .setCurrencyType(currency_type)
                .setPayMethod(pay_method)
                .setBillNO(billno)
                .setReceipt(receipt)
                .setReceiptOpenID(receipt_openid)
                .setReceiptSign(receipt_sign)
                .setIsReProvide(isReProvide)
                .setNum(num)
                .setRequest(NetworkReqParams.switchParams(request))
                .setProvideParams(provideParams)
                .setBasePlanId(basePlanId)
                .setGwVersion(gw_version)
                .setGwpricingPhases(gw_pricingPhases)
                .setOneStop("provide")
                .setUp();

        MTimer.start(String.valueOf(req.hashCode()));
        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(new ICTIHttpCallback() {

            @Override
            public void onSuccess(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Succ");
                if(callBack != null) {
                    callBack.onSuccess(ctiHttpAns);
                }
            }

            @Override
            public void onFailure(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Fail");
                if(callBack != null) {
                    callBack.onFailure(ctiHttpAns);
                }
            }

            @Override
            public void onStop(CTIHttpAns ctiHttpAns) {
                reportTime(ctiHttpAns,"Stop");
                if(callBack != null) {
                    callBack.onStop(ctiHttpAns);
                }
            }
        }));
    }
    //get mall
    public void getMall(ICTIHttpCallback callback){
        CTIOverSeaCommReq req = new CTIOverSeaCommReq()
                .setCmd(NetworkManager.CMD_INFO)
                .setUp();
        networkManager.executeRequestAsync(req,new CTIOverSeaCommAns(callback));
    }

    //net request
    public void request(CTIHttpRequest request, CTIHttpAns ans){
        networkManager.executeRequestAsync(request, ans);
    }

    //cancel all request
    public void cancelPreRequest() {
        networkManager.cancelAllRequest();
    }

    //Time consumption for reporting network requests
    private void reportTime(CTIHttpAns ans, String tag) {
        CTIHttpRequestBase req = (CTIHttpRequestBase)ans.getCentauriHttpRequest();
        MTimer.stop(String.valueOf(req.hashCode()));

        String extendTag = req.getCmd() + tag;
        if(!TextUtils.isEmpty(extendTag)){
            extendTag.replace("|","");

            CTIDataReportManager.instance().insertData(
                    CTIDataReportManager.SDK_COMM_NET_TIME,
                    "name="+extendTag
                            +"&times="+MTimer.duration(String.valueOf(req.hashCode()))
                            +"&retCode="+ans.getResultCode());
        }

    }

    //data report
    public void dataReport(ICTIHttpCallback callback) {
        ArrayList<String> log = new ArrayList<String>();
        int num = CTIDataReportManager.instance().getLogRecord(log);

        for (int i = 0; i < num; i++) {
            CTILog.i(TAG, "report data: " + log.get(i));
            CTIDataReportReq req = new CTIDataReportReq()
                    .setData(log.get(i))
                    .setUp();
            if(req.needReport()){
                CTILog.d("isNeedReport", "needreport");
                networkManager.executeRequestAsync(req,new CTIDataReportAns(callback));
            }
        }
    }

    //Probe task query
    public void detectTaskQuery(ICTIHttpCallback callback) {
        CTIDetectRequest request = new CTIDetectRequest();
        request.setUp();
        CTIDetectAns ans = new CTIDetectAns(callback);
        networkManager.executeRequestAsync(request, ans);
    }
}
