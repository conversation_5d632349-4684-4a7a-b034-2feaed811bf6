package com.centauri.oversea.newnetwork.model;

import com.centauri.http.core.HttpURL;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.newnetwork.http.CTIHttpRequestBase;
import java.util.ArrayList;
import java.util.List;


/**
 * Discount information
 * <AUTHOR>
 */
public class CTIIntroPriceReq extends CTIHttpRequestBase {
    public static final String TAG = "APIntroPriceReq";
    private List<String> _pList = new ArrayList<String>();
    private String _channel = "";

    @Override
    protected void initUrl() {
        String env = GlobalData.singleton().env;
        if ("release".equals(env)) {
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS, MConstants.INTRO_PRICE_CGI);
        }else{
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS, MConstants.INTRO_PRICE_CGI);
        }
    }

    //External settings query item id list
    public CTIIntroPriceReq setQueryProductList(List<String> productList){
        if(productList != null && !productList.isEmpty()){
            _pList.addAll(productList);
        }

        return this;
    }

    //Set the channel name
    public CTIIntroPriceReq setChannel(String channel){
        this._channel = channel;
        return this;
    }

    @Override
    protected void constructParam() {
        addHttpParameters("cmd", "query_user_eligibility");
        addHttpParameters("query_type","all");
        addHttpParameters("channel", _channel);
        addHttpParameters("offer_id", GlobalData.singleton().offerID);
        addHttpParameters("openid", GlobalData.singleton().openID);
        addHttpParameters("openid", GlobalData.singleton().openID);
        addHttpParameters("openkey", GlobalData.singleton().openKey);
        addHttpParameters("pf", GlobalData.singleton().pf);
        addHttpParameters("pfkey", GlobalData.singleton().pfKey);
        addHttpParameters("session_id", GlobalData.singleton().sessionID);
        addHttpParameters("session_type", GlobalData.singleton().sessionType);
        //Pass any value to avoid cgi check
        addHttpParameters("encrypt_msg", "0");
        addHttpParameters("msg_len", "0");

        //Item id list, urlEncode
        if(_pList!= null && !_pList.isEmpty()){
            StringBuilder _strList = new StringBuilder();
            for(String item : _pList)
                _strList.append(item).append(",");
            _strList.deleteCharAt(_strList.length()-1);

            addHttpParameters("productid", _strList.toString());
        }
    }


    public CTIIntroPriceReq setUp(){
        initUrl();
        constructParam();
        return this;
    }
}
