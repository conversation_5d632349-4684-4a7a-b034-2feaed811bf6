package com.centauri.oversea.newnetwork.model;

import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.http.core.Response;
import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.TestConfig;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.CTIDataReportManager;
//import com.centauri.oversea.comm.CTIToolAES;
import com.centauri.http.centaurikey.CTIToolAES;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newnetwork.http.CTIHttpRequestBase;
import com.centauri.oversea.newnetwork.http.NetworkManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

/**
 * Created by zachzeng on 2017/12/29.
 */

public class CTIOverSeaCommAns extends CTIHttpAns {
    public static final String TAG = "APOverSeaCommAns";

    private String order_info;
    private String billno;
    private JSONObject info;
    private String currency_amt;
    private String currency_type;
    private String offer_name;
    private String product_name;
    private String num;
    private String verifyRes = "";   //Return the result when verifying the bill
    private String infoMsg = "";
    private String mallMpInfo = ""; //The top of the mall page is full of gift marketing information
    private String provideSdkRet = ""; //Delivery sdk_ret content


    /********************mock test*************************/
    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }


    public CTIOverSeaCommAns(ICTIHttpCallback callback) {
        super(callback);
    }


    public String getProvideSdkRet() {
        return provideSdkRet;
    }

    public String getVerifyRes() {
        return verifyRes;
    }

    public String getBillno() {
        return billno;
    }

    public JSONObject getInfo() {
        return info;
    }

    public String getAmount() {
        return currency_amt;
    }

    public String getCurrentType() {
        return currency_type;
    }

    public String getProductName() {
        return product_name;
    }

    public String getNum() {
        return num;
    }

    public String getMallMpInfo() {
        return mallMpInfo;
    }

    public String getOfferName() {
        return offer_name;
    }

    public String getInfoMsg() {
        return infoMsg;
    }

    public String getOrderInfo() {
        return order_info;
    }

    //net result 200
    @Override
    protected boolean handleSuccess(Response response) {
        progressJson(response.responseBody);
        return true;
    }


    @Override
    protected boolean handleFailure(Response response) {
        CTILog.e(TAG, "handleFailure(..): request failed.");
        return true;
    }


    @Override
    protected boolean handleStop(Response response) {
        CTILog.e(TAG, "handleStop(..): request stop.");
        return true;
    }

    //Analysis result
    private void progressJson(String jsonData) {
        try {
            String cmd = getCentauriHttpRequest().getParameter(NetworkManager.CMD_TAG);
            CTILog.i(TAG, "CMD: " + cmd + ", resultData: " + jsonData);

            JSONObject jObj = new JSONObject(jsonData);

            if (cmd.contains(NetworkManager.CMD_GET_KEY)) { //get_key request
                analyseGetKey(jObj.getJSONObject(NetworkManager.CMD_GET_KEY));
            }

            if (cmd.contains(NetworkManager.CMD_INFO)) {//info request
                analyseInfo(jObj.getJSONObject(NetworkManager.CMD_INFO));
            }

            if (cmd.contains(NetworkManager.CMD_ORDER)) {//Order request
                analyseOrder(jObj.getJSONObject(NetworkManager.CMD_ORDER));
            }

            if (cmd.contains(NetworkManager.CMD_PROVIDE)) {//Delivery request
                analyseProvider(jObj.getJSONObject(NetworkManager.CMD_PROVIDE));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //Decryption returns a string
    private String decodeString(String encodeStr) {
        if (TextUtils.isEmpty(encodeStr)) {
            return "";
        }

        int index = encodeStr.indexOf("_");
        String context = encodeStr.substring(0, index);
        String len = encodeStr.substring(index + 1, encodeStr.length());
        int finalLen = Integer.parseInt(len);
        return CTIToolAES.doDecode(context, ((CTIHttpRequestBase) getCentauriHttpRequest()).getDecodeKey());
    }


    private void analyseGetKey(JSONObject jKeyInfo) {
        if (jKeyInfo.length() == 0) {
            return;
        }

        int retCode = Integer.parseInt(jKeyInfo.optString("ret"));
        if (retCode == 0) {
            String keyInfo = jKeyInfo.optString("key_info");
            String keyInfoLen = jKeyInfo.optString("key_info_len");

            //Use basekey to decrypt the key information
            String keys = CTIToolAES.doDecode(keyInfo, GlobalData.singleton().getBaseKey());
            keys = keys.substring(0, Integer.valueOf(keyInfoLen));
            HashMap<String, String> kvKeys = CTITools.kv2Map(keys);

            //Exchange the key overseas, exchange the unified baseKey for encryption and payment key
            String secKey = kvKeys.get("key");
            String cryKey = kvKeys.get("cryptkey");
            String keyTime = kvKeys.get("cryptkeytime");

            String openID = getCentauriHttpRequest().getOpenIDFromRequest();
            String offerID = getCentauriHttpRequest().getOfferIDFromRequest();
            NetworkManager.singleton().saveKeyInfo(offerID, openID, secKey, cryKey, keyTime);
        } else {
            CTILog.i(TAG, "get_key retCode is not equals zero.");
        }
    }

    //Processing info information
    private void analyseInfo(JSONObject obj) {
        if (obj.length() == 0) {
            return;
        }

        try {
            resultCode = Integer.parseInt(obj.getString("ret"));
            if (resultCode == 0) {

                if (obj.has("mp_info")) {
                    JSONObject mp_info = null;
                    if (!TextUtils.isEmpty(TestConfig.getCurTestMpInfo())) {
                        mp_info = new JSONObject(TestConfig.getCurTestMpInfo());
                    } else {
                        mp_info = obj.getJSONObject("mp_info");
                    }
                    if (mp_info.has("buycurrency")) {//首充满赠营销活动
                        mallMpInfo = mp_info.toString();
                    }
                }

                //save session_token
                if (obj.has("session_token")) {
                    GlobalData.singleton().setNetToken(obj.optString("session_token"));
                }

                infoMsg = decodeString(obj.getString("rsp_msg"));
                //  infoHandleMsg(new JSONObject(infoMsg));

            } else {
                resultMsg = obj.optString("msg");
                setMsgErrCode(obj.optString("complete_err_code"));
                String strError = obj.optString("err_code");
                if (!strError.equals("")) {
                    resultMsg = CTICommMethod.getStringId(CTIPayNewAPI.singleton().getApplicationContext(),
                            "unipay_pay_busy")
                            + "\n(" + strError + ")";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setMsgErrCode(String msgErrCode) {
            this.msgErrorCode= msgErrCode;
    }


    //analyse order
    private void analyseOrder(JSONObject obj) {
        try {
            if (obj == null || obj.length() == 0) {
                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_NETWORK_CALL_RESPONSE, "name=order&result=null");
                return;
            }
            // Increase the reporting of the order process
            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_NETWORK_CALL_RESPONSE, "name=order&result=" + obj.optString("ret") + "&info=" + obj.toString());


            resultCode = Integer.parseInt(obj.optString("ret"));
            if (resultCode == 0) {
                //{"billno":"202007212000175082","currency_amt":"8000000","currency_type":"HKD","productid":"testsub2","offer_name":"%E6%B5%B7%E5%A4%96%E6%94%AF%E4%BB%98DEMO_HK","product_name":"%B0%B4%D6%DC%B6%A9%D4%C4%C4%DA%C8%DD","num":"7","openid":"1234567890","use_plugin":"0","info":{"mol_appcode":"","channel_key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkh9XyCSVSLcyfNWV/fr6+ulxLJDGCbVQzxQgC7pidbRNCBWVB2wP3Sh+cwURVKnsiqnAIxiCoE8XApXsdK67X2rV9dBrXYu13j2hIZFOH6ZJHq+7TK21rRFBvLryPqBb2cX7tK9tsWY34mBPLq5yb592qKodKDmgBB9awJhdrlulsNegraTOVyTSlxb2EaFA7iFsc/RWqPS5IPQHhcFolXrLIXgguZigGrSMMG3Rc5/0UEy4LR6b73HWsN770t6U8oYlj68EGfANapMyGEoh/V28ZgobUIfIp5PUEXGQ9culCnwBhK1jylI98MwSlYy46jjYkLkcAJgRXZnwZE9/TwIDAQAB","boku_serviceid":"","project_key":"","uid":"","widget":"","api_type":"","secret_key":"","ag_external_id":"","ag_name":"","ag_type":"","sign_version":"","success_url":"","pingback_url":"","evaluation":"","sign":"","auth_code":"","pay_url":"","fwd_url":"","sdkret":"", "channel_info":"", "user_name":"FF8968ECF3654E071A6A73AFB26EC57C"}}
                String encMsg = decodeString(obj.optString("rsp_msg"));
                orderHandleMsg(new JSONObject(encMsg));
            } else {
                resultMsg = obj.optString("msg");
                setMsgErrCode(obj.optString("complete_err_code"));
                String strError = obj.optString("err_code");
                if (!strError.equals("")) {
                    resultMsg = CTICommMethod.getStringId(CTIPayNewAPI.singleton().getApplicationContext(), "unipay_pay_busy") + "(" + strError + ")";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //analyse provide
    private void analyseProvider(JSONObject obj) {

        // 1307-0-5014
        // 1307-0-5017
        try {
            if (obj == null || obj.length() == 0) {
                CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_NETWORK_CALL_RESPONSE, "name=provide&result=null");
                return;
            }
            // Increase the reporting of the order process
            CTIDataReportManager.instance().insertData(CTIDataReportManager.SDK_NETWORK_CALL_RESPONSE, "name=provide&result=" + obj.optString("ret") + "&info=" + obj.toString());

            resultCode = Integer.parseInt(obj.getString("ret"));

            //new logic,edit by zach
            parseProvideRspMsg(obj);



            if (resultCode != 0) {
                setMsgErrCode(obj.optString("complete_err_code"));
                String strError = obj.optString("err_code");
                resultMsg = obj.optString("msg");

                if (!strError.equals("")) {
                    resultMsg = CTICommMethod.getStringId(CTIPayNewAPI.singleton().getApplicationContext(), "unipay_pay_busy") + "(" + strError + ")";
                }
            }

//            if (resultCode == 0) {
//                parseVerifyRes(obj);
//            } else {
//                //5017、1138强制销票据，读取结果
//                if (resultCode == 1138 || resultCode == 5017) {
//                    parseVerifyRes(obj);
//                }
//
//                // 5020 表示兑换使用超限，这种情况需要消耗票据，并且返回给游戏
//                if (resultCode == 5020){
//                    parseVerifyRes(obj);
//                }
//
//                resultMsg = obj.optString("msg");
//                String strError = obj.optString("err_code");
//                if (!strError.equals("")) {
//                    resultMsg = APCommMethod.getStringId(CTIPayNewAPI.singleton().getApplicationContext(), "unipay_pay_busy") + "\n(" + strError + ")";
//                }
//            }
        } catch (Exception e) {
            CTILog.e(TAG, "analyseProvider Exception: " + e.getMessage());
        }
    }


    private void orderHandleMsg(JSONObject obj) {
        try {
            if (obj.has("order_info")) {
                this.order_info = obj.getString("order_info");
            }

            if (obj.has("billno")) {
                this.billno = obj.getString("billno");
            }

            if (obj.has("info")) {
                this.info = obj.getJSONObject("info");

                CTILog.d(TAG, "orderHandleMsg info:" + this.info);
                GlobalData.singleton().setUserName("");
                if (this.info.has("user_name")) {
                    GlobalData.singleton().setUserName(this.info.getString("user_name"));
                }
            }

            if (obj.has("currency_amt")) {
                this.currency_amt = obj.getString("currency_amt");
            }

            if (obj.has("currency_type")) {
                this.currency_type = obj.getString("currency_type");
            }

            if (obj.has("num")) {
                this.num = obj.getString("num");
            }

            if (obj.has("product_name")) {
                this.product_name = CTITools.urlDecode(obj.getString("product_name"), 1);
            }

            if (obj.has("offer_name")) {
                this.offer_name = CTITools.urlDecode(obj.getString("offer_name"), 1);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //Netmarble billing information
    private void parseProvideRspMsg(JSONObject obj) {
        try {
            String encMsg = decodeString(obj.optString("rsp_msg"));
            CTILog.d(TAG, "parseProvideRspMsg: " + encMsg);

            JSONObject jObject = new JSONObject(encMsg);
            if (jObject.has("verify_res")) {
                verifyRes = jObject.getJSONObject("verify_res").toString();
            }
            if (jObject.has("sdk_ret")) {
                provideSdkRet = jObject.getJSONObject("sdk_ret").toString();
            }
        } catch (Exception e) {

        }
    }
}
