package com.centauri.oversea.newnetwork.model;

import android.content.Context;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.http.centaurihttp.CTIEncodeKey;
import com.centauri.http.centaurikey.CTIToolAES;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.comm.CTICommMethod;
import com.centauri.oversea.comm.CTIMD5;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GDPR;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.data.CTIPayReceipt;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.centauri.oversea.newnetwork.http.CTIHttpRequestBase;
import com.centauri.oversea.newnetwork.http.NetworkManager;
import com.centauri.oversea.newnetwork.http.NetworkReqParams;

import org.json.JSONArray;
import org.json.JSONObject;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Currency;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

/**
 * Overseas merger request
 * Created by zachzeng on 2017/12/29.
 */
public class CTIOverSeaCommReq extends CTIHttpRequestBase {
    public static final String TAG = "APOverSeaCommReq";

    //info
    private String service_code;
    private String userip;
    private String currency_type;
    private String infoType = "";    //Type when obtaining marketing information
    private String oneStop;

    //order
    private String amt;
    private String pay_method;
    private String payAmount;
    private String buy_quantity;

    //provider
    private String billno;
    private String receipt;
    private String receipt_openid;
    private String receipt_sign;
    private boolean isReProvide = false;
    private String num;
    private CTIPayReceipt provideParams;

    private String payCurrencyType;
    private String basePlanId;
    private String gw_version; //5
    private String gw_pricingPhases;
    private String gw_reprovide_version; //5

    private HashMap<String, String> sku;
    private List<String> unifiedProductIds;
    //payInfo channelExtra
    private String channelExtra;
//    private String tokenId = "";

    public CTIOverSeaCommReq(){
        super();
    }

    public CTIOverSeaCommReq setCmd(String cmd) {
        this.cmd = cmd;
        return this;
    }

    /**
     * Note that request must be passed in from outside!!!
     * @param request
     * @return
     */
    public CTIOverSeaCommReq setRequest(NetworkReqParams request){
        this.request = request;
        return this;
    }

    public CTIOverSeaCommReq setProvideParams(CTIPayReceipt provideParams){
        this.provideParams = provideParams;
        return this;
    }

    public CTIOverSeaCommReq setInfoType(String infoType) {
        this.infoType = infoType;
        return this;
    }

    public CTIOverSeaCommReq setChannelExtra(String channelExtra) {
        this.channelExtra = channelExtra;
        return this;
    }

    public String getChannelExtra() {
        return channelExtra;
    }

    public CTIOverSeaCommReq setServiceCode(String serviceCode) {
        this.service_code = serviceCode;
        return this;
    }

    public CTIOverSeaCommReq setUserIp(String userip) {
        this.userip = userip;
        return this;
    }

    public CTIOverSeaCommReq setCurrencyType(String currencyType) {
        this.currency_type = currencyType;
        return this;
    }

    public CTIOverSeaCommReq setPayCurrencyType(String payCurrencyType) {
        this.payCurrencyType = payCurrencyType;
        return this;
    }

    public CTIOverSeaCommReq setBasePlanId(String basePlanId) {
        this.basePlanId = basePlanId;
        return this;
    }

    public CTIOverSeaCommReq setGwVersion(String gw_version) {
        this.gw_version = gw_version;
        return this;
    }

    public CTIOverSeaCommReq setGwpricingPhases(String gw_pricingPhases) {
        this.gw_pricingPhases = gw_pricingPhases;
        return this;
    }

    public CTIOverSeaCommReq setGwReporideVersion(String gw_reprovide_version) {
        this.gw_reprovide_version = gw_reprovide_version;
        return this;
    }

    public CTIOverSeaCommReq setOneStop(String oneStop) {
        this.oneStop = oneStop;
        return this;
    }

    public CTIOverSeaCommReq setAmt(String amt) {
        this.amt = amt;
        return this;
    }

    public CTIOverSeaCommReq setPayMethod(String payMethod) {
        this.pay_method = payMethod;
        return this;
    }

    public CTIOverSeaCommReq setPayAmount(String payAmount) {
        this.payAmount = payAmount;
        return this;
    }

    public CTIOverSeaCommReq setBuyQuantity(String buyQuantity) {
        this.buy_quantity = buyQuantity;
        return this;
    }

    public CTIOverSeaCommReq setBillNO(String billno) {
        this.billno = billno;
        return this;
    }

    public CTIOverSeaCommReq setReceipt(String receipt) {
        this.receipt = receipt;
        return this;
    }

    public CTIOverSeaCommReq setReceiptOpenID(String receiptOpenID) {
        this.receipt_openid = receiptOpenID;
        return this;
    }

    public CTIOverSeaCommReq setReceiptSign(String receiptSign) {
        this.receipt_sign = receiptSign;
        return this;
    }


    public CTIOverSeaCommReq setIsReProvide(boolean isReProvide) {
        this.isReProvide = isReProvide;
        return this;
    }

    public CTIOverSeaCommReq setNum(String num) {
        this.num = num;
        return this;
    }

    public CTIOverSeaCommReq setSku(HashMap<String, String> sku) {
        this.sku = sku;
        return this;
    }

    public CTIOverSeaCommReq setUnifiedProductIds(List<String> unifiedProductIds) {
        this.unifiedProductIds = unifiedProductIds;
        return this;
    }

    //Set the parameters, and finally call the method
    public CTIOverSeaCommReq setUp(){ 
        initUrl();
        constructParam();
        ifChangeKey(); //Merge the request, determine whether the key needs to be changed
        return this;
    }

    public CTIOverSeaCommReq setProduct(){
        initProductUrl();
        constructProductParam();
        return this;
    }

    public CTIOverSeaCommReq setPromotion(){
        initPromotionUrl();
        constructPromotionParam();
        return this;
    }

    protected void constructProductParam() {

        addHttpHeader("Content-Type", "application/json");
        addHttpParameters("isNewOfficial", "1");
        addHttpParameters("app_id", GlobalData.singleton().offerID);
        addHttpParameters("sdk_version", "androidoversea_v" + GlobalData.singleton().SDK_VERSION);
        JSONObject keyObject = new JSONObject();
        JSONObject basekeyObject = new JSONObject();
        try {
            basekeyObject.put("vid", "cti_1.0.1");
            keyObject.put("base_key", basekeyObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        addHttpKeyParameters("key", keyObject);

        String paymentMethod = GlobalData.singleton().payChannel;
        String projectId = GlobalData.singleton().provideAppID;
        String platformType = "android";
        String userId = GlobalData.singleton().openID;

        String regionCode = GlobalData.singleton().regionCode;
        String currencyCode = GlobalData.singleton().currencyCode;

        JSONArray productList = new JSONArray(unifiedProductIds);
        JSONObject conditionInfoObject = new JSONObject();
        JSONObject encryptMsgObject = new JSONObject();
        try {
            conditionInfoObject.put("region_code", regionCode);
            conditionInfoObject.put("currency_code", currencyCode);
            conditionInfoObject.put("unified_product_id_list", productList);

            encryptMsgObject.put("payment_method", paymentMethod);
            encryptMsgObject.put("project_id", projectId);
            encryptMsgObject.put("platform_type", platformType);
            encryptMsgObject.put("user_id", userId);
            encryptMsgObject.put("condition", conditionInfoObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String gBaseAesKey = "34asdS5WEls2SD23SFS282ASD5sf23SF";
        String gIv = "1234567890123456";

        String encryptMsg = CTIToolAES.encryptAES(encryptMsgObject.toString(), gBaseAesKey, gIv);
        addHttpParameters("encrypt_msg", encryptMsg);
        addHttpParameters("msg_len", String.valueOf(encryptMsg.length()));

//        JSONObject payloadObject = new JSONObject();
//        addHttpParameters("payload", payloadObject.toString());
    }

    protected void constructPromotionParam() {

        addHttpHeader("Content-Type", "application/json");
        addHttpParameters("isNewOfficial", "1");
        addHttpParameters("app_id", GlobalData.singleton().offerID);
        addHttpParameters("sdk_version", "androidoversea_v" + GlobalData.singleton().SDK_VERSION);
        JSONObject keyObject = new JSONObject();
        JSONObject basekeyObject = new JSONObject();
        try {
            basekeyObject.put("vid", "cti_1.0.1");
            keyObject.put("base_key", basekeyObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        addHttpKeyParameters("key", keyObject);
        addHttpParameters("provide_number", GlobalData.singleton().openID);

        String platformType = "android";
        String providerId = GlobalData.singleton().provideAppID;
        JSONArray productList = new JSONArray(unifiedProductIds);

        JSONObject encryptMsgObject = new JSONObject();
        try {
            encryptMsgObject.put("platform_type", platformType);
            encryptMsgObject.put("provider_app_id", providerId);
            encryptMsgObject.put("product_id_list", productList);
            encryptMsgObject.put("user_id", GlobalData.singleton().openID);
            encryptMsgObject.put("provide_number", GlobalData.singleton().openID);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String gBaseAesKey = "34asdS5WEls2SD23SFS282ASD5sf23SF";
        String gIv = "1234567890123456";

        String encryptMsg = CTIToolAES.encryptAES(encryptMsgObject.toString(), gBaseAesKey, gIv);
        addHttpParameters("encrypt_msg", encryptMsg);
        addHttpParameters("msg_len", String.valueOf(encryptMsg.length()));
    }

    @Override
    protected void constructParam() {
        if(request == null){
            CTILog.e(TAG,"Request is null !!!");
            return;
        }

        super.constructParam();
        //GW5.1
//        addHttpParameters("offerToken", GlobalData.singleton().offerToken);
        addHttpParameters("openid", GlobalData.singleton().openID);
        addHttpParameters("pf", GlobalData.singleton().pf);
        addHttpParameters("pfkey", GlobalData.singleton().pfKey);
        addHttpParameters("zoneid", GlobalData.singleton().zoneID);
        addHttpParameters("format", "json");
        addHttpParameters("key_len", "newkey");
        addHttpParameters("key_time", NetworkManager.singleton().readKeyTime(
                GlobalData.singleton().offerID, GlobalData.singleton().openID));
        addHttpParameters("session_token", GlobalData.singleton().getNetToken());
        addHttpParameters("goods_zoneid", request.goodsZoneID);
        //GDPR
        if(GDPR.ifCollect) {
            String xgId = GDPR.getDeviceGuid(CTIPayAPI.singleton().getApplicationContext());
            if(TextUtils.isEmpty(xgId)) {
                addHttpParameters("xg_mid", "");
            } else {
                addHttpParameters("xg_mid", URLEncoder.encode(GDPR.getDeviceGuid(CTIPayAPI.singleton().getApplicationContext()))); //请求添加信鸽id;
            }
        }




        //extend sub goodsZoneId

//        try {
//            addHttpParameters("extend", extend);
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }

        addHttpEncodeParameter("openid", GlobalData.singleton().openID);
        addHttpEncodeParameter("openkey", GlobalData.singleton().openKey);
        addHttpEncodeParameter("session_id", GlobalData.singleton().sessionID);
        addHttpEncodeParameter("session_type", GlobalData.singleton().sessionType);
        addHttpEncodeParameter("sdkversion", "androidoversea_v" + GlobalData.SDK_VERSION);
        addHttpEncodeParameter("buy_quantity", buy_quantity);

        if(!TextUtils.isEmpty(infoType)){
            //Type of marketing activity, get shortcode
            addHttpParameters("type",infoType);
        }else if(!TextUtils.isEmpty(request.mType)){
            addHttpEncodeParameter("type", request.mType);
        }


        String drmInfo = request.drmInfo;
        if (TextUtils.isEmpty(drmInfo)) {
            drmInfo = "version=3.0";
        } else {
            drmInfo = drmInfo + "&version=3.0";
        }
        addHttpEncodeParameter("drm_info", URLEncoder.encode(drmInfo));


        if (!TextUtils.isEmpty(currency_type)) {
            addHttpEncodeParameter("currency_type", currency_type);
        }

        if (!TextUtils.isEmpty(request.country)) {
            addHttpEncodeParameter("country", request.country);
        }

        if (!TextUtils.isEmpty(service_code)) {
            addHttpEncodeParameter("service_code", service_code);
        }

        if (!TextUtils.isEmpty(userip)) {
            addHttpEncodeParameter("userip", userip);
        }
        String extend = request.reserve;

        if(!TextUtils.isEmpty(extend)){
            extend = extend+"&" + CTITools.getCentuarimds() + "_goods_zoneid="+request.goodsZoneID;
        }else{
            extend = CTITools.getCentuarimds() + "_goods_zoneid="+request.goodsZoneID;
        }
        CTILog.d("Extend reserv",extend);
        try {
            addHttpEncodeParameter("language", Locale.getDefault().getISO3Language());
            addHttpParameters("extend", URLEncoder.encode(extend, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        //order
        if (!TextUtils.isEmpty(pay_method)) {
            addHttpEncodeParameter("pay_method", pay_method);
        }

        String productID = request.productID;
        if (!TextUtils.isEmpty(productID)) {
            addHttpEncodeParameter("productid",productID);
        }

        if (!TextUtils.isEmpty(amt)) {
            addHttpEncodeParameter("amt", amt);
        }

        //wf_info
        StringBuilder payInfo = new StringBuilder();
        if (!TextUtils.isEmpty(payAmount)) {
            payInfo.append("gw_amt=").append(payAmount);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(payCurrencyType)) {
            payInfo.append("gw_currency=").append(payCurrencyType);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(CTIPayNewAPI.singleton().getApplicationContext().getPackageName())) {
            payInfo.append("gw_packageName=").append(CTIPayNewAPI.singleton().getApplicationContext().getPackageName());
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(basePlanId)) {
            payInfo.append("basePlanId=").append(basePlanId);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(gw_version)) {
            payInfo.append("gw_version=").append(gw_version);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(gw_pricingPhases)) {
            payInfo.append("gw_pricingPhases=").append(gw_pricingPhases);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(oneStop)) {
            payInfo.append("one_stop=").append(oneStop);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(gw_reprovide_version)) {
            payInfo.append("gw_reprovide_version=").append(gw_reprovide_version);
            payInfo.append("&");
        }

        if ("dev".equals(GlobalData.singleton().env)
                || "test".equals(GlobalData.singleton().env) || "sandbox".equals(GlobalData.singleton().env)) {
            payInfo.append("sandbox=" + true);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(pay_method) && "os_doku".equals(pay_method)) {
            payInfo.append("from=" + "doku_sdk");
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(GlobalData.singleton().preOrderId)){
            payInfo.append("pre_order_id=" + GlobalData.singleton().preOrderId);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(GlobalData.singleton().unifiedProductId)){
            payInfo.append("dao_product_id=" + GlobalData.singleton().unifiedProductId);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(GlobalData.singleton().provideAppID)){
            payInfo.append("provide_app_id=" + GlobalData.singleton().provideAppID);
            payInfo.append("&");
        }

        if (!TextUtils.isEmpty(channelExtra)){
            payInfo.append(channelExtra);
            payInfo.append("&");
        }

        payInfo.append(request.extra);
        if (TextUtils.isEmpty(payInfo)) {
            addHttpEncodeParameter("wf_info","");
        } else {
            addHttpEncodeParameter("wf_info", CTITools.urlEncode(payInfo.toString(), 1));
        }


        //Pass in productType when placing an order for monthly card, "2": automatic renewal, "0": ordinary monthly card
        if(cmd.contains(NetworkManager.CMD_ORDER)){
            if(request != null &&
                    (BillingFlowParams.TYPE_UNION_MONTH.equals(request.mType) ||
                            BillingFlowParams.TYPE_MONTH.equals(request.mType))){
                addHttpEncodeParameter("producttype",request.isAutoPay ? "2" : "0");
            }
        }


        //provide
        if (cmd.contains(NetworkManager.CMD_PROVIDE)) {
            // req 新增 amt 字段，值为支付后拉到的物品价格，不校验是否为空
            addHttpEncodeParameter("amt", provideParams.second_amt);
            // req 新增 locale 字段，格式为 "支付前币种,支付后币种"，不校验是否为空
            addHttpEncodeParameter("locale", provideParams.first_currency_type + "," + provideParams.second_currency_type);
            // req 新增 device_locale 字段，格式为 {"country_code":"CN","currency_code":"CNY","lang_code":"zh"}
            String currencyCode = "";
            try {
                currencyCode = Currency.getInstance(Locale.getDefault()).getCurrencyCode();
            } catch (Exception e) {
                if ("ar".equals(Locale.getDefault().getCountry())) {
                    currencyCode = "ARS";
                }
                CTILog.e(TAG, "getDefaultCurrencyCode exception: " + e.getMessage());
                e.printStackTrace();
            }
            addHttpEncodeParameter("device_locale", "{\"country_code\":\"" + Locale.getDefault().getCountry()
                        + "\",\"currency_code\":" + currencyCode
                        + "\",\"lang_code\":" + Locale.getDefault().getLanguage() + "\"}");
            if (!TextUtils.isEmpty(billno)) {
                addHttpEncodeParameter("billno", billno);
            }
            if (!TextUtils.isEmpty(receipt)) {
                addHttpEncodeParameter("receipt", receipt);
            }
            if (!TextUtils.isEmpty(receipt_openid)) {
                addHttpEncodeParameter("receipt_openid", receipt_openid);
            }
            addHttpEncodeParameter("buy_quantity", num);
            if (!TextUtils.isEmpty(receipt_sign)) {
                addHttpEncodeParameter("receipt_sign", receipt_sign);
                addHttpEncodeParameter("sig", CTIMD5.toMd5(receipt.getBytes()));
            }

            if (!TextUtils.isEmpty(GlobalData.singleton().offerID)) {
                addHttpEncodeParameter("receipt_offer_id", GlobalData.singleton().offerID);
            }

            //Whether to make up the shipment
            if (isReProvide) {
                addHttpParameters("action", "reprovide");
            } else {
                addHttpParameters("action", "provide");
            }
        }

        //GDPR
        if(GDPR.ifCollect) {
            Context context = CTIPayAPI.singleton().getApplicationContext();
            String ssid = GDPR.getWifiSSID(context);
            if(TextUtils.isEmpty(ssid)) {
                addHttpEncodeParameter("wifi_ssid","");
            } else {
                addHttpEncodeParameter("wifi_ssid", URLEncoder.encode(ssid));
            }

            addHttpEncodeParameter("device_guid", GDPR.getDeviceGuid(context));
            addHttpEncodeParameter("device_type", GDPR.getDeviceType());
            addHttpEncodeParameter("device_name", GDPR.getDeviceName());
            addHttpEncodeParameter("device", GDPR.getDevice());
            addHttpEncodeParameter("device_product", GDPR.getDeviceManufacturer());
            addHttpEncodeParameter("sys_version", GDPR.getSysVersion());
        }

        //Add measured network information
        addHttpEncodeParameter("rtt", ""+ GlobalData.singleton().NetTimeout().getRtt(getHost()));
        addHttpEncodeParameter("loss_rate", ""+ GlobalData.singleton().NetTimeout().getIpLossRate(getHost()));
    }
}
