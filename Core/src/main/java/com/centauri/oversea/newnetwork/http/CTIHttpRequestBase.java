package com.centauri.oversea.newnetwork.http;

import android.text.TextUtils;
import com.centauri.http.core.HttpURL;
import com.centauri.http.core.Request;
import com.centauri.http.core.Response;
import com.centauri.http.centaurihttp.CTIHttpRequest;
import com.centauri.oversea.api.request.CTIBaseRequest;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.comm.CTILog;
import com.centauri.oversea.comm.MConstants;

/**
 * The base class for all network requests, handling some common logic
 * Avoid having a large piece of the same code for each Request
 *
 */
public abstract class CTIHttpRequestBase extends CTIHttpRequest {
    private final String TAG = this.getClass().getSimpleName();

    protected String cmd = "";
    protected String httpRequestKey = "";

    //In order to prevent the request data from being chaotic, the data is passed in from outside, and global data is not taken
    protected NetworkReqParams request = null;


    public CTIHttpRequestBase() {
        needFrontGetKeyInterceptor = false; //Do not go to the underlying network module to change the key logic
        needEndGetKeyInterceptor = false;
        GlobalData.singleton().refreshNetToken(); //Refresh network request token
    }


    //Support test/live network two sets of requests
    protected void initUrl(){
        String env = GlobalData.singleton().env;
        if ("local_test".equals(GlobalData.singleton().IDC) && "test".equals(env)) {
            String testUrl =  String.format(MConstants.AP_OVERSEA_COMM_SANDBOX_FCG, GlobalData.singleton().offerID);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTP,testUrl);
        } else if ("release".equals(env)) {
            String releaseUrl =  String.format(MConstants.AP_OVERSEA_COMM_FCG, GlobalData.singleton().offerID);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS,releaseUrl);
        } else {
            //sandbox
            String testUrl =  String.format(MConstants.AP_OVERSEA_COMM_SANDBOX_FCG, GlobalData.singleton().offerID);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS,testUrl);
        }
    }

    protected void initProductUrl(){
        String env = GlobalData.singleton().env;
        if ("local_test".equals(GlobalData.singleton().IDC) && "test".equals(env)) {
            String testUrl =  String.format(MConstants.AP_OVERSEA_PRODUCT_SANDBOX_FCG);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTP,testUrl);
        } else if ("release".equals(env)) {
            String releaseUrl =  String.format(MConstants.AP_OVERSEA_PRODUCT_FCG);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS,releaseUrl);
        } else {
            //sandbox
            String testUrl =  String.format(MConstants.AP_OVERSEA_PRODUCT_SANDBOX_FCG);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS,testUrl);
        }
    }

    protected void initPromotionUrl(){
        String env = GlobalData.singleton().env;
        if ("local_test".equals(GlobalData.singleton().IDC) && "test".equals(env)) {
            String testUrl =  String.format(MConstants.AP_OVERSEA_PROMOTION_SANDBOX_FCG);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTP,testUrl);
        } else if ("release".equals(env)) {
            String releaseUrl =  String.format(MConstants.AP_OVERSEA_PROMOTION_FCG);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS,releaseUrl);
        } else {
            //sandbox
            String testUrl =  String.format(MConstants.AP_OVERSEA_PROMOTION_SANDBOX_FCG);
            setCentauriHttpURL(HttpURL.SCHEMA.HTTPS,testUrl);
        }
    }

    @Override
    public void onHttpStart() {
        super.onHttpStart();
        HttpURL httpURL = getHttpUrl();
        CTILog.d("onHttpStart", "url="+ httpURL.host);
        connectTimeout = NetWorker.getTimeOut(getHost());
        readTimeout = connectTimeout;
    }


    @Override
    public void onHttpRetry(int currentRetry, int maxRetry, Request request, Response response) {
        String retryDomain = "";
        CTILog.d("HttpBase", "retry:"+ request.getClass().getName());
        if(currentRetry < maxRetry){    //ip retry
            retryDomain = GlobalData.singleton().IPManager().getRandomIp();
        }else{ //Domain retry
            if(GlobalData.singleton().isUseDomainFirst() &&
                    GlobalData.singleton().getIPListLength() >= maxRetry-1){
                // If the domain name takes precedence, if there are unused ip for the last time, continue to use ip
                retryDomain = GlobalData.singleton().IPManager().getRandomIp();
            } else {
                retryDomain = GlobalData.singleton().getHost();
            }
        }

        if(!TextUtils.isEmpty(retryDomain)){
            request.setHost(retryDomain);
        }
    }


    /**
     * Set the schema of this request
     * Set the suffix of the network request, that is, the remaining part of the URL except the domain name port
     * @param suffix corresponding url suffix
     * @param schema the schema of this request
     */
    protected void setCentauriHttpURL(final String schema, final String suffix) {
        if (TextUtils.isEmpty(suffix)) {
            CTILog.e(TAG, "Setting a empty http url suffix!");
        }

        if (TextUtils.isEmpty(schema)) {
            CTILog.e(TAG, "Setting a empty http url schema!");
        }
        //Check if you want domain name first
        String domain = "";
        if(GlobalData.singleton().isUseDomainFirst()){
            domain = GlobalData.singleton().getHost();
        } else {
            domain = GlobalData.singleton().IPManager().getRandomIp();
        }

        HttpURL httpURL = new HttpURL(schema, domain);
        httpURL.suffix = suffix;
        setURL(httpURL);
        CTILog.i(TAG,cmd+ " requestUrl: "+getFullURLString());
    }


    public String getHttpRequestKey() {
        return httpRequestKey;
    }


    /**
     * Get the encryption key for decryption
     */
    public String getDecodeKey(){
        String key = NetworkManager.singleton().getCryToKey(GlobalData.singleton().offerID,
                GlobalData.singleton().openID);
        if(TextUtils.isEmpty(key)){
            key = getEncodeKeyString();
        }
        return key;
    }


    //Whether to change the key
    protected boolean ifChangeKey(){
        //Already include key change request
        if(!TextUtils.isEmpty(cmd) && cmd.contains(NetworkManager.CMD_GET_KEY)){
            return true;
        }

        String offerId = getOfferIDFromRequest();
        String openId = getOpenIDFromRequest();

        //Need to change the key
      if(NetworkManager.singleton().needChangeKey(offerId,openId)){
            addGetKey();
          return true;
      }else{
          CTILog.i(TAG,cmd + " don't need change key.");
          return false;
      }
    }

    //Add key change request
    public void addGetKey(){

        if(TextUtils.isEmpty(cmd)) {
            cmd += NetworkManager.CMD_GET_KEY;
        }else if(!cmd.contains(NetworkManager.CMD_GET_KEY)){
            cmd += "|" + NetworkManager.CMD_GET_KEY;
        }


        addHttpParameters(NetworkManager.CMD_TAG,cmd);
        addHttpEncodeParameter("key", GlobalData.singleton().getBaseKey());
        addHttpParameters("get_key_type", "secret"); //Use the curing key for the security encryption key
        addHttpParameters("vid", GlobalData.singleton().changeVid); //Required for security encryption
    }


    protected void constructParam(){
        addHttpParameters("amode", "1");
        addHttpParameters("offer_id", GlobalData.singleton().offerID);
        if(!TextUtils.isEmpty(cmd)) {
            addHttpParameters(NetworkManager.CMD_TAG, cmd);
        }
    }


    public NetworkReqParams getRequest(){
        return request;
    }

    public String getCmd(){
        return cmd;
    }
}
