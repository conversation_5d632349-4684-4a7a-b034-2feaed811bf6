package com.centauri.oversea.newnetwork.http;

import android.content.Context;
import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.oversea.api.CTIPayAPI;
import com.centauri.oversea.comm.CTISPTools;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MConstants;
import com.centauri.oversea.data.Cfg;
import com.centauri.oversea.newapi.CTIPayNewAPI;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Responsible for reading ip and certificate information
 * Created by zachzeng on 2018/1/10.
 */

public class CTINetCfg {
    public static final String TAG = "APNetCfg";

    public final String UNIPAY_SANDBOX_DOMAIN_LOCAL = "";//sandbox
    private final String UNIPAY_RELEASE_REPORT_DOMAIN_LOCAL = ""; //The default value of the domain name reported by the risk control login
    private final String NET_CFG_DIR_PREFIX = "centauri_oversea_";
    private final String NET_CFG_FILE = "centauri_oversea_cp.cfg";
    public static final String UNIPAY_RELEASE_DOMAIN_HK = ""; //live network
    public static final String UIIPAY_DETECT_DOMAIN_HK = ""; // The default domain name for network detection

    private ConcurrentHashMap<String, Cfg> mMap = new ConcurrentHashMap<String, Cfg>();
    private ConcurrentHashMap<String, Cfg> mSandboxMap = new ConcurrentHashMap<String, Cfg>();

    public CTINetCfg(){
//        loadLocalData();
    }

    ///sandbox
    public static String getSandBoxDomainLocal(){
        StringBuffer stringBuffer = new StringBuffer();
        return stringBuffer.toString();
    }

    //Risk control login and report the default domain name
    public static String getReleaseReportDomainLocal(){
        StringBuffer stringBuffer = new StringBuffer();
        return stringBuffer.toString();
    }
    //The existing network
    public static String getReleaseDomainHK(){
        StringBuffer stringBuffer = new StringBuffer();
        return stringBuffer.toString();
    }
    // Default domain name for network detection
    public static String getDetectDomainHK(){
        StringBuffer stringBuffer = new StringBuffer();
        return stringBuffer.toString();
    }

    public void init(){
        String env = GlobalData.singleton().env;
        if (TextUtils.isEmpty(env)) {
            throw new IllegalArgumentException("env should be set before init");
        }

        if (initServerCfg()) {
            CTILog.i(TAG, env + " configuration contains in server config");
        }
//        else if (initFileCfg()) {
//            CTILog.i(TAG, idc + " configuration contains in file config");
//        }
        else {
            if ("release".equals(env) ||
                    "sandbox".equals(env) ||
                    "dev".equals(env)) {
                CTILog.i(TAG, "configuration has found");
            } else {
                throw new IllegalArgumentException(env + " not exist,please help to check the name");
            }
        }
//
//        if (null == CTIPayAPI.singleton().getApplicationContext()) {
//            CTILog.i(TAG, "there is no context");
//            throw new IllegalArgumentException("there is no context");
//        }

        // Set whether the domain name has priority
        if(null != CTIPayAPI.singleton().getApplicationContext() &&
                1 == CTISPTools.getInt(CTIPayAPI.singleton().getApplicationContext(), "domain_first")){
            GlobalData.singleton().setUseDomainFirst(true);
        }
    }


    /**
     * Initialize the configuration file, if the configuration file does not exist, prompt the developer
     * If it exists, verify the configuration and load the configuration file
     * @return
     */
    private boolean initServerCfg() {
        String idcInfo = GlobalData.singleton().IDCInfo;
        if (TextUtils.isEmpty(idcInfo)) {
            return false;
        }

        try {
            String env = GlobalData.singleton().env;
            JSONObject obj = new JSONObject(idcInfo);
            JSONObject jENV = obj.optJSONObject(env);
            if (jENV == null) {
                return false;
            }

//            String idc = GlobalData.singleton().IDC;
//            JSONObject jIDC = jENV.optJSONObject(env);
//            if (jIDC == null) {
//                return false;
//            }

            String k_domain = jENV.optString("k_domain");
            String k_domain_hb = jENV.optString("k_domain_hb");
            String detectDomain = jENV.optString("detect_domain");

            if (TextUtils.isEmpty(k_domain)) {
                return false;
            }
            JSONArray ipList = null;
            if (jENV.has("k_ip_list")) {
                ipList = jENV.optJSONArray("k_ip_list");
            }
            String[] ipArray = parseIPList(ipList);
//            if (ipArray == null) {
//                return false;
//            }

            CTILog.i(TAG, "initServerCfg  OK");
            if (env.equals("release")) {
                mMap.put(env, new Cfg(k_domain, ipArray, k_domain_hb, detectDomain));
            } else if (env.equals("sandbox") || env.equals("dev")) {
                mSandboxMap.put(env, new Cfg(k_domain, ipArray, k_domain_hb, detectDomain));
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * Initialize the configuration file, if the configuration file does not exist, prompt the developer
     * If it exists, verify the configuration and load the configuration file
     * @return
     */
    private boolean initFileCfg() {
        String idc = GlobalData.singleton().IDC;
        String cfg = getFromAssets(NET_CFG_DIR_PREFIX + idc + "/" + NET_CFG_FILE);

        if (TextUtils.isEmpty(cfg)) {
            CTILog.e(TAG, CTITools.getCentuarimds() + "_oversea_cp.cfg file is missing,if is cp mode ,this must be set");
            return false;
        } else {
            try {
                JSONObject cfgInfo = new JSONObject(cfg);
                JSONArray json = cfgInfo.getJSONArray("config");
                String mode, domain, reportDomain, detectDomain="";
                JSONObject item = null;
                JSONArray ipList = null;

                for (int i = 0; i < json.length(); i++) {
                    item = json.getJSONObject(i);
                    mode = item.optString("mode");
                    domain = item.optString("domain");
                    reportDomain = item.optString("reportdomain");    //New login and report domain name
                    // In the configuration file, the domain of data detection
                    detectDomain = item.optString("detectdomain");
                    CTILog.d("APNetCfg", mode+" detectdomain in configFile : " + detectDomain);
                    if (item.has("iplist")) {
                        ipList = item.getJSONArray("iplist");
                    }

                    String[] ipArray = parseIPList(ipList);
                    if (ipArray == null || ipArray.length == 0) {
                        CTILog.e(TAG, "ipList is empty in centauri_oversea_cp.cfg file, no return");
//                        return false;
                    }

                    if ("release".equals(mode)) {
                        mMap.put(idc, new Cfg(domain, ipArray, reportDomain, detectDomain));
                    } else if ("sandbox".equals(mode) || "dev".equals(mode)) {
                        mSandboxMap.put(idc, new Cfg(domain, ipArray, reportDomain, detectDomain));
                    }
                }
                CTILog.d("APNetCfg", "test config:" + mSandboxMap.toString());

                return true;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return false;
        }
    }


    private String getFromAssets(String fileName) {
        BufferedReader bufReader = null;
        try {
            Context context = CTIPayNewAPI.singleton().getApplicationContext();
            bufReader = new BufferedReader(new InputStreamReader(context.getResources().getAssets().open(fileName)));
            String line = "";
            StringBuilder sb = new StringBuilder();
            while ((line = bufReader.readLine()) != null){
                sb.append(line);
            }
            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (bufReader != null) {
                    bufReader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }


    private String[] parseIPList(JSONArray arr) {
        if (arr == null || arr.length() == 0) {
            return new String[0];
        }

        int size = arr.length();
        String[] list = new String[size];
        for (int i = 0; i < size; i++) {
            list[i] = arr.optString(i);
        }
        return list;
    }
    private int count = 0;

    private Cfg getCfg() {
        Cfg cfg = null;
        String env = GlobalData.singleton().env;
        String idc = GlobalData.singleton().IDC;
        if (TextUtils.isEmpty(env)) {
            CTILog.e(TAG, "getCfg setIDCAndEnv---");
            GlobalData.singleton().setIDCAndEnv();
            env = GlobalData.singleton().env;
            idc = GlobalData.singleton().IDC;
            initServerCfg();
//            initFileCfg();
        }

        if (MConstants.ReleaseEnv.equals(env)) {
            cfg = mMap.get(env);
        } else{
            cfg = mSandboxMap.get(env);
        }

        if (cfg == null) {
            throw new IllegalArgumentException(idc + "  does not deploy " + env + " env!!,please check the params and the  config file in assets");
        }
        return cfg;
    }

    public String[] getIPList() {
        return getCfg().mIpList;
    }

    //Get the host of the access ip
    public String getHost() {
        //Support demo dev test
//        if("dev".equals(GlobalData.singleton().env)){
//            StringBuffer stringBuffer = new StringBuffer();
//            return stringBuffer.toString();
//        }

        return getCfg().mDomain;
    }

    //Get the domain name reported during login
    public String getReportDomain() {
        return getCfg().mReportDomain;
    }

    // Get idcinfo or detect_domain in the network configuration file
    public String getDetectDomain(){
        return getCfg().mDetectDoain;
    }
}
