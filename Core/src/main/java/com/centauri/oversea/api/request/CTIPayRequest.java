package com.centauri.oversea.api.request;

import com.centauri.oversea.api.CTIPayAPI;


/**
 * Game coin type request object
 * <AUTHOR>
 * 
 */
public class CTIPayRequest extends CTIBaseRequest
{

	private static final long serialVersionUID = -6975967912774129232L;

	public String payInfo;
	/**
	 * Game currency mode request construction method
	 */
	public CTIPayRequest()
	{
		super();
//		mType = OFFER_TYPE_GAME;
//		mOldType = CTIPayAPI.SAVETYPE_GAME;
	}

}

	

