package com.centauri.oversea.api.request;

import java.io.Serializable;

/**
 * pay base class
 * <AUTHOR>
 */
public abstract class CTIBaseRequest implements Serializable
{
	private static final long serialVersionUID = -9123623786877679280L;
	public final static String DEFAULTPRODUCTID = "FFFFFFFF";
	//pay type
	public static final int MALL_TYPE_DEFAULT 	= 0;		
	public static final int MALL_TYPE_GROUPBUY 	= 1;		//group buy
	public static final int MALL_TYPE_VMALL		= 2;		//SDK vmall
	public static final String OFFER_TYPE_BG = "bg";
	public static final String OFFER_TYPE_GAME = "save";
	public static final String OFFER_TYPE_UNIONMONTH = "unimonth";
	
	
	/**
	 * old type
	 */
	public int mOldType;
	
	/**
	 * type
	 */
	public String mType;

	/**
	 * pay id
	 */
	public String offerId;
	public String appId;

	/**
	 * provideApp id
	 */
	public String provideAppId;
	public String providerOfferId;
	/**
	 * user id for login
	 */
	public String openId;
	public String userId;
	/**
	 * user key for login
	 */
	public String openKey;

	/**
	 * session id
	 */
	public String sessionId;

	/**
	 * session type
	 */
	public String sessionType;

	/**
	 * account zone id
	 */
	public String zoneId;
	public String serverId;

	/**
	 * account role id
	 */
	public String roleId;

	/**
	 * game zone id
	 */
	public String goodsZoneId;

	/**
	 * pf,Specific reference documents
	 */
	public String pf;

	/**
	 * pfKey,Specific reference documents
	 */
	public String pfKey;

	/**
	 * Purchase quantity, default empty string. Game currency, monthly, subscription type list mode pass empty string or null
	 */
	public String saveValue;

	/**
	 * GoogleBilling in-app messaging,Specific reference documents
	 */
	public boolean inAppMessaging;

	/**
	 * This parameter is only valid if the purchase quantity is passed. For item purchase mode,
	 * if the server side orders, this parameter is ignored because the server side can specify
	 * whether the purchase quantity can be changed. For item purchase mode, if the SDK orders,
	 * if the purchase quantity is specified, this parameter will take effect.
	 * The number edit page is displayed. If equal to flase, the number edit page is skipped.
	 * Default is true.
	 */
	public boolean isCanChange;

	/**
	 * Resource ID, game currency, monthly payment and subscription type are displayed in the purchase list without default value
	 */
	public int resId;

	/**
	 * ResData, the icon displayed in the purchase list, is used in the same way as ResID. ResData is preferred
	 */
	public byte[] resData;
	/**
	 * Account Type, Base Currency Common, Securty, Default Base Currency
	 */
	public String acctType;

	/**
	 * Keep field
	 */
	public String reserv;
	
	/**
	 * Types of purchase, such as group purchase, micro store, general purchase
	 */
	public int mallType = MALL_TYPE_DEFAULT;
	
	/**
	 * This parameter is a reserved parameter, which has not been implemented yet.
	 * Use WebView to directly pull up the specified H5 interface, and then call the JS interface provided externally
	 */
	public String h5Url = "";

	/**
	 * Marketing activities
	 */
	public CTIMPInfo mpInfo;

	/**
	 * Extended information
	 */
	public CTIExtendInfo extendInfo;
	
	/**
	 * Currency type
	 */
	public  String currency_type = "";

	/**
	 * Currency Code == Currency type
	 */
	public  String currencyCode = "";

	/**
	 * country
	 */
	public  String country = "";

	/**
	 * regionCode
	 */
	public  String regionCode = "";

    /**
     * Extended information
     */
    public String extras = "";

	/**
	 * Type of Marketing Activities
	 */
	public String drmInfo = "";

	/**
	 * Other extended information
	 */
	public class CTIExtendInfo implements Serializable
	{
		private static final long serialVersionUID = -5387967973327966068L;
		
		/**
		 * Specifies the unit to display
		 */
		public String unit;
		/**
		 * Specifies whether to display the quantity
		 */
		public boolean isShowNum;
		/**
		 * Specifies whether the purchase list displays other amounts
		 */
		public boolean isShowListOtherNum;
		
		
		/**
		 * User extension information
		 * The Vietnamese channel will have an extended profile of the user
		 */
		public String userExtend = "";
		
		public String   iChannel  = "";	//

		
		public CTIExtendInfo()
		{
			unit = "";
			isShowNum = true;
			isShowListOtherNum = true;
			userExtend = "";
	
			iChannel = "";

		}
	}

	/**
	 * Marketing activities within the category
	 */
	public class CTIMPInfo implements Serializable
	{
		private static final long serialVersionUID = 631101753621041424L;

		/**
		 * Specify channel activities
		 */
		public String payChannel;

		/**
		 * The specified item
		 */
		public String productid;

		/**
		 * discount type
		 */
		public String discountType;

		/**
		 * discount url
		 */
		public String discountUrl;

		/**
		 * Type of Marketing Activities
		 */
		public String drmInfo = "";

		/**
		 * Marketing campaign number. Use this field with caution
		 * Do not set this field again if the activity number has expired, otherwise the payment will fail
		 */
		public String discoutId = "";
		

		public String extras = "";
		
		public CTIMPInfo()
		{
			payChannel = "";
			discountType = "";
			discountUrl = "";
			drmInfo = "";
			discoutId = "";
			extras = "";
		}
	}

	/**
	 * Abstract the base class constructor. Instead of creating the object directly, create the corresponding subclass object
	 * <AUTHOR>
	 */
	public CTIBaseRequest()
	{
		offerId = "";
		appId = "";
		provideAppId = "";
		providerOfferId = "";
		openId = "";
		userId = "";
		openKey = "";
		sessionId = "";
		sessionType = "";
		zoneId = "";
		serverId = "";
		roleId = "";
		pf = "";
		pfKey = "";
		resId = 0;
		acctType = "common";
		saveValue = "";
		isCanChange = true;
		mallType = MALL_TYPE_DEFAULT;
		h5Url = "";
		mpInfo = new CTIMPInfo();
		extendInfo = new CTIExtendInfo();
		drmInfo = "";
	}
	
	
//====================================== getter =================================
	
	public String getOfferId()
	{
		return this.offerId;
	}
	public String getProvideAppId()
	{
		return this.provideAppId;
	}
	public String getOpenId()
	{
		return this.openId;
	}
	public String getOpenKey()
	{
		return this.openKey;
	}
	public String getSessionId()
	{
		return this.sessionId;
	}
	public String getSessionType()
	{
		return this.sessionType;
	}
	public String getZoneId()
	{
		return this.zoneId;
	}
	public String getPf()
	{
		return this.pf;
	}
	public String getPfKey()
	{
		return this.pfKey;
	}
	public boolean getInAppMessaging()
	{
		return this.inAppMessaging;
	}
	public String getSaveValue()
	{
		return this.saveValue;
	}
	public boolean getIsCanChange()
	{
		return this.isCanChange;
	}
	public int getResId()
	{
		return this.resId;
	}
	public byte[] getResData()
	{
		return this.resData;
	}
	public String getAcctType()
	{
		return this.acctType;
	}
	public String getReserv()
	{
		return this.reserv;
	}
	public int getMallType()
	{
		return this.mallType;
	}
	public String getH5Url() {
		return h5Url;
	}
	
	public String getPayChannel() {
		return this.mpInfo.payChannel;
	}
	public String getAssignProductid() {
		return this.mpInfo.productid;
	}
	public String getDiscountType() {
		return this.mpInfo.discountType;
	}
	public String getDiscountUrl() {
		return this.mpInfo.discountUrl;
	}
	public String getDrmInfo() {
		return this.mpInfo.drmInfo;
	}
	public String getDiscoutId() {
		return this.mpInfo.discoutId;
	}

	public String getExtras() {
		return this.mpInfo.extras;
	}
	
	public String getUnit() {
		return this.extendInfo.unit;
	}
	public boolean getShowNum() {
		return this.extendInfo.isShowNum;
	}
	public boolean getShowListOtherNum() {
		return this.extendInfo.isShowListOtherNum;
	}

	public String getCDrmInfo() {
		return this.drmInfo;
	}
	
	
	
//====================================== setter =================================
	
	public void setOfferId(String offerId)
	{
		this.offerId = offerId;
	}
	public void setProvideAppId(String provideAppId)
	{
		this.provideAppId = provideAppId;
	}
	public void setOpenId(String openId)
	{
		this.openId = openId;
	}
	public void setOpenKey(String openKey)
	{
		this.openKey = openKey;
	}
	public void setSessionId(String sessionId)
	{
		this.sessionId = sessionId;
	}
	public void setSessionType(String sessionType)
	{
		this.sessionType = sessionType;
	}
	public void setZoneId(String zoneId)
	{
		this.zoneId = zoneId;
	}
	public void setPf(String pf)
	{
		this.pf = pf;
	}
	public void setPfKey(String pfKey)
	{
		this.pfKey = pfKey;
	}
	public void setSaveValue(String saveValue)
	{
		this.saveValue = saveValue;
	}
	public void setIsCanChange(boolean isCanChange)
	{
		this.isCanChange = isCanChange;
	}
	public void setResId(int resId)
	{
		this.resId = resId;
	}
	public void setResData(byte[] resData)
	{
		this.resData = resData;
	}
	public void setAcctType(String acctType)
	{
		this.acctType = acctType;
	}
	public void setReserv(String reserv)
	{
		this.reserv = reserv;
	}
	public void setMallType(int mallType)
	{
		this.mallType = mallType;
	}
	public void setH5Url(String h5Url) {
		this.h5Url = h5Url;
	}
	
	public void setPayChannel(String payChannel) {
		this.mpInfo.payChannel = payChannel;
	}
	public void setDiscountType(String discountType) {
		this.mpInfo.discountType = discountType;
	}
	public void setDiscountUrl(String discountUrl) {
		this.mpInfo.discountUrl = discountUrl;
	}
	public void setDrmInfo(String drmInfo) {
		this.mpInfo.drmInfo = drmInfo;
	}
	public void setDiscoutId(String discoutId) {
		this.mpInfo.discoutId = discoutId;
	}

    /**
     * Marketing activities use interfaces, if not marketing activities, please directly set extras
     * @param extras
     */
    public void setExtras(String extras) {
		this.mpInfo.extras = extras;
	}
	
	public void setUnit(String unit) {
		this.extendInfo.unit = unit;
	}
	public void setShowNum(boolean isShowNum) {
		this.extendInfo.isShowNum = isShowNum;
	}
	public void setShowListOtherNum(boolean isShowListOtherNum) {
		this.extendInfo.isShowListOtherNum = isShowListOtherNum;
	}
	public void setCDrmInfo(String drmInfo) {
		this.drmInfo = drmInfo;
	}

}
