package com.centauri.oversea.api;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.centauri.comm.CTILog;
import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.api.request.CTIBaseRequest;
import com.centauri.oversea.api.request.CTIMonthRequest;
import com.centauri.oversea.api.request.ICTIProductInfoCallback;
import com.centauri.oversea.comm.CTISPTools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.centauri.oversea.newapi.params.InitParams;
import com.centauri.oversea.newapi.params.NetParams;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.InfoCallback;
import com.centauri.oversea.newnetwork.http.NetworkManager;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.HashMap;

/**
 * Maintain the old interface and adapt the new interface
 * Created by zachzeng on 2018/3/1.
 */

public class CTIPayAPI {
    public static final String TAG = "CTIPayAPI";

    public static final int SAVETYPE_GAME = 0;
    public static final int SAVETYPE_GOODS = 1;
    public static final int SAVETYPE_MONTH = 4;


    public static final int CLOSE_LOADING = 0x01;   // Close the Centauri loading progress bar
    public static final int CLOSE_RESULT = 0x02;    // Close the payment results page
    public static final int CLOSE_ALL = 0x03;       // Close all UIs

    @Retention(RetentionPolicy.CLASS)
    public @interface MUILevel {
    }


    private String env = "";
    private String idc = "";
    private String idcInfo = "";
    private boolean logEnable = false;

    private void CTIPayAPI() {
    }

    private static class InstanceHolder {
        private static CTIPayAPI instance = new CTIPayAPI();
    }

    public static CTIPayAPI singleton() {
        return InstanceHolder.instance;
    }


    /**
     * Set up the Centauri UI presentation
     * @param level
     */
    public void showCentauriUI(@MUILevel int level) {
        CTIPayNewAPI.singleton().showCentauriUI(level);
        CTILog.i(TAG, "showCentauriUI(),showCentauriUI UILevel: " + level);
    }


    /**
     * Set up master meter pay environment
     * Set it to "test" before publishing to the current web and set it to "release".
     * @param env "release" : web, "test" : sandbox
     */
    public void setEnv(String env) {
        this.env = env;
        CTILog.i(TAG, "setEnv(),env: " + env);
    }


    public String getEnv() {
        return CTIPayNewAPI.singleton().getEnv();
    }


    public void setLogEnable(boolean logEnable) {

        CTIPayNewAPI.singleton().setLogEnable(logEnable);
    }


    public boolean isLogEnable() {
        return CTIPayNewAPI.singleton().isLogEnable();
    }


    /**
     * Set IDC, determined by the business publishing region
     * @param idc "HongKong" : Hong Kong, "Canada" : Canada
     */
    public void setReleaseIDC(String idc) {
        this.idc = idc;
        CTILog.i(TAG, "setReleaseIDC(),idc: " + idc);
    }


    public void setReleaseIDC(String idc, String idcInfo) {
        this.idc = idc;
        this.idcInfo = idcInfo;
        CTILog.i(TAG, "setReleaseIDC(),idc: " + idc + " idcInfo: " + idcInfo);
    }


    public String getReleaseIDC() {
        return CTIPayNewAPI.singleton().getReleaseIDC();
    }


    public Context getApplicationContext() {
        return CTIPayNewAPI.singleton().getApplicationContext();
    }


    /**
     * Initialize the interface, which should be invoked as early as possible after logging in to help reduce the time spent entering payments
     * @param activity Pay for an activity
     * @Param Request Request for payment
     * @Param Callback to send back the shipment
     */
    public void init(Activity activity, CTIBaseRequest request, final ICTIPayUpdateCallBack callback) {
        //Init数据持久化
        dataPersistence(activity, request);
        InitParams.InitParamsExtra extra = new InitParams.InitParamsExtra();
        extra.setIDCInfo(idcInfo);
        extra.setGoodsZoneID(request.goodsZoneId);
        extra.setOpenKey(request.openKey);
        extra.setPF(request.pf);
        extra.setPFKey(request.pfKey);
        extra.setSessionID(request.sessionId);
        extra.setSessionType(request.sessionType);
        extra.setChannelExtras(request.extras); // 保存channelExtra字段
        InitParams params = new InitParams.Builder()
                .setEnv(env)
                .setIDC(idc)
                .setOfferID(request.offerId)
                .setOpenID(request.openId)
                .setZoneID(request.zoneId)
                .setInAppMessage(request.inAppMessaging)
                .setExtra(extra)
                .build();

        //init
        CTIPayNewAPI.singleton().init(activity,params);

        //reProvide
        CTIPayNewAPI.singleton().reProvide(new ICTICallback() {
            @Override
            public void callback(int retCode, String info) {

                callback.onUpdate(retCode, info);


            }
        });

        //dns
        //DnsManager.singleton().prefetchDnsDefault();
    }


    public void getProductInfo(String channel, HashMap<String, String> sku, final ICTIProductInfoCallback callback) {
        CTILog.i(TAG, "getProductInfo()");
        CTIPayNewAPI.singleton().getProductInfo(channel, sku, new InfoCallback() {
            @Override
            public void callback(String resp) {
                callback.onProductInfoResp(resp);
            }
        });
    }

    /**
     * Check discount price information, such as GP entry price information
     * @param channel
     * @param productIdMap
     * @param callback
     */
    public void getIntroPriceInfo(String channel, HashMap<String, String> productIdMap, final ICTIProductInfoCallback callback) {
        CTILog.i(TAG, "getIntroPriceInfo()");
        CTIPayNewAPI.singleton().getIntroPriceInfo(channel, productIdMap, new InfoCallback() {
            @Override
            public void callback(String resp) {
                if (callback != null) {
                    callback.onProductInfoResp(resp);
                }
            }
        });
    }

    public void reProvide(final ICTIPayUpdateCallBack callback) {
        CTILog.i(TAG, "reProvide()");
        CTIPayNewAPI.singleton().reProvide(new ICTICallback() {
            @Override
            public void callback(int retCode, String info) {
                callback.onUpdate(retCode, info);
                NetworkManager.singleton().dataReport(new ICTIHttpCallback() {
                    @Override
                    public void onSuccess(CTIHttpAns ans) {
                        CTILog.d("reprovide", "data report succ");
                    }

                    @Override
                    public void onFailure(CTIHttpAns ans) {
                        CTILog.d("reprovide\"", "data report failed; ret" + ans.getResultCode() + "&msg=" + ans.getResultMessage());
                    }

                    @Override
                    public void onStop(CTIHttpAns ans) {
                        CTILog.d("reprovide\"", "data report stopped");
                    }
                });
            }
        });
    }


    public void pay(Activity activity, CTIBaseRequest request, ICTICallBack callBack) {
        GlobalData instance = GlobalData.singleton();
        instance.offerID = request.offerId;
        instance.openID = request.openId;
        instance.openKey = request.openKey;
        instance.pf = request.pf;
        instance.pfKey = request.pfKey;
        instance.sessionID = request.sessionId;
        instance.sessionType = request.sessionType;
        instance.zoneID = request.zoneId;
        instance.goodsZoneID = request.goodsZoneId;

        BillingFlowParams.BillingFlowParamsExtra extra = new BillingFlowParams.BillingFlowParamsExtra();
        extra.setDrmInfo(request.getDrmInfo());
        extra.setGoodsZoneId(request.goodsZoneId);
        extra.setAppExtends(request.reserv);    //The video is passed into the aid field through the Reserv
        extra.setChannelExtras(request.extras);

        BillingFlowParams.Builder builder = new BillingFlowParams.Builder();
        builder.setCountry(request.country)
                .setCurrencyType(request.currency_type)
                .setPayChannel(request.getPayChannel())
                .setProductID(request.getAssignProductid())
                .setType(request.mType)
                .setExtra(extra);

        //month
        if(request instanceof CTIMonthRequest){
            CTIMonthRequest monthRequest = (CTIMonthRequest)request;
            builder.setIsAutoPay(monthRequest.autoPay)
                    .setServiceCode(monthRequest.serviceCode)
                    .setServiceName(monthRequest.serviceName)
                    .setBasePlanId(monthRequest.basePlanId)
                    .setGwOfferId(monthRequest.gw_offerId);
        }

        CTIPayNewAPI.singleton().pay(activity, builder.build(), callBack);
    }


    public void net(String reqType, final ICTINetCallBack callBack) {
        CTILog.i(TAG, "net()");
        NetParams params = new NetParams.Builder()
                .setMpReqType(reqType)
                .build();

        CTIPayNewAPI.singleton().net(params, callBack);
    }

    public void net(CTIBaseRequest request, String reqType, final ICTINetCallBack callBack) {
        CTILog.i(TAG, "net()");
        CTILog.i(TAG, "drmInfo is: " + request.drmInfo);

        NetParams params = new NetParams.Builder()
                .setMpReqType(reqType)
                .build();
        params.drmInfo = request.drmInfo;

        CTIPayNewAPI.singleton().net(params, callBack);
    }

    public void dataPersistence(Activity activity, CTIBaseRequest request) {

        CTISPTools.putString(activity,
                "initIdcInfo", idcInfo);
        CTISPTools.putString(activity,
                "initEnv", env);
        CTISPTools.putString(activity,
                "initIdc", idc);

    }

    public void deinit() {
        CTILog.i(TAG, "deinit()");
        CTIPayNewAPI.singleton().dispose();
    }
}
