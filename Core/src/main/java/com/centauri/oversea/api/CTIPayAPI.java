package com.centauri.oversea.api;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.centauri.comm.CTILog;
import com.centauri.http.centaurihttp.CTIHttpAns;
import com.centauri.http.centaurihttp.ICTIHttpCallback;
import com.centauri.oversea.api.request.CTIBaseRequest;
import com.centauri.oversea.api.request.CTIMonthRequest;
import com.centauri.oversea.api.request.CTIPayRequest;
import com.centauri.oversea.api.request.CTIProductRequest;
import com.centauri.oversea.api.request.CTIPromotionRequest;
import com.centauri.oversea.api.request.ICTIProductInfoCallback;
import com.centauri.oversea.api.request.ICTIPromotionCallback;
import com.centauri.oversea.comm.CTISPTools;
import com.centauri.oversea.comm.CTITools;
import com.centauri.oversea.comm.GlobalData;
import com.centauri.oversea.comm.MTimer;
import com.centauri.oversea.newapi.CTIPayNewAPI;
import com.centauri.oversea.newapi.params.BillingFlowParams;
import com.centauri.oversea.newapi.params.InitParams;
import com.centauri.oversea.newapi.params.NetParams;
import com.centauri.oversea.newapi.response.GetProductCallback;
import com.centauri.oversea.newapi.response.ICTICallback;
import com.centauri.oversea.newapi.response.InfoCallback;
import com.centauri.oversea.newapi.response.NotifyCallback;
import com.centauri.oversea.newnetwork.http.NetworkManager;
import com.centauri.oversea.newnetwork.http.NetworkReqParams;
import com.centauri.oversea.newnetwork.model.CTIOverSeaCommAns;
import com.centauri.oversea.newnetwork.model.CTIOverSeaCommReq;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Base64;
import java.util.HashMap;

/**
 * Maintain the old interface and adapt the new interface
 * Created by zachzeng on 2018/3/1.
 */

public class CTIPayAPI {
    public static final String TAG = "CTIPayAPI";

    public static final int SAVETYPE_GAME = 0;
    public static final int SAVETYPE_GOODS = 1;
    public static final int SAVETYPE_MONTH = 4;


    public static final int CLOSE_LOADING = 0x01;   // Close the Centauri loading progress bar
    public static final int CLOSE_RESULT = 0x02;    // Close the payment results page
    public static final int CLOSE_ALL = 0x03;       // Close all UIs

    @Retention(RetentionPolicy.CLASS)
    public @interface MUILevel {
    }


    private String env = "";
    private String idc = "";
    private String idcInfo = "";
    private boolean logEnable = false;
    private static final String PAY_CHANNEL_OS_MIDASPAY = "os_midaspay";
    private static final String PAY_CHANNEL_OS_OLDMIDASPAY = "os_mp";

    private void CTIPayAPI() {
    }

    private static class InstanceHolder {
        private static CTIPayAPI instance = new CTIPayAPI();
    }

    public static CTIPayAPI singleton() {
        return InstanceHolder.instance;
    }


    /**
     * Set up the Centauri UI presentation
     * @param level
     */
    public void showCentauriUI(@MUILevel int level) {
        CTIPayNewAPI.singleton().showCentauriUI(level);
        CTILog.i(TAG, "showCentauriUI(),showCentauriUI UILevel: " + level);
    }


    /**
     * Set up master meter pay environment
     * Set it to "test" before publishing to the current web and set it to "release".
     * @param env "release" : web, "test" : sandbox
     */
    public void setEnv(String env) {
        this.env = env;
        CTILog.i(TAG, "setEnv(),env: " + env);
    }


    public String getEnv() {
        return CTIPayNewAPI.singleton().getEnv();
    }


    public void setLogEnable(boolean logEnable) {

        CTIPayNewAPI.singleton().setLogEnable(logEnable);
    }


    public boolean isLogEnable() {
        return CTIPayNewAPI.singleton().isLogEnable();
    }


    /**
     * Set IDC, determined by the business publishing region
     * @param idc "HongKong" : Hong Kong, "Canada" : Canada
     */
    public void setReleaseIDC(String idc) {
        this.idc = idc;
        CTILog.i(TAG, "setReleaseIDC(),idc: " + idc);
    }

    public void setIDCInfo(String idcInfo) {
        this.idcInfo = idcInfo;
        CTILog.i(TAG, "setReleaseIDC(),idcInfo: " + idcInfo);
    }

    public void setReleaseIDC(String idc, String idcInfo) {
        this.idc = idc;
        this.idcInfo = idcInfo;
        CTILog.i(TAG, "setReleaseIDC(),idc: " + idc + " idcInfo: " + idcInfo);
    }


    public String getReleaseIDC() {
        return CTIPayNewAPI.singleton().getReleaseIDC();
    }


    public Context getApplicationContext() {
        return CTIPayNewAPI.singleton().getApplicationContext();
    }


    /**
     * Initialize the interface, which should be invoked as early as possible after logging in to help reduce the time spent entering payments
     * @param activity Pay for an activity
     * @Param Request Request for payment
     * @Param Callback to send back the shipment
     */
    public void init(Activity activity, CTIBaseRequest request, final ICTIPayUpdateCallBack callback) {
        //Init数据持久化
        dataPersistence(activity, request);
        InitParams.InitParamsExtra extra = new InitParams.InitParamsExtra();
        extra.setIDCInfo(idcInfo);
        extra.setGoodsZoneID(request.goodsZoneId);
        extra.setOpenKey(request.openKey.isEmpty() ? "openKey" : request.openKey);
        extra.setPF(request.pf.isEmpty() ? "yu_m-2001-android-2001" : request.pf);
        extra.setPFKey(request.pfKey.isEmpty() ? "pfkey" : request.pfKey);
        extra.setSessionID(request.sessionId.isEmpty() ? "hy_gameid" : request.sessionId);
        extra.setSessionType(request.sessionType.isEmpty() ? "st_dummy" : request.sessionType);
        extra.setChannelExtras(request.extras); // 保存channelExtra字段
        String offerID = request.appId.isEmpty() ? request.offerId : request.appId;
        String openID = request.userId.isEmpty() ? request.openId : request.userId;
        String provideAppId = request.providerOfferId.isEmpty() ? request.provideAppId : request.providerOfferId;
        String zoneID = request.zoneId.isEmpty()? request.serverId : request.zoneId;
        String regionCode = request.country.isEmpty()? request.regionCode : request.country;
        String currencyCode = request.currency_type.isEmpty()? request.currencyCode : request.currency_type;

        if (!request.roleId.isEmpty()) {
            zoneID = zoneID + "_" + request.roleId;
        }
        InitParams params = new InitParams.Builder()
                .setEnv(env)
                .setIDC(idc)
                .setOfferID(offerID)
                .setProvideAppID(provideAppId)
                .setPayChannel(request.mpInfo.payChannel)
                .setOpenID(openID)
                .setZoneID(zoneID)
                .setCurrencyCode(currencyCode)
                .setRegionCode(regionCode)
                .setInAppMessage(request.inAppMessaging)
                .setExtra(extra)
                .build();

        //init
        CTIPayNewAPI.singleton().init(activity,params);

        //reProvide
        CTIPayNewAPI.singleton().reProvide(new ICTICallback() {
            @Override
            public void callback(int retCode, String info) {

                callback.onUpdate(retCode, info);


            }
        });

        //dns
        //DnsManager.singleton().prefetchDnsDefault();
    }

    public void getProductInfo(String channel, HashMap<String, String> sku, final ICTIProductInfoCallback callback) {
        CTILog.i(TAG, "getProductInfo()");

        NetworkManager.singleton().getProductReq(sku, new GetProductCallback() {
            @Override
            public void onSuccess(CTIHttpAns ans) {
                CTIPayNewAPI.singleton().getProductInfo(channel, sku, ans.getResultData(), new InfoCallback() {
                    @Override
                    public void callback(String resp) {
                        callback.onProductInfoResp(resp);
                    }
                });
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                JSONObject obj = new JSONObject();
                try {
                    obj.put("retCode", ans.getResultCode());
                    JSONObject jsonObject = new JSONObject(ans.getResultData());
                    obj.put("innerCode", jsonObject.getString("name"));
                    obj.put("retMsg", jsonObject.getString("message"));
                    JSONArray jsRes = new JSONArray();
                    obj.put("respInfo", jsRes);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onProductInfoResp(obj.toString());
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                CTILog.e(TAG,"getProductInfo() onStop.");
            }
        });
    }

    public void getProductInfo(CTIProductRequest request, final ICTIProductInfoCallback callback) {
        CTILog.i(TAG, "getProductInfo()");
        NetworkManager.singleton().getProductReq(request.unifiedSkuLists, new GetProductCallback() {
            @Override
            public void onSuccess(CTIHttpAns ans) {
                try {
                    JSONObject resJson = new JSONObject(ans.getResultData());
                    JSONArray productList = resJson.getJSONObject("result").getJSONArray("product_list");
                    String payChannel = GlobalData.singleton().payChannel;
                    if (PAY_CHANNEL_OS_MIDASPAY.equals(payChannel) || PAY_CHANNEL_OS_OLDMIDASPAY.equals(payChannel)) {
                        JSONObject successObj = new JSONObject();
                        successObj.put("retCode", 0);
                        successObj.put("innerCode", "");
                        successObj.put("retMsg", "success");
                        successObj.put("respInfo", productList);
                        callback.onProductInfoResp(successObj.toString());
                    } else {
                        java.util.HashMap<String, String> skuMap = new java.util.HashMap<>();
                        for (int i = 0; i < productList.length(); i++) {
                            JSONObject product = productList.getJSONObject(i);
                            skuMap.put(product.getString("unified_product_id"),
                                    new JSONObject(product.getString("platform_product_info")).getString("sdk_product_type"));
                        }
                        CTIPayNewAPI.singleton().getProductInfo(request.paymentMethod, skuMap, ans.getResultData(),
                                callback::onProductInfoResp);
                    }
                } catch (Exception e) {
                    handleError(e, callback);
                }
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                CTILog.e(TAG, "getProductInfo() onFailure.");
                try {
                    JSONObject failObj = new JSONObject();
                    failObj.put("retCode", ans.getResultCode());
                    JSONObject dataJson = new JSONObject(ans.getResultData());
                    failObj.put("innerCode", dataJson.getString("name"));
                    failObj.put("retMsg", dataJson.getString("message"));
                    failObj.put("respInfo", new JSONArray());
                    callback.onProductInfoResp(failObj.toString());
                } catch (Exception e) {
                    handleError(e, callback);
                }
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                CTILog.e(TAG, "getProductInfo() onStop.");
            }
        });
    }

    public void getPromotionInfo(CTIPromotionRequest request, final ICTIPromotionCallback callback) {
        CTILog.i(TAG, "getPromotionInfo()");

        NetworkManager.singleton().getPromotionReq(request.unifiedSkuLists, new GetProductCallback() {
            @Override
            public void onSuccess(CTIHttpAns ans) {
                JSONObject obj = new JSONObject();
                try {
                    obj.put("retCode", 0);
                    obj.put("innerCode", "");
                    obj.put("retMsg", "");
                    obj.put("respInfo", ans.getResultData());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onPromotionInfoResp(obj.toString());
            }

            @Override
            public void onFailure(CTIHttpAns ans) {
                JSONObject obj = new JSONObject();
                try {
                    obj.put("retCode", ans.getResultCode());
                    JSONObject jsonObject = new JSONObject(ans.getResultData());
                    obj.put("innerCode", jsonObject.getString("name"));
                    obj.put("retMsg", jsonObject.getString("message"));
                    JSONArray jsRes = new JSONArray();
                    obj.put("respInfo", jsRes);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onPromotionInfoResp(obj.toString());
            }

            @Override
            public void onStop(CTIHttpAns ans) {
                CTILog.e(TAG,"getProductInfo() onStop.");
            }
        });
    }
    /**
     * Check discount price information, such as GP entry price information
     * @param channel
     * @param productIdMap
     * @param callback
     */
    public void getIntroPriceInfo(String channel, HashMap<String, String> productIdMap, final ICTIProductInfoCallback callback) {
        CTILog.i(TAG, "getIntroPriceInfo()");
        CTIPayNewAPI.singleton().getIntroPriceInfo(channel, productIdMap, new InfoCallback() {
            @Override
            public void callback(String resp) {
                if (callback != null) {
                    callback.onProductInfoResp(resp);
                }
            }
        });
    }

    public void reProvide(final ICTIPayUpdateCallBack callback) {
        CTILog.i(TAG, "reProvide()");
        CTIPayNewAPI.singleton().reProvide(new ICTICallback() {
            @Override
            public void callback(int retCode, String info) {
                callback.onUpdate(retCode, info);
                NetworkManager.singleton().dataReport(new ICTIHttpCallback() {
                    @Override
                    public void onSuccess(CTIHttpAns ans) {
                        CTILog.d("reprovide", "data report succ");
                    }

                    @Override
                    public void onFailure(CTIHttpAns ans) {
                        CTILog.d("reprovide\"", "data report failed; ret" + ans.getResultCode() + "&msg=" + ans.getResultMessage());
                    }

                    @Override
                    public void onStop(CTIHttpAns ans) {
                        CTILog.d("reprovide\"", "data report stopped");
                    }
                });
            }
        });
    }

    public void pay(Activity activity, CTIPayRequest request, ICTICallBack callBack) {
        try {
            CTILog.i(TAG, "CTIPayRequest() " + request.payInfo);
            JSONObject jsonObject = new JSONObject(request.payInfo);
            JSONObject dataObject;

            if (jsonObject.has("version") && jsonObject.getString("version").equals("v3")) {
                String base64Data = jsonObject.getString("data");
                byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
                String decodedData = new String(decodedBytes, "UTF-8");
                dataObject = new JSONObject(decodedData);
            } else {
                dataObject = jsonObject.getJSONObject("data");
            }

            processPaymentData(dataObject, request, activity, callBack);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void pay(Activity activity, CTIBaseRequest request, ICTICallBack callBack) {
        CTILog.i(TAG, "CTIBaseRequest()");
        GlobalData instance = GlobalData.singleton();
        instance.offerID = request.appId.isEmpty() ? request.offerId : request.appId;;
        instance.openID = request.userId.isEmpty() ? request.openId : request.userId;
        instance.openKey = request.openKey.isEmpty() ? "openKey" : request.openKey;
        instance.pf = request.pf.isEmpty() ? "yu_m-2001-android-2001" : request.pf;
        instance.pfKey = request.pfKey.isEmpty() ? "pfkey" : request.pfKey;
        instance.sessionID = request.sessionId.isEmpty() ? "hy_gameid" : request.sessionId;
        instance.sessionType = request.sessionType.isEmpty() ? "st_dummy" : request.sessionType;
        String zoneID = request.zoneId.isEmpty()? request.serverId : request.zoneId;
        if (!request.roleId.isEmpty()) {
            zoneID = zoneID + "_" + request.roleId;
        }
        instance.zoneID =zoneID;
        instance.goodsZoneID = request.goodsZoneId;

        BillingFlowParams.BillingFlowParamsExtra extra = new BillingFlowParams.BillingFlowParamsExtra();
        extra.setDrmInfo(request.getDrmInfo());
        extra.setGoodsZoneId(request.goodsZoneId);
        extra.setAppExtends(request.reserv);    //The video is passed into the aid field through the Reserv
        extra.setChannelExtras(request.extras);

        BillingFlowParams.Builder builder = new BillingFlowParams.Builder();
        builder.setCountry(request.country)
                .setCurrencyType(request.currency_type)
                .setPayChannel(request.getPayChannel())
                .setProductID(request.getAssignProductid())
                .setType(request.mType)
                .setExtra(extra);

        //month
        if(request instanceof CTIMonthRequest){
            CTIMonthRequest monthRequest = (CTIMonthRequest)request;
            builder.setIsAutoPay(monthRequest.autoPay)
                    .setServiceCode(monthRequest.serviceCode)
                    .setServiceName(monthRequest.serviceName)
                    .setBasePlanId(monthRequest.basePlanId)
                    .setGwOfferId(monthRequest.gw_offerId);
        }

        CTIPayNewAPI.singleton().pay(activity, builder.build(), callBack);
    }

    private void processPaymentData(JSONObject dataObject, CTIPayRequest request, Activity activity, ICTICallBack callBack) throws JSONException {
        String referenceId = dataObject.getString("reference_id");
        JSONObject transaction = dataObject.getJSONObject("transaction");
        JSONObject buyInfo = dataObject.getJSONObject("buy_info");
        JSONArray purchaseList = dataObject.getJSONArray("purchase_list");
        JSONObject purchase = purchaseList.getJSONObject(0);

        String productId = purchase.getString("product_id");
        String platformProductId = purchase.getString("channel_product_id");
        String provideAppId = purchase.getString("provide_app_id");
        String mType = purchase.getString("product_type");

        String country = transaction.getString("region");
        String currency = transaction.getString("currency");
        String payChannel = transaction.getString("pay_channel");
        String goodsZoneID = GlobalData.singleton().goodsZoneID;
        String serverId = buyInfo.getString("server_id");
        String roleId = buyInfo.getString("role_id");
        String zoneID = serverId;
        if (!roleId.isEmpty()) {
            zoneID = zoneID + "_" + roleId;
        }

        GlobalData instance = GlobalData.singleton();
        instance.preOrderId = referenceId;
        instance.unifiedProductId = productId;
        instance.serverId = zoneID;
        instance.zoneID = zoneID;

        BillingFlowParams.BillingFlowParamsExtra extra = new BillingFlowParams.BillingFlowParamsExtra();
        extra.setDrmInfo(request.getDrmInfo());
        extra.setGoodsZoneId(goodsZoneID);
        extra.setAppExtends(request.reserv);

        if (PAY_CHANNEL_OS_MIDASPAY.equals(payChannel)) {
            String payInfo = request.payInfo;
            if (payInfo != null) {
                String channelExtras = "pay_info=" + payInfo;
                extra.setChannelExtras(channelExtras);
            }
        } else {
            String requestExtras = request.getExtras();
            if (requestExtras != null) {
                extra.setChannelExtras(requestExtras);
            }
        }

        BillingFlowParams.Builder builder = new BillingFlowParams.Builder();
        builder.setCountry(country)
                .setCurrencyType(currency)
                .setPayChannel(payChannel)
                .setProductID(platformProductId)
                .setType(mType)
                .setExtra(extra);

        CTIPayNewAPI.singleton().pay(activity, builder.build(), callBack);
    }


    private void handleError(Exception e, ICTIProductInfoCallback callback) {
        CTILog.e(TAG, "getProductInfo() handleError: " + e.getMessage());
        try {
            JSONObject errorObj = new JSONObject();
            errorObj.put("retCode", 1);
            errorObj.put("innerCode", "-2030");
            errorObj.put("retMsg", "");
            errorObj.put("respInfo", "");
            callback.onProductInfoResp(errorObj.toString());
        } catch (Exception err) {
            err.printStackTrace();
        }
        e.printStackTrace();
    }

    public void net(String reqType, final ICTINetCallBack callBack) {
        CTILog.i(TAG, "net()");
        NetParams params = new NetParams.Builder()
                .setMpReqType(reqType)
                .build();

        CTIPayNewAPI.singleton().net(params, callBack);
    }

    public void net(CTIBaseRequest request, String reqType, final ICTINetCallBack callBack) {
        CTILog.i(TAG, "net()");
        CTILog.i(TAG, "drmInfo is: " + request.drmInfo);

        NetParams params = new NetParams.Builder()
                .setMpReqType(reqType)
                .build();
        params.drmInfo = request.drmInfo;

        CTIPayNewAPI.singleton().net(params, callBack);
    }

    public void dataPersistence(Activity activity, CTIBaseRequest request) {

        CTISPTools.putString(activity,
                "initIdcInfo", idcInfo);
        CTISPTools.putString(activity,
                "initEnv", env);
        CTISPTools.putString(activity,
                "initIdc", idc);

    }

    public void deinit() {
        CTILog.i(TAG, "deinit()");
        CTIPayNewAPI.singleton().dispose();
    }
}
